import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/color_palette/color_palette_service.dart';
import 'package:noeji/utils/color_utils.dart';

/// Custom theme extension for Noeji-specific colors
class NoejiColors extends ThemeExtension<NoejiColors> {
  final Color textPrimary;
  final Color textSecondary;
  final Color textDisabled;
  final Color divider;
  final Color border;
  final Color searchBarBackground;
  final Color cardBackground;
  final Color ideabookIndicatorBorder;
  final Color error;
  final Color chatMessageBackground;
  final Color chatMessageBorder;
  final Color modalOverlayBackground;
  final Color tooltipBackground;
  final Map<String, Color> ideabookColors;

  NoejiColors({
    required this.textPrimary,
    required this.textSecondary,
    required this.textDisabled,
    required this.divider,
    required this.border,
    required this.searchBarBackground,
    required this.cardBackground,
    required this.ideabookIndicatorBorder,
    required this.error,
    required this.chatMessageBackground,
    required this.chatMessageBorder,
    required this.modalOverlayBackground,
    required this.tooltipBackground,
    required this.ideabookColors,
  });

  @override
  ThemeExtension<NoejiColors> copyWith({
    Color? textPrimary,
    Color? textSecondary,
    Color? textDisabled,
    Color? divider,
    Color? border,
    Color? searchBarBackground,
    Color? cardBackground,
    Color? ideabookIndicatorBorder,
    Color? error,
    Color? chatMessageBackground,
    Color? chatMessageBorder,
    Color? modalOverlayBackground,
    Color? tooltipBackground,
    Map<String, Color>? ideabookColors,
  }) {
    return NoejiColors(
      textPrimary: textPrimary ?? this.textPrimary,
      textSecondary: textSecondary ?? this.textSecondary,
      textDisabled: textDisabled ?? this.textDisabled,
      divider: divider ?? this.divider,
      border: border ?? this.border,
      searchBarBackground: searchBarBackground ?? this.searchBarBackground,
      cardBackground: cardBackground ?? this.cardBackground,
      ideabookIndicatorBorder:
          ideabookIndicatorBorder ?? this.ideabookIndicatorBorder,
      error: error ?? this.error,
      chatMessageBackground:
          chatMessageBackground ?? this.chatMessageBackground,
      chatMessageBorder: chatMessageBorder ?? this.chatMessageBorder,
      modalOverlayBackground:
          modalOverlayBackground ?? this.modalOverlayBackground,
      tooltipBackground: tooltipBackground ?? this.tooltipBackground,
      ideabookColors: ideabookColors ?? this.ideabookColors,
    );
  }

  @override
  ThemeExtension<NoejiColors> lerp(
    ThemeExtension<NoejiColors>? other,
    double t,
  ) {
    if (other is! NoejiColors) {
      return this;
    }
    return NoejiColors(
      textPrimary: Color.lerp(textPrimary, other.textPrimary, t)!,
      textSecondary: Color.lerp(textSecondary, other.textSecondary, t)!,
      textDisabled: Color.lerp(textDisabled, other.textDisabled, t)!,
      divider: Color.lerp(divider, other.divider, t)!,
      border: Color.lerp(border, other.border, t)!,
      searchBarBackground:
          Color.lerp(searchBarBackground, other.searchBarBackground, t)!,
      cardBackground: Color.lerp(cardBackground, other.cardBackground, t)!,
      ideabookIndicatorBorder:
          Color.lerp(
            ideabookIndicatorBorder,
            other.ideabookIndicatorBorder,
            t,
          )!,
      error: Color.lerp(error, other.error, t)!,
      chatMessageBackground:
          Color.lerp(chatMessageBackground, other.chatMessageBackground, t)!,
      chatMessageBorder:
          Color.lerp(chatMessageBorder, other.chatMessageBorder, t)!,
      modalOverlayBackground:
          Color.lerp(modalOverlayBackground, other.modalOverlayBackground, t)!,
      tooltipBackground:
          Color.lerp(tooltipBackground, other.tooltipBackground, t)!,
      ideabookColors:
          ideabookColors, // Can't lerp a map, so just return this one
    );
  }

  /// Light theme colors
  static NoejiColors light(ColorPalette colorPalette) => NoejiColors(
    textPrimary: const Color(0xFF333333),
    textSecondary: const Color(0xFF666666),
    textDisabled: const Color(0xFFAAAAAA), // Light gray for disabled text
    divider: const Color(0xFFDDDDDD), // Light gray for less obvious dividers
    border: const Color(0xFF333333),
    searchBarBackground: Colors.white,
    cardBackground: Colors.white,
    ideabookIndicatorBorder: const Color(0xFF333333),
    error: Colors.red,
    chatMessageBackground: const Color(0xFFEEEEEE), // Light gray-ish background
    chatMessageBorder: const Color(0xFF333333), // Black-ish border
    modalOverlayBackground: const Color(0xDDFFFFFF), // Semi-transparent white
    tooltipBackground: Colors.white, // White background for tooltips
    ideabookColors: {
      'none': colorPalette.noneColor,
      'red': colorPalette.redColor,
      'orange': colorPalette.orangeColor,
      'yellow': colorPalette.yellowColor,
      'green': colorPalette.greenColor,
      'blue': colorPalette.blueColor,
      'purple': colorPalette.purpleColor,
    },
  );

  /// Dark theme colors
  static NoejiColors dark(ColorPalette colorPalette) => NoejiColors(
    textPrimary: Colors.white,
    textSecondary: const Color(0xFFCCCCCC),
    textDisabled: const Color(0xFF777777), // Dark gray for disabled text
    divider: const Color(0xFF444444), // Darker gray for less obvious dividers
    border: Colors.white,
    searchBarBackground: const Color(0xFF333333),
    cardBackground: const Color(0xFF333333),
    ideabookIndicatorBorder: Colors.white,
    error: const Color(0xFFFF453A),
    chatMessageBackground: const Color(0xFF444444), // Gray-ish background
    chatMessageBorder: Colors.white, // White-ish border
    modalOverlayBackground: const Color(
      0xDD222222,
    ), // Semi-transparent dark gray
    tooltipBackground: const Color(0xFF333333), // Dark background for tooltips
    ideabookColors: {
      'none': colorPalette.noneColor,
      'red': colorPalette.redColor,
      'orange': colorPalette.orangeColor,
      'yellow': colorPalette.yellowColor,
      'green': colorPalette.greenColor,
      'blue': colorPalette.blueColor,
      'purple': colorPalette.purpleColor,
    },
  );
}

/// Custom theme extension for Noeji-specific text styles
class NoejiTextStyles extends ThemeExtension<NoejiTextStyles> {
  final TextStyle titleLarge;
  final TextStyle titleMedium;
  final TextStyle titleSmall;
  final TextStyle bodyLarge;
  final TextStyle bodyMedium;
  final TextStyle bodySmall;
  final TextStyle buttonText;
  final TextStyle ideabookName;
  final TextStyle searchHint;

  NoejiTextStyles({
    required this.titleLarge,
    required this.titleMedium,
    required this.titleSmall,
    required this.bodyLarge,
    required this.bodyMedium,
    required this.bodySmall,
    required this.buttonText,
    required this.ideabookName,
    required this.searchHint,
  });

  @override
  ThemeExtension<NoejiTextStyles> copyWith({
    TextStyle? titleLarge,
    TextStyle? titleMedium,
    TextStyle? titleSmall,
    TextStyle? bodyLarge,
    TextStyle? bodyMedium,
    TextStyle? bodySmall,
    TextStyle? buttonText,
    TextStyle? ideabookName,
    TextStyle? searchHint,
  }) {
    return NoejiTextStyles(
      titleLarge: titleLarge ?? this.titleLarge,
      titleMedium: titleMedium ?? this.titleMedium,
      titleSmall: titleSmall ?? this.titleSmall,
      bodyLarge: bodyLarge ?? this.bodyLarge,
      bodyMedium: bodyMedium ?? this.bodyMedium,
      bodySmall: bodySmall ?? this.bodySmall,
      buttonText: buttonText ?? this.buttonText,
      ideabookName: ideabookName ?? this.ideabookName,
      searchHint: searchHint ?? this.searchHint,
    );
  }

  @override
  ThemeExtension<NoejiTextStyles> lerp(
    ThemeExtension<NoejiTextStyles>? other,
    double t,
  ) {
    if (other is! NoejiTextStyles) {
      return this;
    }
    return NoejiTextStyles(
      titleLarge: TextStyle.lerp(titleLarge, other.titleLarge, t)!,
      titleMedium: TextStyle.lerp(titleMedium, other.titleMedium, t)!,
      titleSmall: TextStyle.lerp(titleSmall, other.titleSmall, t)!,
      bodyLarge: TextStyle.lerp(bodyLarge, other.bodyLarge, t)!,
      bodyMedium: TextStyle.lerp(bodyMedium, other.bodyMedium, t)!,
      bodySmall: TextStyle.lerp(bodySmall, other.bodySmall, t)!,
      buttonText: TextStyle.lerp(buttonText, other.buttonText, t)!,
      ideabookName: TextStyle.lerp(ideabookName, other.ideabookName, t)!,
      searchHint: TextStyle.lerp(searchHint, other.searchHint, t)!,
    );
  }

  /// Create text styles for light theme
  static NoejiTextStyles light(NoejiColors colors) {
    return NoejiTextStyles(
      titleLarge: GoogleFonts.pacifico(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      titleMedium: GoogleFonts.pacifico(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      titleSmall: GoogleFonts.pacifico(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      bodyLarge: GoogleFonts.afacad(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      bodyMedium: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colors.textPrimary,
      ),
      bodySmall: GoogleFonts.afacad(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colors.textSecondary,
      ),
      buttonText: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      ideabookName: GoogleFonts.afacad(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      searchHint: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colors.textSecondary,
      ),
    );
  }

  /// Create text styles for dark theme
  static NoejiTextStyles dark(NoejiColors colors) {
    return NoejiTextStyles(
      titleLarge: GoogleFonts.pacifico(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      titleMedium: GoogleFonts.pacifico(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      titleSmall: GoogleFonts.pacifico(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: colors.textPrimary,
      ),
      bodyLarge: GoogleFonts.afacad(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      bodyMedium: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colors.textPrimary,
      ),
      bodySmall: GoogleFonts.afacad(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colors.textSecondary,
      ),
      buttonText: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      ideabookName: GoogleFonts.afacad(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: colors.textPrimary,
      ),
      searchHint: GoogleFonts.afacad(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: colors.textSecondary,
      ),
    );
  }
}

/// Helper class to access theme extensions easily
class NoejiTheme {
  /// Get the NoejiColors extension from the current theme
  static NoejiColors colorsOf(BuildContext context) {
    return Theme.of(context).extension<NoejiColors>()!;
  }

  /// Get the NoejiTextStyles extension from the current theme
  static NoejiTextStyles textStylesOf(BuildContext context) {
    return Theme.of(context).extension<NoejiTextStyles>()!;
  }

  /// Get the color for an ideabook based on its color index
  static Color getIdeabookColor(BuildContext context, int colorIndex) {
    final colors = colorsOf(context).ideabookColors;
    switch (colorIndex) {
      case 0:
        return colors['none']!;
      case 1:
        return colors['red']!;
      case 2:
        return colors['green']!;
      case 3:
        return colors['blue']!;
      case 4:
        return colors['yellow']!;
      case 5:
        return colors['purple']!;
      case 6:
        return colors['orange']!;
      default:
        return colors['none']!;
    }
  }

  /// Get the color for an ideabook based on its color index using a color palette directly
  /// This is useful when you have a ColorPalette instance but no BuildContext
  static Color getIdeabookColorFromPalette(
    ColorPalette palette,
    int colorIndex,
  ) {
    switch (colorIndex) {
      case 0:
        return palette.noneColor;
      case 1:
        return palette.redColor;
      case 2:
        return palette.greenColor;
      case 3:
        return palette.blueColor;
      case 4:
        return palette.yellowColor;
      case 5:
        return palette.purpleColor;
      case 6:
        return palette.orangeColor;
      default:
        return palette.noneColor;
    }
  }

  /// Get the appropriate text color for a given ideabook color
  /// Uses contrast-based algorithm to determine optimal text color (black or white)
  /// for maximum readability against the background color
  static Color getTextColorForIdeabookColor(
    BuildContext context,
    int colorIndex,
  ) {
    // Get the actual background color for this ideabook
    final backgroundColor = getIdeabookColor(context, colorIndex);

    // Use the contrast-based algorithm to determine optimal text color
    return ColorUtils.getContrastingTextColor(backgroundColor);
  }

  /// Get the appropriate text color for a given ideabook color using a color palette directly
  /// This is useful when you have a ColorPalette instance but no BuildContext
  /// Uses contrast-based algorithm to determine optimal text color (black or white)
  /// for maximum readability against the background color
  static Color getTextColorForIdeabookColorFromPalette(
    ColorPalette palette,
    int colorIndex,
  ) {
    // Get the actual background color for this ideabook
    final backgroundColor = getIdeabookColorFromPalette(palette, colorIndex);

    // Use the contrast-based algorithm to determine optimal text color
    return ColorUtils.getContrastingTextColor(backgroundColor);
  }

  /// Get the appropriate text color for SnackBar based on theme and background color
  /// If backgroundColor is provided, returns contrasting text color
  /// Otherwise, returns white for light theme (dark SnackBar) and black for dark theme (light SnackBar)
  static Color getSnackBarTextColor(
    BuildContext context, {
    Color? backgroundColor,
  }) {
    // If a specific background color is provided, use the contrast-based algorithm
    if (backgroundColor != null) {
      return ColorUtils.getContrastingTextColor(backgroundColor);
    }

    // Default SnackBar in Flutter has dark background in light theme and light background in dark theme
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    // For dark theme, SnackBar is light, so use dark text
    // For light theme, SnackBar is dark, so use light text
    return isDarkTheme ? const Color(0xFF333333) : Colors.white;
  }
}
