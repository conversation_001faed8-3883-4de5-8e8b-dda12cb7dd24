import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/theme/theme_storage.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/services/color_palette/color_palette_provider.dart';

/// Theme mode state notifier
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  /// Constructor
  ThemeModeNotifier() : super(ThemeMode.system) {
    // Load saved theme when initialized
    _loadSavedTheme();
  }

  /// Toggle between light and dark theme
  /// When system theme is active, toggle based on current effective theme
  void toggleTheme() {
    final effectiveTheme = getEffectiveTheme();
    final newTheme =
        effectiveTheme == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    state = newTheme;
    // Save the new theme preference
    ThemeStorage.saveThemeMode(newTheme);
  }

  /// Set theme mode explicitly
  void setThemeMode(ThemeMode mode) {
    state = mode;
    // Save the new theme preference
    ThemeStorage.saveThemeMode(mode);
  }

  /// Load the saved theme from storage
  Future<void> _loadSavedTheme() async {
    final savedTheme = await ThemeStorage.loadThemeMode();
    state = savedTheme;
  }

  /// Get the effective theme mode (resolves system theme to light/dark)
  ThemeMode getEffectiveTheme() {
    if (state == ThemeMode.system) {
      // Get system brightness
      final platformDispatcher = WidgetsBinding.instance.platformDispatcher;
      final brightness = platformDispatcher.platformBrightness;
      return brightness == Brightness.dark ? ThemeMode.dark : ThemeMode.light;
    }
    return state;
  }
}

/// Provider for theme mode
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((
  ref,
) {
  return ThemeModeNotifier();
});

/// Provider for effective theme mode (resolves system theme to light/dark)
final effectiveThemeModeProvider = Provider<ThemeMode>((ref) {
  final themeNotifier = ref.watch(themeModeProvider.notifier);
  // Watch the theme mode to trigger rebuilds when it changes
  ref.watch(themeModeProvider);
  return themeNotifier.getEffectiveTheme();
});

/// Provider for light theme data
final lightThemeProvider = Provider<ThemeData>((ref) {
  final colorPalette = ref.watch(currentColorPaletteProvider);
  final noejiColors = NoejiColors.light(colorPalette);
  final noejiTextStyles = NoejiTextStyles.light(noejiColors);

  return ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      elevation: 0,
      iconTheme: IconThemeData(color: noejiColors.textPrimary),
      titleTextStyle: noejiTextStyles.titleMedium,
    ),
    iconTheme: IconThemeData(color: noejiColors.textPrimary),
    dividerTheme: DividerThemeData(
      color: noejiColors.divider,
      thickness: 0.5, // Reduced thickness for less obvious dividers
      space: 1,
    ),
    cardTheme: CardThemeData(
      color: noejiColors.cardBackground,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
        side: BorderSide(color: noejiColors.border, width: 1),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        side: BorderSide(color: noejiColors.border, width: 1),
        foregroundColor: noejiColors.textPrimary,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      ),
    ),
    extensions: [noejiColors, noejiTextStyles],
  );
});

/// Provider for dark theme data
final darkThemeProvider = Provider<ThemeData>((ref) {
  final colorPalette = ref.watch(currentColorPaletteProvider);
  final noejiColors = NoejiColors.dark(colorPalette);
  final noejiTextStyles = NoejiTextStyles.dark(noejiColors);

  return ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: const Color(0xFF121212),
    appBarTheme: AppBarTheme(
      backgroundColor: const Color(0xFF121212),
      elevation: 0,
      iconTheme: IconThemeData(color: noejiColors.textPrimary),
      titleTextStyle: noejiTextStyles.titleMedium,
    ),
    iconTheme: IconThemeData(color: noejiColors.textPrimary),
    dividerTheme: DividerThemeData(
      color: noejiColors.divider,
      thickness: 0.5, // Reduced thickness for less obvious dividers
      space: 1,
    ),
    cardTheme: CardThemeData(
      color: noejiColors.cardBackground,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
        side: BorderSide(color: noejiColors.border, width: 1),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        side: BorderSide(color: noejiColors.border, width: 1),
        foregroundColor: noejiColors.textPrimary,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      ),
    ),
    extensions: [noejiColors, noejiTextStyles],
  );
});
