import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/ui/screens/ideabooks_list_screen.dart';
import 'package:noeji/ui/screens/terms_of_service_screen.dart';
import 'package:noeji/ui/theme/theme_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';

/// Welcome screen for the app
/// This is the first screen shown to users when they open the app
class WelcomeScreen extends ConsumerStatefulWidget {
  /// Constructor
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen>
    with SingleTickerProviderStateMixin {
  // Animation controller for the fade-in effect
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800), // Adjust duration as needed
    );

    // Create fade-in animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // Start the animation when the widget is built
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveThemeMode = ref.watch(effectiveThemeModeProvider);
    // Get the scaffold background color from the current theme
    final scaffoldBackgroundColor = Theme.of(context).scaffoldBackgroundColor;

    return Scaffold(
      // Explicitly set the background color to ensure it's visible before the fade-in
      backgroundColor: scaffoldBackgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // This ensures the background is visible and not affected by the fade animation
            Container(color: scaffoldBackgroundColor),

            // Theme toggle button in top right corner
            Positioned(
              top: 16,
              right: 16,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: IconButton(
                  icon: Icon(
                    // Show moon icon in light mode, sun icon in dark mode
                    // Use effective theme to determine icon when system theme is active
                    effectiveThemeMode == ThemeMode.light
                        ? Icons.nightlight_round
                        : Icons.wb_sunny,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                    size: 28,
                  ),
                  onPressed: () {
                    // Toggle theme
                    ref.read(themeModeProvider.notifier).toggleTheme();
                  },
                ),
              ),
            ),

            // Fade in the content
            FadeTransition(
              opacity: _fadeAnimation,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  children: [
                    // Expanded to push logo and slogan to middle
                    const Expanded(flex: 1, child: SizedBox()),

                    // Logo and slogan in the middle
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Logo
                        Center(
                          child: SvgPicture.asset(
                            'assets/images/noeji_logo_v3.svg',
                            height: 64,
                            colorFilter: ColorFilter.mode(
                              NoejiTheme.colorsOf(context).textPrimary,
                              BlendMode.srcIn,
                            ),
                            fit: BoxFit.contain,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Slogan
                        Center(
                          child: Text(
                            'Ideas Spoken. Stories Born',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.afacad(
                              fontSize: 20,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Expanded to push sign-in to bottom
                    const Expanded(flex: 1, child: SizedBox()),

                    // Sign in section at the bottom
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Sign in to Continue text
                        Center(
                          child: Text(
                            'Sign in to Continue',
                            style: GoogleFonts.afacad(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: NoejiTheme.colorsOf(context).textPrimary,
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Google Sign-in button
                        _buildSignInButton(
                          context,
                          'Sign in with Google',
                          'assets/images/social/google.svg',
                          onPressed: () => _handleGoogleSignIn(context, ref),
                        ),

                        const SizedBox(height: 16),

                        // Apple Sign-in button
                        _buildSignInButton(
                          context,
                          'Sign in with Apple',
                          'assets/images/social/apple.svg',
                          onPressed: () => _handleAppleSignIn(context, ref),
                        ),

                        // Terms of Service agreement
                        const SizedBox(height: 16),

                        // Terms of Service text
                        GestureDetector(
                          onTap: () => _showTermsOfService(context),
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'By signing in, you agree to our ',
                                  style: GoogleFonts.afacad(
                                    fontSize: 12,
                                    color:
                                        NoejiTheme.colorsOf(
                                          context,
                                        ).textSecondary,
                                  ),
                                ),
                                TextSpan(
                                  text: 'Terms of Service',
                                  style: GoogleFonts.afacad(
                                    fontSize: 12,
                                    color:
                                        NoejiTheme.colorsOf(
                                          context,
                                        ).textSecondary,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        // Bottom padding
                        const SizedBox(height: 32),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a sign-in button with text and SVG icon
  Widget _buildSignInButton(
    BuildContext context,
    String text,
    String svgPath, {
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1, // Reduced from 2pt to 1pt as requested
        ),
        // Add a subtle background color that works in both light and dark modes
        color:
            Theme.of(context).brightness == Brightness.light
                ? Colors.white
                : const Color(0xFF1E1E1E),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  svgPath,
                  width: 24,
                  height: 24,
                  // For X and Apple icons, we need to adjust the color in dark mode
                  colorFilter:
                      (svgPath.contains('x.svg') ||
                                  svgPath.contains('apple')) &&
                              Theme.of(context).brightness == Brightness.dark
                          ? const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          )
                          : null,
                  // Ensure proper fit for all logos
                  fit: BoxFit.contain,
                ),
                const SizedBox(width: 12),
                Text(
                  text,
                  style: GoogleFonts.afacad(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Handle Google sign-in
  Future<void> _handleGoogleSignIn(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading indicator
      _showLoadingDialog(context);

      Logger.debug('Sign in button pressed - starting Google sign-in flow');

      // Get the auth service
      final authService = ref.read(authServiceProvider);

      // Sign in with Google
      final userCredential = await authService.signInWithGoogle();

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // If sign-in was successful, navigate to ideabooks list screen
      if (userCredential != null && context.mounted) {
        Logger.debug('Sign-in successful, navigating to ideabooks list screen');

        // Navigate to ideabooks list screen with a completely new navigation stack
        // Use pushReplacement instead of pushNamedAndRemoveUntil to avoid routing issues
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const IdeabooksListScreen()),
        );
      } else if (userCredential == null && context.mounted) {
        // User canceled the sign-in
        Logger.debug('Sign-in canceled by user');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign-in canceled', style: GoogleFonts.afacad()),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      Logger.error('Error signing in with Google', e);

      // Format error message for display
      String errorMessage = 'Error signing in';
      if (e.toString().contains('network')) {
        errorMessage = 'Network error. Check your connection.';
      } else if (e.toString().contains('canceled')) {
        errorMessage = 'Sign-in canceled.';
      } else if (ErrorUtils.isPermissionError(e)) {
        // Use generic message for permission errors including App Check failures
        errorMessage = ErrorUtils.permissionDeniedMessage;
      } else {
        // For other errors, show a generic message instead of the full error
        errorMessage = 'Error signing in. Please try again later.';
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage, style: GoogleFonts.afacad()),
            backgroundColor: Colors.red[300],
          ),
        );
      }
    }
  }

  /// Handle Apple sign-in
  Future<void> _handleAppleSignIn(BuildContext context, WidgetRef ref) async {
    try {
      // Show loading indicator
      _showLoadingDialog(context);

      Logger.debug('Apple sign in button pressed - starting Apple sign-in flow');

      // Get the auth service
      final authService = ref.read(authServiceProvider);

      // Sign in with Apple
      final userCredential = await authService.signInWithApple();

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // If sign-in was successful, navigate to ideabooks list screen
      if (userCredential != null && context.mounted) {
        Logger.debug('Apple sign-in successful, navigating to ideabooks list screen');

        // Navigate to ideabooks list screen with a completely new navigation stack
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const IdeabooksListScreen()),
        );
      } else if (userCredential == null && context.mounted) {
        // User canceled the sign-in
        Logger.debug('Apple sign-in canceled by user');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sign-in canceled', style: GoogleFonts.afacad()),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      Logger.error('Error signing in with Apple', e);

      // Format error message for display
      String errorMessage = 'Error signing in';
      if (e.toString().contains('Apple Sign-In Error')) {
        errorMessage = e.toString().replaceFirst('Exception: ', '');
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Check your connection.';
      } else if (e.toString().contains('canceled')) {
        errorMessage = 'Sign-in canceled.';
      } else if (ErrorUtils.isPermissionError(e)) {
        // Use generic message for permission errors including App Check failures
        errorMessage = ErrorUtils.permissionDeniedMessage;
      } else {
        // For other errors, show a generic message instead of the full error
        errorMessage = 'Error signing in. Please try again later.';
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage, style: GoogleFonts.afacad()),
            backgroundColor: Colors.red[300],
          ),
        );
      }
    }
  }

  /// Show a loading dialog
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );
  }

  /// Show the Terms of Service screen
  void _showTermsOfService(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TermsOfServiceScreen()),
    );
  }
}
