import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/models/ideabook.dart';
import 'package:noeji/ui/providers/passcode_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Screen for entering, confirming, and validating passcodes
class PasscodeScreen extends ConsumerStatefulWidget {
  /// The ideabook being locked/unlocked, if applicable
  final Ideabook? ideabook;

  /// Callback when passcode validation is successful
  final Function? onSuccess;

  /// Callback when passcode entry is cancelled
  final Function? onCancel;

  /// Whether this is for unlocking an ideabook (vs. locking)
  final bool isUnlocking;

  /// Whether this is for locking an ideabook
  final bool isLocking;

  /// Whether this is for changing the passcode
  final bool isChangingPasscode;

  /// Whether this is for accessing an ideabook detail page
  final bool isAccessingDetail;

  /// Constructor
  const PasscodeScreen({
    super.key,
    this.ideabook,
    this.onSuccess,
    this.onCancel,
    this.isUnlocking = false,
    this.isLocking = false,
    this.isChangingPasscode = false,
    this.isAccessingDetail = false,
  });

  @override
  ConsumerState<PasscodeScreen> createState() => _PasscodeScreenState();
}

class _PasscodeScreenState extends ConsumerState<PasscodeScreen> {
  /// Flag to prevent restarting validation after successful completion
  bool _hasCompletedSuccessfully = false;

  @override
  void initState() {
    super.initState();
    Logger.debug('PasscodeScreen: initState called');
    Logger.debug(
      'PasscodeScreen: isLocking=${widget.isLocking}, isUnlocking=${widget.isUnlocking}, isAccessingDetail=${widget.isAccessingDetail}, isChangingPasscode=${widget.isChangingPasscode}',
    );

    // Reset the passcode state when the screen is first initialized
    // This ensures a fresh state each time the screen is shown
    Future.microtask(() {
      Logger.debug('PasscodeScreen: Resetting passcode state in initState');
      ref.read(passcodeProvider.notifier).reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    final passcodeState = ref.watch(passcodeProvider);
    final passcodeNotifier = ref.read(passcodeProvider.notifier);

    // Use the cached provider for synchronous access
    final isPasscodeSetCached = ref.watch(passcodeSetCachedProvider).isSet;

    // Also watch the async provider to ensure we have the latest data
    // This will trigger updates to the cached state if needed
    ref.watch(passcodeSetProvider);

    return Scaffold(
      appBar: AppBar(
        title: null,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            // Reset state and call cancel callback
            passcodeNotifier.reset();
            if (widget.onCancel != null) {
              widget.onCancel!();
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
      ),
      body: Builder(
        builder: (context) {
          // Use the cached value for immediate UI rendering
          final isSet = isPasscodeSetCached;
          Logger.debug('PasscodeScreen: isPasscodeSet (cached) = $isSet');

          Logger.debug(
            'PasscodeScreen: Determining mode - isSet: $isSet, isValidating: ${passcodeState.isValidating}, isSettingNew: ${passcodeState.isSettingNew}, isChangingPasscode: ${passcodeState.isChangingPasscode}, hasCompleted: $_hasCompletedSuccessfully',
          );

          // Handle unlocking or accessing detail case first - these should always take precedence
          if ((widget.isUnlocking || widget.isAccessingDetail) &&
              !_hasCompletedSuccessfully) {
            // If we're unlocking/accessing detail, always force validation mode
            // We should only reach this point if a passcode is set (checked in the calling code)
            Logger.debug(
              'PasscodeScreen: Starting validation mode for unlocking/accessing detail',
            );
            if (!passcodeState.isValidating) {
              Logger.debug(
                'PasscodeScreen: State not in validation mode, switching to validation',
              );
              Future.microtask(
                () => passcodeNotifier.startValidatingPasscode(),
              );
            } else {
              Logger.debug('PasscodeScreen: Already in validation mode');
            }
          } else if ((widget.isUnlocking || widget.isAccessingDetail) &&
              _hasCompletedSuccessfully) {
            Logger.debug(
              'PasscodeScreen: Skipping validation restart - operation already completed successfully',
            );
          }
          // If not in unlocking/accessing detail mode, initialize the correct mode if needed
          else if (!passcodeState.isValidating &&
              !passcodeState.isSettingNew &&
              !passcodeState.isChangingPasscode &&
              !_hasCompletedSuccessfully) {
            Logger.debug(
              'PasscodeScreen: No mode set, determining correct mode',
            );
            if (widget.isChangingPasscode) {
              // If we're changing the passcode, start in changing mode
              Logger.debug('PasscodeScreen: Starting changing mode');
              Future.microtask(() => passcodeNotifier.startChangingPasscode());
            } else if (isSet && widget.isLocking) {
              // If passcode is already set and we're locking, we need to validate it
              Logger.debug(
                'PasscodeScreen: Starting validation mode for locking with existing passcode',
              );
              Future.microtask(
                () => passcodeNotifier.startValidatingPasscode(),
              );
            } else if (isSet) {
              // If passcode is already set for other operations, we need to validate it
              Logger.debug(
                'PasscodeScreen: Starting validation mode for existing passcode',
              );
              Future.microtask(
                () => passcodeNotifier.startValidatingPasscode(),
              );
            } else {
              // If no passcode is set, we need to set a new one
              Logger.debug(
                'PasscodeScreen: Starting setup mode for new passcode',
              );
              Future.microtask(() => passcodeNotifier.startSettingPasscode());
            }
          } else if (!passcodeState.isValidating &&
              !passcodeState.isSettingNew &&
              !passcodeState.isChangingPasscode &&
              _hasCompletedSuccessfully) {
            Logger.debug(
              'PasscodeScreen: Skipping mode initialization - operation already completed successfully',
            );
          } else {
            Logger.debug('PasscodeScreen: Mode already set, no action needed');
          }

          return Column(
            children: [
              // Title based on current state
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Text(
                  _getTitleText(passcodeState),
                  style: GoogleFonts.afacad(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // Passcode display
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(6, (index) {
                    final currentPasscode =
                        passcodeState.isConfirming
                            ? passcodeState.passcodeToConfirm
                            : passcodeState.currentPasscode;
                    final isFilled = index < currentPasscode.length;

                    return Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            isFilled
                                ? NoejiTheme.colorsOf(context).textPrimary
                                : Colors.transparent,
                        border: Border.all(
                          color: NoejiTheme.colorsOf(context).border,
                          width: 1,
                        ),
                      ),
                    );
                  }),
                ),
              ),

              // Error message
              if (passcodeState.errorMessage != null)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    passcodeState.errorMessage!,
                    style: TextStyle(
                      color: NoejiTheme.colorsOf(context).textPrimary,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

              // Spacer
              const Spacer(),

              // Numpad
              _buildNumpad(context, ref),

              // Bottom padding
              const SizedBox(height: 40),
            ],
          );
        },
      ),
    );
  }

  /// Build the numpad widget
  Widget _buildNumpad(BuildContext context, WidgetRef ref) {
    final passcodeState = ref.watch(passcodeProvider);
    final passcodeNotifier = ref.read(passcodeProvider.notifier);

    // Handle digit press
    void onDigitPressed(String digit) async {
      // Prevent any input after successful completion
      if (_hasCompletedSuccessfully) {
        Logger.debug(
          'PasscodeScreen: Ignoring digit press - operation already completed successfully',
        );
        return;
      }

      Logger.debug('PasscodeScreen: Digit pressed: $digit');

      // Get current passcode length before adding digit
      final currentPasscode =
          passcodeState.isConfirming
              ? passcodeState.passcodeToConfirm
              : passcodeState.currentPasscode;

      Logger.debug(
        'PasscodeScreen: Current passcode length before adding digit: ${currentPasscode.length}',
      );
      Logger.debug(
        'PasscodeScreen: State - isValidating: ${passcodeState.isValidating}, isSettingNew: ${passcodeState.isSettingNew}, isConfirming: ${passcodeState.isConfirming}, isChangingPasscode: ${passcodeState.isChangingPasscode}',
      );

      // Add the digit
      passcodeNotifier.addDigit(digit);

      // Get updated passcode length after adding digit
      final updatedState = ref.read(passcodeProvider);
      final updatedPasscode =
          updatedState.isConfirming
              ? updatedState.passcodeToConfirm
              : updatedState.currentPasscode;

      Logger.debug(
        'PasscodeScreen: Updated passcode length after adding digit: ${updatedPasscode.length}',
      );

      // Auto-proceed when passcode is complete (6 digits)
      if (updatedPasscode.length == 6) {
        Logger.debug(
          'PasscodeScreen: Passcode complete (6 digits), proceeding with validation/save',
        );

        // Wait a moment for visual feedback
        await Future.delayed(const Duration(milliseconds: 200));

        if (passcodeState.isValidating) {
          Logger.debug('PasscodeScreen: Validating passcode');
          // Validate the passcode
          final isValid = await passcodeNotifier.validatePasscode();
          Logger.debug('PasscodeScreen: Validation result: $isValid');

          if (isValid) {
            Logger.debug(
              'PasscodeScreen: Passcode validated successfully, calling onSuccess callback',
            );
            Logger.debug(
              'PasscodeScreen: isLocking=${widget.isLocking}, isUnlocking=${widget.isUnlocking}, isAccessingDetail=${widget.isAccessingDetail}',
            );

            // Set the completion flag to prevent restarting validation
            Logger.debug(
              'PasscodeScreen: Setting completion flag to prevent restart loop',
            );
            _hasCompletedSuccessfully = true;

            // Reset the passcode state
            passcodeNotifier.reset();

            if (widget.onSuccess != null) {
              Logger.debug(
                'PasscodeScreen: onSuccess callback is not null, calling it',
              );
              widget.onSuccess!();
            } else {
              // Store a local reference to the context
              final currentContext = context;
              // Use a microtask to ensure we're not in an async gap
              Logger.debug(
                'PasscodeScreen: onSuccess callback is null, popping screen',
              );
              Future.microtask(() {
                if (currentContext.mounted) {
                  Navigator.of(currentContext).pop(true);
                }
              });
            }
          }
        } else if (passcodeState.isChangingPasscode &&
            passcodeState.isEnteringOldPasscode) {
          Logger.debug('PasscodeScreen: Validating old passcode for change');
          // Validate the old passcode
          final isValid = await passcodeNotifier.validateOldPasscode();
          Logger.debug(
            'PasscodeScreen: Old passcode validation result: $isValid',
          );
          if (isValid) {
            // Old passcode is valid, now enter the new passcode
            // The state is already updated in validateOldPasscode()
            Logger.debug(
              'PasscodeScreen: Old passcode valid, moving to new passcode entry',
            );
          }
        } else if (passcodeState.isChangingPasscode &&
            passcodeState.isEnteringNewPasscode) {
          Logger.debug(
            'PasscodeScreen: Moving to confirmation step for new passcode',
          );
          // Move to confirmation step for the new passcode
          passcodeNotifier.moveToConfirmation();
        } else if (passcodeState.isChangingPasscode &&
            passcodeState.isConfirming) {
          Logger.debug('PasscodeScreen: Saving changed passcode');
          // Save the changed passcode
          final success = await passcodeNotifier.saveChangedPasscode();
          Logger.debug(
            'PasscodeScreen: Changed passcode save result: $success',
          );

          if (success) {
            // Set the completion flag to prevent restarting validation
            _hasCompletedSuccessfully = true;

            // Reset the passcode state
            passcodeNotifier.reset();

            // Handle success (show message and navigate)
            _handlePasscodeChangeSuccess();
          }
        } else if (passcodeState.isSettingNew && !passcodeState.isConfirming) {
          Logger.debug(
            'PasscodeScreen: Moving to confirmation step for new passcode',
          );
          // Move to confirmation step
          passcodeNotifier.moveToConfirmation();
        } else if (passcodeState.isSettingNew && passcodeState.isConfirming) {
          Logger.debug('PasscodeScreen: Saving new passcode');
          // Save the passcode
          final success = await passcodeNotifier.savePasscode();
          Logger.debug('PasscodeScreen: New passcode save result: $success');

          if (success) {
            Logger.debug(
              'PasscodeScreen: Passcode saved successfully, calling onSuccess callback',
            );
            Logger.debug(
              'PasscodeScreen: isLocking=${widget.isLocking}, isUnlocking=${widget.isUnlocking}',
            );

            // Set the completion flag to prevent restarting validation
            _hasCompletedSuccessfully = true;

            // Reset the passcode state
            passcodeNotifier.reset();

            if (widget.onSuccess != null) {
              Logger.debug(
                'PasscodeScreen: onSuccess callback is not null, calling it',
              );
              widget.onSuccess!();
            } else {
              // Store a local reference to the context
              final currentContext = context;
              // Use a microtask to ensure we're not in an async gap
              Logger.debug(
                'PasscodeScreen: onSuccess callback is null, popping screen',
              );
              Future.microtask(() {
                if (currentContext.mounted) {
                  Navigator.of(currentContext).pop(true);
                }
              });
            }
          }
        }
      }
    }

    return Column(
      children: [
        // Row 1: 1, 2, 3
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildDigitButton(context, '1', onDigitPressed),
            _buildDigitButton(context, '2', onDigitPressed),
            _buildDigitButton(context, '3', onDigitPressed),
          ],
        ),
        const SizedBox(height: 16),

        // Row 2: 4, 5, 6
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildDigitButton(context, '4', onDigitPressed),
            _buildDigitButton(context, '5', onDigitPressed),
            _buildDigitButton(context, '6', onDigitPressed),
          ],
        ),
        const SizedBox(height: 16),

        // Row 3: 7, 8, 9
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildDigitButton(context, '7', onDigitPressed),
            _buildDigitButton(context, '8', onDigitPressed),
            _buildDigitButton(context, '9', onDigitPressed),
          ],
        ),
        const SizedBox(height: 16),

        // Row 4: Delete, 0, Empty
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Delete button
            SizedBox(
              width: 80,
              height: 80,
              child: IconButton(
                icon: Icon(
                  Icons.backspace,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: () {
                  // Prevent any input after successful completion
                  if (_hasCompletedSuccessfully) {
                    Logger.debug(
                      'PasscodeScreen: Ignoring delete press - operation already completed successfully',
                    );
                    return;
                  }
                  passcodeNotifier.removeLastDigit();
                },
              ),
            ),

            // 0 button
            _buildDigitButton(context, '0', onDigitPressed),

            // Empty space for symmetry
            const SizedBox(width: 80, height: 80),
          ],
        ),
      ],
    );
  }

  /// Handle successful passcode change
  void _handlePasscodeChangeSuccess() {
    // Set the completion flag to prevent restarting validation
    _hasCompletedSuccessfully = true;

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Passcode changed successfully'),
        duration: Duration(seconds: 2),
      ),
    );

    // Call success callback or pop
    if (widget.onSuccess != null) {
      widget.onSuccess!();
    } else {
      Navigator.of(context).pop(true);
    }
  }

  /// Get the title text based on the current state
  String _getTitleText(PasscodeState state) {
    // Handle passcode change flow
    if (state.isChangingPasscode) {
      if (state.isEnteringOldPasscode) {
        return 'Enter Current Passcode';
      } else if (state.isEnteringNewPasscode) {
        return 'Enter New Passcode';
      } else if (state.isConfirming) {
        return 'Confirm New Passcode';
      }
    }
    // Handle new passcode setup flow
    else if (state.isSettingNew) {
      if (state.isConfirming) {
        return 'Confirm Passcode';
      } else {
        return 'Set New Passcode';
      }
    }
    // Handle validation flow with different messages based on context
    else if (state.isValidating) {
      // Check for accessing detail page first, as it takes precedence
      if (widget.isAccessingDetail) {
        return 'Enter Passcode to Continue';
      } else if (widget.isLocking) {
        return 'Enter Passcode to Lock';
      } else if (widget.isUnlocking) {
        return 'Enter Passcode to Unlock';
      }
    }

    // Default for validation
    return 'Enter Passcode to Continue';
  }

  /// Build a digit button for the numpad
  Widget _buildDigitButton(
    BuildContext context,
    String digit,
    Function(String) onPressed,
  ) {
    return SizedBox(
      width: 80,
      height: 80,
      child: TextButton(
        onPressed: () => onPressed(digit),
        style: TextButton.styleFrom(shape: const CircleBorder()),
        child: Text(
          digit,
          style: TextStyle(
            fontSize: 28,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
        ),
      ),
    );
  }
}
