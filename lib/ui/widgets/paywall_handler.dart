import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/paywall/paywall_providers.dart';
import 'package:noeji/services/paywall/paywall_trigger_service.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Utility class for handling paywall triggers in the UI
class PaywallHandler {
  /// Handle a limit exception by showing either paywall or error dialog
  /// Returns true if the action should continue (user upgraded), false if cancelled
  static Future<bool> handleLimitException({
    required BuildContext context,
    required WidgetRef ref,
    required LimitException exception,
  }) async {
    try {
      final paywallTriggerService = ref.read(paywallTriggerServiceProvider);

      // Determine if we should show paywall or error dialog
      final result = await paywallTriggerService.shouldShowPaywallForLimit(
        limitType: exception.limitType,
        errorMessage: exception.message,
      );

      // Check if context is still valid
      if (!context.mounted) {
        Logger.debug('PaywallHandler: Context no longer mounted, aborting');
        return false;
      }

      if (result.shouldShowPaywall) {
        Logger.debug(
          'PaywallHandler: Showing paywall for ${exception.limitType}',
        );
        return await _showPaywall(context, ref, exception.limitType);
      } else {
        Logger.debug(
          'PaywallHandler: Showing error dialog for ${exception.limitType}',
        );
        await _showErrorDialog(
          context,
          result.errorMessage ?? exception.message,
        );
        return false;
      }
    } catch (e) {
      Logger.error('PaywallHandler: Error handling limit exception', e);
      // Fallback to error dialog
      if (context.mounted) {
        await _showErrorDialog(context, exception.message);
      }
      return false;
    }
  }

  /// Show the RevenueCat paywall
  /// Returns true if user made a purchase, false if cancelled
  static Future<bool> _showPaywall(
    BuildContext context,
    WidgetRef ref,
    LimitType limitType,
  ) async {
    try {
      if (!context.mounted) return false;

      final paywallTriggerService = ref.read(paywallTriggerServiceProvider);
      final purchased = await paywallTriggerService.showPaywall();

      if (purchased) {
        // Force refresh user tier immediately after purchase
        try {
          Logger.debug(
            'PaywallHandler: Refreshing user tier after successful purchase',
          );
          final refreshUserTier = ref.read(refreshUserTierProvider);
          await refreshUserTier();

          // Also invalidate all the old cached limit providers to force refresh
          Logger.debug('PaywallHandler: Invalidating cached limit providers');
          ref.invalidate(maxIdeabooksProvider);
          ref.invalidate(maxIdeasPerIdeabookProvider);
          ref.invalidate(maxNotesPerIdeabookProvider);
          ref.invalidate(chatRateLimitsProvider);
          ref.invalidate(userTierProvider);
          ref.invalidate(isProUserProvider);

          // Also invalidate real-time providers to force immediate UI updates
          Logger.debug('PaywallHandler: Invalidating real-time providers');
          ref.invalidate(userTierNotifierProvider);
          ref.invalidate(realtimeUserTierProvider);
          ref.invalidate(realtimeIsProUserProvider);

          Logger.debug('PaywallHandler: User tier refreshed successfully');
        } catch (e) {
          Logger.error(
            'PaywallHandler: Failed to refresh user tier after purchase',
            e,
          );
          // Don't fail the purchase flow if refresh fails
        }

        // Show success message if context is still mounted
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Welcome to Noeji Pro! 🎉',
                style: GoogleFonts.afacad(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return purchased;
    } catch (e) {
      Logger.error('PaywallHandler: Error showing paywall', e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error showing subscription options. Please try again.',
              style: GoogleFonts.afacad(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Show error dialog for pro users or when paywall is not applicable
  static Future<void> _showErrorDialog(
    BuildContext context,
    String message,
  ) async {
    if (!context.mounted) return;

    return showDialog<void>(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              _getErrorTitle(message),
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              _getErrorContent(message),
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(
                  'OK',
                  style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                ),
              ),
            ],
          ),
    );
  }

  /// Get appropriate error title based on the message
  static String _getErrorTitle(String message) {
    if (message.toLowerCase().contains('ideabook')) {
      return 'Maximum Ideabooks Reached! 🈵';
    } else if (message.toLowerCase().contains('idea')) {
      return 'Ideabook Full! 📝';
    } else if (message.toLowerCase().contains('note')) {
      return 'Notes Limit Reached! 📋';
    } else if (message.toLowerCase().contains('chat')) {
      return 'Chat Limit Reached! 💬';
    }
    return 'Limit Reached!';
  }

  /// Get appropriate error content based on the message
  static String _getErrorContent(String message) {
    if (message.toLowerCase().contains('ideabook')) {
      return "Wow! You've hit the max ideabook milestone! 🏆 Amazing! To add more, you can free up space by deleting some you no longer need. 👍";
    } else if (message.toLowerCase().contains('idea')) {
      return "This ideabook is packed with ideas! 💡 To add more, consider deleting some existing ideas or creating a new ideabook.";
    } else if (message.toLowerCase().contains('note')) {
      return "This ideabook has reached its notes capacity! 📝 To add more notes, consider deleting some existing ones.";
    }
    return message;
  }
}
