import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Enum to track swipe direction
enum SwipeDirection { leftToRight, rightToLeft }

/// A callback for building the context menu that appears when swiped
typedef ContextMenuBuilder =
    Widget Function(BuildContext context, double totalMenuWidth);

/// A callback for building the main content of the swipeable item
typedef ContentBuilder = Widget Function(BuildContext context);

/// A callback for building the edit mode content
typedef EditModeBuilder = Widget Function(BuildContext context);

/// A callback for building the left swipe content
typedef LeftSwipeBuilder =
    Widget Function(BuildContext context, double totalMenuWidth);

/// A generic swipeable item widget that can be used for any swipeable content
class SwipeableItem extends ConsumerStatefulWidget {
  /// The ID of this item, used to track swipe state
  final String itemId;

  /// Provider that tracks which item is currently swiped right-to-left
  final StateProvider<String?> swipedItemProvider;

  /// Provider that tracks which item is currently left-swiped (left-to-right) (optional)
  final StateProvider<String?>? leftSwipedItemProvider;

  /// Provider that tracks which item is currently in edit mode (optional)
  final StateProvider<String?>? editItemProvider;

  /// Builder for the context menu that appears when swiped right-to-left
  final ContextMenuBuilder contextMenuBuilder;

  /// Builder for the content that appears when swiped left-to-right (optional)
  final LeftSwipeBuilder? leftSwipeBuilder;

  /// Builder for the main content
  final ContentBuilder contentBuilder;

  /// Builder for the edit mode content (optional)
  final EditModeBuilder? editModeBuilder;

  /// Maximum width of the context menu when fully swiped right-to-left
  final double maxSwipeExtent;

  /// Maximum width of the left content when fully swiped left-to-right
  final double maxLeftSwipeExtent;

  /// Threshold to determine when to complete the swipe
  final double swipeThreshold;

  /// Whether to show a divider at the bottom
  final bool showDivider;

  /// Constructor
  const SwipeableItem({
    super.key,
    required this.itemId,
    required this.swipedItemProvider,
    this.leftSwipedItemProvider,
    this.editItemProvider,
    required this.contextMenuBuilder,
    this.leftSwipeBuilder,
    required this.contentBuilder,
    this.editModeBuilder,
    this.maxSwipeExtent = 120.0,
    this.maxLeftSwipeExtent = 120.0,
    this.swipeThreshold = 80.0,
    this.showDivider = true,
  });

  @override
  ConsumerState<SwipeableItem> createState() => _SwipeableItemState();
}

class _SwipeableItemState extends ConsumerState<SwipeableItem>
    with TickerProviderStateMixin {
  // Animation controller for the right-to-left swipe effect
  late AnimationController _swipeController;

  // Animation controller for the left-to-right swipe effect
  late AnimationController _leftSwipeController;

  // Drag gesture variables
  double _dragExtent = 0.0;
  double _leftDragExtent = 0.0;

  // Track swipe direction to avoid conflicts
  SwipeDirection? _currentSwipeDirection;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _swipeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _leftSwipeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Check if this item is already in swipe mode and set initial animation value
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final swipedId = ref.read(widget.swipedItemProvider);
      if (swipedId == widget.itemId) {
        // If already swiped right-to-left, set animation to completed state
        _swipeController.value = 1.0;
      }

      // Check left swipe state if provider exists
      if (widget.leftSwipedItemProvider != null) {
        final leftSwipedId = ref.read(widget.leftSwipedItemProvider!);
        if (leftSwipedId == widget.itemId) {
          // If already swiped left-to-right, set animation to completed state
          _leftSwipeController.value = 1.0;
        }
      }
    });

    // Listen to right-to-left swipe controller to update provider state
    _swipeController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        // Animation completed to closed state, update provider
        if (ref.read(widget.swipedItemProvider) == widget.itemId) {
          ref.read(widget.swipedItemProvider.notifier).state = null;
        }
      } else if (status == AnimationStatus.completed) {
        // Animation completed to open state, update provider
        ref.read(widget.swipedItemProvider.notifier).state = widget.itemId;
      }
    });

    // Listen to left-to-right swipe controller to update provider state
    _leftSwipeController.addStatusListener((status) {
      if (widget.leftSwipedItemProvider != null) {
        if (status == AnimationStatus.dismissed) {
          // Animation completed to closed state, update provider
          if (ref.read(widget.leftSwipedItemProvider!) == widget.itemId) {
            ref.read(widget.leftSwipedItemProvider!.notifier).state = null;
          }
        } else if (status == AnimationStatus.completed) {
          // Animation completed to open state, update provider
          ref.read(widget.leftSwipedItemProvider!.notifier).state =
              widget.itemId;
        }
      }
    });
  }

  @override
  void didUpdateWidget(SwipeableItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the item ID changed, we need to update the animation state
    if (widget.itemId != oldWidget.itemId) {
      // Check if this item is in right-to-left swipe mode
      final swipedId = ref.read(widget.swipedItemProvider);
      final isSwiped = swipedId == widget.itemId;

      // Update animation value based on swipe state
      if (isSwiped && _swipeController.value != 1.0) {
        _swipeController.value = 1.0;
      } else if (!isSwiped && _swipeController.value != 0.0) {
        _swipeController.value = 0.0;
      }

      // Check if this item is in left-to-right swipe mode
      if (widget.leftSwipedItemProvider != null) {
        final leftSwipedId = ref.read(widget.leftSwipedItemProvider!);
        final isLeftSwiped = leftSwipedId == widget.itemId;

        // Update animation value based on left swipe state
        if (isLeftSwiped && _leftSwipeController.value != 1.0) {
          _leftSwipeController.value = 1.0;
        } else if (!isLeftSwiped && _leftSwipeController.value != 0.0) {
          _leftSwipeController.value = 0.0;
        }
      }
    }
  }

  @override
  void dispose() {
    _swipeController.dispose();
    _leftSwipeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Create slide animations here to avoid using MediaQuery in initState
    final slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(
        -widget.maxSwipeExtent / MediaQuery.of(context).size.width,
        0,
      ),
    ).animate(
      CurvedAnimation(parent: _swipeController, curve: Curves.easeInOut),
    );

    final leftSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(
        widget.maxLeftSwipeExtent / MediaQuery.of(context).size.width,
        0,
      ),
    ).animate(
      CurvedAnimation(parent: _leftSwipeController, curve: Curves.easeInOut),
    );

    // Check if this item is in right-to-left swipe mode
    final swipedId = ref.watch(widget.swipedItemProvider);
    final isSwiped = swipedId == widget.itemId;

    // Check if this item is in left-to-right swipe mode
    bool isLeftSwiped = false;
    if (widget.leftSwipedItemProvider != null) {
      final leftSwipedId = ref.watch(widget.leftSwipedItemProvider!);
      isLeftSwiped = leftSwipedId == widget.itemId;
    }

    // Check if this item is in edit mode (if edit mode is supported)
    bool isEditing = false;
    if (widget.editItemProvider != null) {
      final editingId = ref.watch(widget.editItemProvider!);
      isEditing = editingId == widget.itemId;
    }

    // Trigger animations based on state changes - only if animation is not running
    // This prevents animation interruptions during user interaction
    if (isSwiped &&
        _swipeController.value == 0 &&
        !_swipeController.isAnimating) {
      // Close left swipe if open before opening right swipe
      if (isLeftSwiped) {
        _leftSwipeController.reverse();
        if (widget.leftSwipedItemProvider != null) {
          ref.read(widget.leftSwipedItemProvider!.notifier).state = null;
        }
      }
      _swipeController.forward();
    } else if (!isSwiped &&
        _swipeController.value == 1 &&
        !_swipeController.isAnimating) {
      _swipeController.reverse();
    }

    // Trigger left swipe animations
    if (isLeftSwiped &&
        _leftSwipeController.value == 0 &&
        !_leftSwipeController.isAnimating) {
      // Close right swipe if open before opening left swipe
      if (isSwiped) {
        _swipeController.reverse();
        ref.read(widget.swipedItemProvider.notifier).state = null;
      }
      _leftSwipeController.forward();
    } else if (!isLeftSwiped &&
        _leftSwipeController.value == 1 &&
        !_leftSwipeController.isAnimating) {
      _leftSwipeController.reverse();
    }

    // If in edit mode, show the edit UI
    if (isEditing && widget.editModeBuilder != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            padding: const EdgeInsets.all(16.0),
            child: widget.editModeBuilder!(context),
          ),
          if (widget.showDivider)
            Divider(
              height: 1,
              thickness: 0.5,
              color: NoejiTheme.colorsOf(context).divider,
            ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          // Handle horizontal drag for bidirectional swipe effect
          onHorizontalDragStart: (details) {
            // Determine initial state and reset drag extents
            if (isSwiped) {
              _dragExtent = widget.maxSwipeExtent;
              _leftDragExtent = 0;
              _currentSwipeDirection = SwipeDirection.rightToLeft;
            } else if (isLeftSwiped) {
              _leftDragExtent = widget.maxLeftSwipeExtent;
              _dragExtent = 0;
              _currentSwipeDirection = SwipeDirection.leftToRight;
            } else {
              _dragExtent = 0;
              _leftDragExtent = 0;
              _currentSwipeDirection = null;
            }
          },
          onHorizontalDragUpdate: (details) {
            final delta = details.primaryDelta!;

            // Determine swipe direction if not already set
            if (_currentSwipeDirection == null && delta.abs() > 5) {
              _currentSwipeDirection =
                  delta < 0
                      ? SwipeDirection.rightToLeft
                      : SwipeDirection.leftToRight;
            }

            setState(() {
              if (_currentSwipeDirection == SwipeDirection.rightToLeft) {
                // Right-to-left swipe (revealing context menu)
                _dragExtent -= delta;
                _dragExtent = _dragExtent.clamp(0.0, widget.maxSwipeExtent);
                _leftDragExtent = 0;

                // Update right swipe animation
                _swipeController.value = (_dragExtent / widget.maxSwipeExtent)
                    .clamp(0.0, 1.0);
                _leftSwipeController.value = 0;
              } else if (_currentSwipeDirection == SwipeDirection.leftToRight &&
                  widget.leftSwipeBuilder != null) {
                // Left-to-right swipe (revealing left content)
                _leftDragExtent += delta;
                _leftDragExtent = _leftDragExtent.clamp(
                  0.0,
                  widget.maxLeftSwipeExtent,
                );
                _dragExtent = 0;

                // Update left swipe animation
                _leftSwipeController.value = (_leftDragExtent /
                        widget.maxLeftSwipeExtent)
                    .clamp(0.0, 1.0);
                _swipeController.value = 0;
              }
            });
          },
          onHorizontalDragEnd: (details) {
            final velocity = details.primaryVelocity ?? 0;

            if (_currentSwipeDirection == SwipeDirection.rightToLeft) {
              // Handle right-to-left swipe completion
              if (velocity.abs() > 200) {
                if (velocity < 0) {
                  // Fast swipe left - open
                  _swipeController.forward();
                  ref.read(widget.swipedItemProvider.notifier).state =
                      widget.itemId;
                } else {
                  // Fast swipe right - close
                  _swipeController.reverse();
                  ref.read(widget.swipedItemProvider.notifier).state = null;
                }
              } else {
                // Use position threshold
                if (_dragExtent >= widget.swipeThreshold) {
                  _swipeController.forward();
                  ref.read(widget.swipedItemProvider.notifier).state =
                      widget.itemId;
                } else {
                  _swipeController.reverse();
                  ref.read(widget.swipedItemProvider.notifier).state = null;
                }
              }
            } else if (_currentSwipeDirection == SwipeDirection.leftToRight &&
                widget.leftSwipeBuilder != null) {
              // Handle left-to-right swipe completion
              if (velocity.abs() > 200) {
                if (velocity > 0) {
                  // Fast swipe right - open
                  _leftSwipeController.forward();
                  if (widget.leftSwipedItemProvider != null) {
                    ref.read(widget.leftSwipedItemProvider!.notifier).state =
                        widget.itemId;
                  }
                } else {
                  // Fast swipe left - close
                  _leftSwipeController.reverse();
                  if (widget.leftSwipedItemProvider != null) {
                    ref.read(widget.leftSwipedItemProvider!.notifier).state =
                        null;
                  }
                }
              } else {
                // Use position threshold
                if (_leftDragExtent >= widget.swipeThreshold) {
                  _leftSwipeController.forward();
                  if (widget.leftSwipedItemProvider != null) {
                    ref.read(widget.leftSwipedItemProvider!.notifier).state =
                        widget.itemId;
                  }
                } else {
                  _leftSwipeController.reverse();
                  if (widget.leftSwipedItemProvider != null) {
                    ref.read(widget.leftSwipedItemProvider!.notifier).state =
                        null;
                  }
                }
              }
            }

            // Reset swipe direction
            _currentSwipeDirection = null;
          },
          // Handle tap on row to restore normal state
          onTap:
              (isSwiped || isLeftSwiped)
                  ? () {
                    if (isSwiped) {
                      _swipeController.reverse();
                      ref.read(widget.swipedItemProvider.notifier).state = null;
                    }
                    if (isLeftSwiped && widget.leftSwipedItemProvider != null) {
                      _leftSwipeController.reverse();
                      ref.read(widget.leftSwipedItemProvider!.notifier).state =
                          null;
                    }
                  }
                  : null,
          child: Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: ClipRect(
              child: Stack(
                children: [
                  // Left swipe content (fixed position, revealed when main row slides right)
                  // Only render when not in right swipe mode to prevent overlap
                  if (widget.leftSwipeBuilder != null && !isSwiped)
                    Positioned(
                      left: 0,
                      top: 0,
                      bottom: 0,
                      child: widget.leftSwipeBuilder!(
                        context,
                        widget.maxLeftSwipeExtent,
                      ),
                    ),

                  // Right context menu (fixed position, revealed when main row slides left)
                  // Only render when not in left swipe mode to prevent overlap
                  if (!isLeftSwiped)
                    Positioned(
                      right: 0,
                      top: 0,
                      bottom: 0,
                      child: widget.contextMenuBuilder(
                        context,
                        widget.maxSwipeExtent,
                      ),
                    ),

                  // Main row content with bidirectional slide animation
                  SlideTransition(
                    position:
                        isLeftSwiped ? leftSlideAnimation : slideAnimation,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      color: Theme.of(context).scaffoldBackgroundColor,
                      alignment: Alignment.centerLeft,
                      child: widget.contentBuilder(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (widget.showDivider)
          Divider(
            height: 1,
            thickness: 0.5,
            color: NoejiTheme.colorsOf(context).divider,
          ),
      ],
    );
  }
}
