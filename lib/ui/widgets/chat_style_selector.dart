import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for selecting chat style preferences
class ChatStyleSelector extends ConsumerWidget {
  const ChatStyleSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appBehavior = ref.watch(appBehaviorProvider);
    final llmPrompts = ref.watch(llmPromptsProvider);

    if (!appBehavior.isLoaded) {
      return const Center(child: CircularProgressIndicator());
    }

    // Get available chat styles from Remote Config
    final chatResponseStyles = llmPrompts.getChatResponseStyle();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Verbosity section
        _buildStyleSection(
          context,
          ref,
          'Verbosity',
          chatResponseStyles['verbosity'] as Map<String, dynamic>? ?? {},
          appBehavior.chatStyleVerbosity,
          (value) => ref
              .read(appBehaviorProvider.notifier)
              .setChatStyleVerbosity(value),
        ),
        const SizedBox(height: 16),

        // Tone section
        _buildStyleSection(
          context,
          ref,
          'Tone',
          chatResponseStyles['tone'] as Map<String, dynamic>? ?? {},
          appBehavior.chatStyleTone,
          (value) =>
              ref.read(appBehaviorProvider.notifier).setChatStyleTone(value),
        ),
        const SizedBox(height: 16),

        // Output Format section
        _buildStyleSection(
          context,
          ref,
          'Output Format',
          chatResponseStyles['output_format'] as Map<String, dynamic>? ?? {},
          appBehavior.chatStyleOutputFormat,
          (value) => ref
              .read(appBehaviorProvider.notifier)
              .setChatStyleOutputFormat(value),
        ),
      ],
    );
  }

  Widget _buildStyleSection(
    BuildContext context,
    WidgetRef ref,
    String sectionTitle,
    Map<String, dynamic> options,
    String selectedValue,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          sectionTitle,
          style: GoogleFonts.afacad(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: NoejiTheme.colorsOf(context).textPrimary,
          ),
        ),
        const SizedBox(height: 8),

        // Options
        ...options.entries.map((entry) {
          final optionKey = entry.key;
          final optionData = entry.value as Map<String, dynamic>;
          final optionDescription =
              optionData['description'] as String? ?? optionData.toString();
          final isSelected = optionKey == selectedValue;

          return _buildStyleOption(
            context,
            optionKey,
            optionDescription,
            isSelected,
            () {
              onChanged(optionKey);
              Logger.debug('Selected $sectionTitle: $optionKey');
            },
          );
        }),
      ],
    );
  }

  Widget _buildStyleOption(
    BuildContext context,
    String optionKey,
    String optionDescription,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Option content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Option name
                      Text(
                        _formatOptionName(optionKey),
                        style: GoogleFonts.afacad(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: NoejiTheme.colorsOf(context).textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Option description
                      Text(
                        optionDescription,
                        style: GoogleFonts.afacad(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: NoejiTheme.colorsOf(context).textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Check mark for selected option
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                    size: 24,
                  )
                else
                  const SizedBox(
                    width: 24,
                  ), // Placeholder to maintain alignment
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatOptionName(String optionKey) {
    // Convert snake_case to Title Case
    return optionKey
        .split('_')
        .map(
          (word) =>
              word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1),
        )
        .join(' ');
  }
}
