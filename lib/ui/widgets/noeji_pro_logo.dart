import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Helper widget to display the Noeji PRO logo using split SVGs
class NoejiProLogo extends StatelessWidget {
  /// Height of the logo
  final double height;

  /// Constructor
  const NoejiProLogo({
    super.key,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    // Determine theme-aware colors for PRO badge
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final proBackgroundColor = isDarkTheme
        ? NoejiTheme.colorsOf(context).textPrimary  // Light background in dark theme
        : NoejiTheme.colorsOf(context).textPrimary; // Dark background in light theme
    final proTextColor = isDarkTheme
        ? Theme.of(context).scaffoldBackgroundColor  // Dark text in dark theme
        : Theme.of(context).scaffoldBackgroundColor; // Light text in light theme

    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(
          'assets/images/noeji_logo_v3.svg', // Just "NOEJI"
          height: height,
          colorFilter: ColorFilter.mode(
            NoejiTheme.colorsOf(context).textPrimary,
            BlendMode.srcIn,
          ),
        ),
        const SizedBox(width: 4), // Add spacing between "NOEJI" and "PRO"
        // Create a custom PRO badge with theme-aware colors
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: proBackgroundColor,
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            'PRO',
            style: GoogleFonts.afacad(
              fontSize: height * 0.4, // Scale font size with height
              fontWeight: FontWeight.w700,
              color: proTextColor,
              height: 1.0,
            ),
          ),
        ),
      ],
    );
  }
}
