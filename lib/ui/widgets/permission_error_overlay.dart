import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';

/// A widget that displays an overlay when a permission error occurs
///
/// This widget should be placed at the top level of the widget tree
/// to ensure it can overlay the entire app when a permission error occurs
class PermissionErrorOverlay extends ConsumerWidget {
  /// The child widget to display when there is no permission error
  final Widget child;

  /// Creates a new permission error overlay
  const PermissionErrorOverlay({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      // Watch the global permission error state
      final hasPermissionError = ref.watch(globalPermissionErrorProvider);

      // If there's no permission error, just show the child
      if (!hasPermissionError) {
        return child;
      }

      Logger.debug('Showing permission error overlay');

      // If there is a permission error, show the error overlay
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Error icon
                  const Icon(Icons.error_outline, size: 80, color: Colors.red),
                  const SizedBox(height: 24),
                  // Error message
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Text(
                      ErrorUtils.permissionDeniedMessage,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      // If there's an error in the error overlay, log it and show the child
      Logger.error('Error in PermissionErrorOverlay', e);
      return child;
    }
  }
}
