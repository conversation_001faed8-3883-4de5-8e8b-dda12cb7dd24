import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/ui/providers/note_swipe_provider.dart';
import 'package:noeji/ui/screens/note_detail_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/common/swipeable_item.dart';

/// Widget for displaying a note item in the list with swipe functionality
class SwipeableNoteItem extends ConsumerWidget {
  /// The note to display
  final Note note;

  /// The ideabook this note belongs to
  final Ideabook ideabook;

  // Context menu constants
  static const double _maxSwipeExtent =
      60.0; // Maximum swipe distance for context menu (1 icon)
  static const double _swipeThreshold =
      40.0; // Threshold to determine when to complete the swipe

  /// Constructor
  const SwipeableNoteItem({
    super.key,
    required this.note,
    required this.ideabook,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the generic SwipeableItem widget
    return SwipeableItem(
      itemId: note.id,
      swipedItemProvider: swipedNoteIdProvider,
      maxSwipeExtent: _maxSwipeExtent,
      swipeThreshold: _swipeThreshold,
      contextMenuBuilder:
          (context, totalMenuWidth) =>
              _buildContextMenu(context, ref, totalMenuWidth),
      contentBuilder: (context) => _buildNormalRow(context),
      showDivider:
          false, // Don't show divider in SwipeableItem to avoid double borders
    );
  }

  /// Build the context menu that appears when swiped
  Widget _buildContextMenu(
    BuildContext context,
    WidgetRef ref,
    double totalMenuWidth,
  ) {
    // Constants for the context menu
    const double iconWidth = 60.0;

    return Container(
      width: totalMenuWidth,
      height: double.infinity, // Make sure it stretches to full height
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center icons vertically
        children: [
          // Delete icon
          SizedBox(
            width: iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.delete,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Show confirmation dialog before deleting
                  _showDeleteConfirmationDialog(context, ref);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the normal row content
  Widget _buildNormalRow(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // Check if this note is in swipe mode
        final swipedId = ref.watch(swipedNoteIdProvider);
        final isSwiped = swipedId == note.id;

        return InkWell(
          onTap: () {
            if (isSwiped) {
              // If swiped, close the context menu instead of navigating
              ref.read(swipedNoteIdProvider.notifier).state = null;
            } else {
              // Navigate to the note detail screen as a separate page
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder:
                      (context) =>
                          NoteDetailScreen(note: note, ideabookId: ideabook.id),
                ),
              );
            }
          },
          child: Container(
            padding: const EdgeInsets.all(16.0),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Create a TextPainter to measure if the text would be truncated
                final textSpan = TextSpan(
                  text: note.content,
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                );
                final textPainter = TextPainter(
                  text: textSpan,
                  maxLines: 4,
                  textDirection: TextDirection.ltr,
                );
                textPainter.layout(maxWidth: constraints.maxWidth);

                // Check if the text would be truncated
                final isTruncated = textPainter.didExceedMaxLines;

                if (isTruncated) {
                  // If truncated, clean the markdown first
                  String cleanedText = _cleanMarkdownForPreview(note.content);

                  // Replace empty newlines (consecutive newlines) with a single newline
                  cleanedText = cleanedText.replaceAll(
                    RegExp(r'\n\s*\n'),
                    '\n',
                  );

                  // Manually truncate the text to ensure ellipsis is visible
                  // Create a new TextPainter to measure and truncate the text
                  final TextSpan measureSpan = TextSpan(
                    text: cleanedText,
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                  );
                  final TextPainter measurePainter = TextPainter(
                    text: measureSpan,
                    maxLines: 4,
                    textDirection: TextDirection.ltr,
                  );
                  measurePainter.layout(maxWidth: constraints.maxWidth);

                  // If text is truncated, we need to manually truncate it
                  String truncatedText = cleanedText;
                  if (measurePainter.didExceedMaxLines) {
                    // Get the position where the text would be truncated
                    final int endPosition =
                        measurePainter
                            .getPositionForOffset(
                              Offset(
                                constraints.maxWidth,
                                measurePainter.height,
                              ),
                            )
                            .offset;

                    // Truncate the text, leaving room for ellipsis
                    // Subtract some characters to ensure ellipsis is visible
                    final int safeEndPosition =
                        endPosition > 10 ? endPosition - 10 : endPosition;
                    truncatedText = cleanedText.substring(0, safeEndPosition);

                    // Ensure we don't cut in the middle of a word if possible
                    if (safeEndPosition < cleanedText.length &&
                        safeEndPosition > 0) {
                      final lastSpaceIndex = truncatedText.lastIndexOf(' ');
                      if (lastSpaceIndex > 0 &&
                          lastSpaceIndex > safeEndPosition - 20) {
                        truncatedText = truncatedText.substring(
                          0,
                          lastSpaceIndex,
                        );
                      }
                    }

                    // Remove any trailing newlines
                    truncatedText = truncatedText.trimRight();

                    // Add ellipsis
                    truncatedText = '$truncatedText...';
                  }

                  return Text(
                    truncatedText,
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                    maxLines: 4,
                    overflow:
                        TextOverflow
                            .visible, // Prevent Flutter from adding its own ellipsis
                  );
                } else {
                  // If not truncated, still clean the markdown for consistency
                  String cleanedText = _cleanMarkdownForPreview(note.content);

                  // Replace empty newlines (consecutive newlines) with a single newline
                  cleanedText = cleanedText.replaceAll(
                    RegExp(r'\n\s*\n'),
                    '\n',
                  );

                  return Text(
                    cleanedText,
                    style: NoejiTheme.textStylesOf(context).bodyMedium,
                  );
                }
              },
            ),
          ),
        );
      },
    );
  }

  /// Show confirmation dialog before deleting a note
  void _showDeleteConfirmationDialog(BuildContext context, WidgetRef ref) {
    // Store the scaffold messenger before the async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Get appropriate text style for snackbar
    final snackBarTextColor = NoejiTheme.getSnackBarTextColor(context);
    final textStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: snackBarTextColor);

    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Delete Note',
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              'Are you sure you want to delete this note?',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(false),
                child: Text(
                  'Cancel',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(true),
                child: Text(
                  'Delete',
                  style: NoejiTheme.textStylesOf(
                    dialogContext,
                  ).buttonText.copyWith(
                    color: NoejiTheme.colorsOf(dialogContext).error,
                  ),
                ),
              ),
            ],
          ),
    ).then((confirmDelete) {
      if (confirmDelete == true) {
        // Delete the note directly using the repository
        final repository = ref.read(noteRepositoryProvider);
        repository.deleteNote(ideabook.id, note.id);

        // Show a snackbar to confirm deletion
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Note deleted', style: textStyle),
            duration: const Duration(seconds: 1),
          ),
        );
      }

      // Close the context menu
      ref.read(swipedNoteIdProvider.notifier).state = null;
    });
  }

  /// Clean markdown syntax for preview
  String _cleanMarkdownForPreview(String text) {
    String cleaned = text;

    // Use RegExp.replace with a match function instead of replaceAll with $1 references

    // Remove bold markers
    cleaned = RegExp(r'\*\*(.*?)\*\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove italic markers
    cleaned = RegExp(r'\*(.*?)\*')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    cleaned = RegExp(r'_(.*?)_')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove headers
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s+'), '');

    // Remove code blocks
    cleaned = cleaned.replaceAll(RegExp(r'```.*?```', dotAll: true), '');

    // Remove inline code
    cleaned = RegExp(r'`(.*?)`')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove bullet points
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '');

    // Remove numbered lists
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // Remove blockquotes
    cleaned = cleaned.replaceAll(RegExp(r'^\s*>\s+', multiLine: true), '');

    // Remove horizontal rules
    cleaned = cleaned.replaceAll(
      RegExp(r'^\s*[-*_]{3,}\s*$', multiLine: true),
      '',
    );

    // Remove links but keep the text
    cleaned = RegExp(r'\[(.*?)\]\(.*?\)')
        .allMatches(cleaned)
        .fold(
          cleaned,
          (prev, match) => prev.replaceFirst(match[0]!, match[1]!),
        );

    // Remove images
    cleaned = cleaned.replaceAll(RegExp(r'!\[.*?\]\(.*?\)'), '');

    return cleaned;
  }
}
