import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/services/tour/tour_service.dart';
import 'package:noeji/ui/providers/firestore_idea_listener_controller.dart';
import 'package:noeji/ui/providers/idea_edit_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/showcase_tour.dart';
import 'package:noeji/ui/widgets/swipeable_idea_item.dart';
import 'package:noeji/utils/logger.dart';

/// Provider to track which idea is currently being dragged
final draggedIdeaIdProvider = StateProvider<String?>((ref) => null);

/// Widget for displaying a reorderable list of ideas with drag-and-drop functionality
class ReorderableIdeasList extends ConsumerStatefulWidget {
  /// The ideabook that contains these ideas
  final Ideabook ideabook;

  /// The list of ideas to display
  final List<Idea> ideas;

  /// Constructor
  const ReorderableIdeasList({
    super.key,
    required this.ideabook,
    required this.ideas,
  });

  @override
  ConsumerState<ReorderableIdeasList> createState() =>
      _ReorderableIdeasListState();
}

class _ReorderableIdeasListState extends ConsumerState<ReorderableIdeasList> {
  /// Flag to track if we've already checked for the tour
  bool _hasCheckedForTour = false;

  @override
  void initState() {
    super.initState();
    // Log the initial list when the widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logIdeasSortingDetails();

      // Check if we should show the ideas reordering tour
      if (widget.ideas.length == 3 && !_hasCheckedForTour) {
        _hasCheckedForTour = true;
        _checkAndShowIdeasReorderingTour();
      }
    });
  }

  @override
  void didUpdateWidget(ReorderableIdeasList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the number of ideas has changed to exactly 3
    if (oldWidget.ideas.length != 3 &&
        widget.ideas.length == 3 &&
        !_hasCheckedForTour) {
      _hasCheckedForTour = true;
      // Add a small delay to ensure the UI is fully updated
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _checkAndShowIdeasReorderingTour();
        }
      });
    }
  }

  /// Check if we should show the ideas reordering tour and show it if needed
  Future<void> _checkAndShowIdeasReorderingTour() async {
    try {
      // Use the TourService to show the ideas reordering tour
      if (mounted) {
        final shown = await TourService.showIdeasReorderingTour(context);
        Logger.debug('Ideas reordering tour shown: $shown');
      }
    } catch (e) {
      Logger.error('Error showing ideas reordering tour', e);
    }
  }

  /// Log detailed sorting information for all ideas in the list
  void _logIdeasSortingDetails() {
    final ideas = widget.ideas;
    Logger.debug('===== IDEA LIST SORTING DEBUG =====');
    Logger.debug('Total ideas: ${ideas.length}');

    for (int i = 0; i < ideas.length; i++) {
      final idea = ideas[i];
      final content =
          idea.content.length > 30
              ? '${idea.content.substring(0, 30)}...'
              : idea.content;
      final timestamp = idea.createdAt.millisecondsSinceEpoch;
      final sValue = idea.sortOrder != null ? idea.sortOrder.toString() : 'n/a';
      final effectiveValue = idea.getEffectiveSortValue();

      Logger.debug(
        'Idea[$i]: effective=$effectiveValue | t=$timestamp | s=$sValue | "$content"',
      );
    }
    Logger.debug('===================================');
  }

  @override
  Widget build(BuildContext context) {
    // Get the currently dragged idea ID
    final draggedIdeaId = ref.watch(draggedIdeaIdProvider);
    // Get the current edit mode idea ID
    final editModeIdeaId = ref.watch(ideaEditProvider);

    // Log the ideas list whenever it's rebuilt
    _logIdeasSortingDetails();

    // Create the reorderable list view
    return ReorderableListView.builder(
      itemCount: widget.ideas.length,
      onReorder: _handleReorder,
      buildDefaultDragHandles: false, // We'll handle drag detection ourselves
      itemBuilder: (context, index) {
        final idea = widget.ideas[index];
        final isBeingDragged = draggedIdeaId == idea.id;
        final isInEditMode = editModeIdeaId != null;

        // Check if this is the last idea and we have exactly 3 ideas
        final isLastIdeaWithThreeTotal =
            (index == widget.ideas.length - 1) && widget.ideas.length == 3;

        // Create the basic item
        Widget itemWidget = _buildIdeaItem(idea, isBeingDragged, index);

        // If this is the last idea with exactly 3 ideas, wrap it with the showcase
        if (isLastIdeaWithThreeTotal) {
          itemWidget = TourService.createShowcaseWidget(
            context: context,
            key: ShowcaseKeys.ideasReorderingTour,
            description: 'Press and hold 👆 an idea, then drag it to reorder.',
            child: itemWidget,
          );
        }

        return ReorderableDelayedDragStartListener(
          key: ValueKey(idea.id),
          index: index,
          enabled:
              !isInEditMode, // Disable reordering when any idea is in edit mode
          child: itemWidget,
        );
      },
      proxyDecorator: (child, index, animation) {
        // Make the dragged item slightly bigger and brighter
        return AnimatedBuilder(
          animation: animation,
          builder: (BuildContext context, Widget? child) {
            final double scale = 1.0 + 0.05 * animation.value;
            return Transform.scale(
              scale: scale,
              child: Material(
                elevation: 4.0 * animation.value,
                color: Colors.transparent,
                shadowColor: Theme.of(context).shadowColor,
                child: child,
              ),
            );
          },
          child: child,
        );
      },
    );
  }

  /// Build an individual idea item
  Widget _buildIdeaItem(Idea idea, bool isBeingDragged, int index) {
    // When dragging, make the item visually distinct
    final ideaItem = Container(
      decoration: BoxDecoration(
        color:
            isBeingDragged
                ? Theme.of(context).brightness == Brightness.light
                    ? Colors.grey.shade100
                    : Colors.grey.shade800
                : null,
        // Add a subtle border when being dragged
        border:
            isBeingDragged
                ? Border.all(
                  color:
                      Theme.of(context).brightness == Brightness.light
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                  width: 1.0,
                )
                : null,
      ),
      // No transform here - we'll use the proxyDecorator for scaling during drag
      child: SwipeableIdeaItem(idea: idea, ideabookId: widget.ideabook.id),
    );

    // Add a divider after each item except the last one
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ideaItem,
        // Add divider after each item except the last one
        if (index < widget.ideas.length - 1)
          Divider(
            height: 1,
            thickness: 0.5,
            color: NoejiTheme.colorsOf(context).divider,
          ),
      ],
    );
  }

  /// Handle reordering of ideas
  void _handleReorder(int oldIndex, int newIndex) {
    Logger.debug('Reordering idea from index $oldIndex to $newIndex');

    // Adjust the newIndex if it's after the oldIndex
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // Get the moved idea
    final movedIdea = widget.ideas[oldIndex];
    final shortContent =
        movedIdea.content.length > 20
            ? '${movedIdea.content.substring(0, 20)}...'
            : movedIdea.content;
    Logger.debug('Moving idea ${movedIdea.id} with content: "$shortContent"');
    Logger.debug(
      'Moved idea details: effective=${movedIdea.getEffectiveSortValue()} | t=${movedIdea.createdAt.millisecondsSinceEpoch} | s=${movedIdea.sortOrder ?? "n/a"}',
    );

    // Calculate the new sort order value based on the algorithm
    final newSortOrder = _calculateNewSortOrder(oldIndex, newIndex);
    Logger.debug('Calculated new sort order: $newSortOrder');

    // Update the sort order in Firestore
    if (newSortOrder != null) {
      Logger.debug('Updating sort order in Firestore for idea ${movedIdea.id}');

      // Use the listener controller to update the sort order
      // This will update both the UI and Firestore
      ref
          .read(
            firestoreIdeasListenerControllerProvider(
              widget.ideabook.id,
            ).notifier,
          )
          .updateIdeaSortOrder(movedIdea.id, newSortOrder)
          .then((success) {
            Logger.debug('Sort order update result: $success');

            // Log the list again after the update to see the changes
            Logger.debug(
              'List after reordering (before Firestore update is applied):',
            );
            _logIdeasSortingDetails();

            // Schedule another log after a short delay to capture the updated list
            Future.delayed(const Duration(milliseconds: 500), () {
              Logger.debug(
                'List after reordering (with delay to capture Firestore update):',
              );
              _logIdeasSortingDetails();
            });
          })
          .catchError((error) {
            Logger.error('Error updating sort order', error);
          });
    } else {
      Logger.error('Failed to calculate new sort order');
    }
  }

  /// Calculate the new sort order value for an idea being moved
  /// Returns null if the calculation cannot be performed
  double? _calculateNewSortOrder(int oldIndex, int newIndex) {
    final ideas = widget.ideas;
    Logger.debug(
      'Calculating new sort order for move from index $oldIndex to $newIndex (total ideas: ${ideas.length})',
    );

    // Log the current state of the list before calculating
    Logger.debug('Current list state before calculating new sort order:');
    for (int i = 0; i < ideas.length; i++) {
      final idea = ideas[i];
      final shortContent =
          idea.content.length > 15
              ? '${idea.content.substring(0, 15)}...'
              : idea.content;
      Logger.debug(
        '  [$i]: effective=${idea.getEffectiveSortValue()} | t=${idea.createdAt.millisecondsSinceEpoch} | s=${idea.sortOrder ?? "n/a"} | "$shortContent"',
      );
    }

    // Get the moved idea
    final movedIdea = ideas[oldIndex];
    Logger.debug(
      'Moved idea: effective=${movedIdea.getEffectiveSortValue()} | t=${movedIdea.createdAt.millisecondsSinceEpoch} | s=${movedIdea.sortOrder ?? "n/a"} | "${movedIdea.content.substring(0, movedIdea.content.length.clamp(0, 15))}..."',
    );

    // Case 1: Moved to the absolute top of the list
    if (newIndex == 0) {
      Logger.debug('Case 1: Moving to the absolute top of the list');
      final topIdea = ideas[0];
      final topValue = topIdea.getEffectiveSortValue();

      Logger.debug(
        'Top idea: effective=$topValue | t=${topIdea.createdAt.millisecondsSinceEpoch} | s=${topIdea.sortOrder ?? "n/a"} | "${topIdea.content.substring(0, topIdea.content.length.clamp(0, 15))}..."',
      );

      // New sort value should be higher than the current top idea
      final newValue = topValue + 1.0;
      Logger.debug('New sort value (top + 1.0): $newValue');
      return newValue;
    }

    // Case 2: Moved to the absolute bottom of the list
    if (newIndex == ideas.length - 1) {
      Logger.debug('Case 2: Moving to the absolute bottom of the list');
      final bottomIdea = ideas[ideas.length - 1];
      final bottomValue = bottomIdea.getEffectiveSortValue();

      Logger.debug(
        'Bottom idea: effective=$bottomValue | t=${bottomIdea.createdAt.millisecondsSinceEpoch} | s=${bottomIdea.sortOrder ?? "n/a"} | "${bottomIdea.content.substring(0, bottomIdea.content.length.clamp(0, 15))}..."',
      );

      // New sort value should be lower than the current bottom idea
      final newValue = bottomValue - 1.0;
      Logger.debug('New sort value (bottom - 1.0): $newValue');
      return newValue;
    }

    // Case 3: Moved between two existing ideas
    Logger.debug('Case 3: Moving between two existing ideas');

    // Create a copy of the ideas list without the moved idea
    final ideasWithoutMoved = List<Idea>.from(ideas);
    ideasWithoutMoved.removeAt(oldIndex);

    // Now get the ideas that will be above and below the moved idea in its new position
    final ideaAbove =
        ideasWithoutMoved[newIndex - 1]; // -1 because we removed an item
    final ideaBelow = ideasWithoutMoved[newIndex];

    final aboveValue = ideaAbove.getEffectiveSortValue();
    final belowValue = ideaBelow.getEffectiveSortValue();

    Logger.debug(
      'Idea above: effective=$aboveValue | t=${ideaAbove.createdAt.millisecondsSinceEpoch} | s=${ideaAbove.sortOrder ?? "n/a"} | "${ideaAbove.content.substring(0, ideaAbove.content.length.clamp(0, 15))}..."',
    );
    Logger.debug(
      'Idea below: effective=$belowValue | t=${ideaBelow.createdAt.millisecondsSinceEpoch} | s=${ideaBelow.sortOrder ?? "n/a"} | "${ideaBelow.content.substring(0, ideaBelow.content.length.clamp(0, 15))}..."',
    );

    // Calculate the average of the two values
    final newValue = (aboveValue + belowValue) / 2.0;
    Logger.debug(
      'New sort value ((above + below) / 2): $newValue = ($aboveValue + $belowValue) / 2.0',
    );

    // Check if the new value is actually between the two values
    if ((aboveValue > newValue && newValue > belowValue) ||
        (aboveValue < newValue && newValue < belowValue)) {
      Logger.debug(
        '✅ New sort value is correctly between above and below values',
      );
    } else {
      Logger.error(
        '❌ New sort value is NOT between above and below values! This may cause sorting issues.',
      );
    }

    return newValue;
  }
}
