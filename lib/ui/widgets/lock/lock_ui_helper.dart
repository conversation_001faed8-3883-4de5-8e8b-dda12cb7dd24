import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/security/lock_operation.dart';
import 'package:noeji/models/security/lock_result.dart';
import 'package:noeji/services/security/ideabook_lock_service.dart';
import 'package:noeji/ui/screens/passcode_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Helper class for lock-related UI operations
///
/// This class provides reusable methods for common lock UI patterns,
/// improving consistency and reducing code duplication across the app.
class LockUIHelper {
  /// Shows a passcode screen for the given lock operation
  ///
  /// Returns true if the operation was successful, false otherwise
  static Future<bool> showPasscodeScreenForOperation({
    required BuildContext context,
    required WidgetRef ref,
    required LockOperation operation,
    VoidCallback? onSuccess,
    VoidCallback? onCancel,
  }) async {
    try {
      Logger.debug(
        'LockUIHelper: Showing passcode screen for operation: ${operation.type}',
      );

      final completer = Completer<bool>();

      if (!context.mounted) {
        Logger.debug(
          'LockUIHelper: Context not mounted, cannot show passcode screen',
        );
        return false;
      }

      await showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        builder: (bottomSheetContext) {
          return SizedBox(
            height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            child: PasscodeScreen(
              ideabook: null, // We'll handle this through the operation
              isLocking: operation.type == LockOperationType.lock,
              isUnlocking: operation.type == LockOperationType.unlock,
              onSuccess: () async {
                Logger.debug('LockUIHelper: Passcode validation successful');

                // Close the passcode screen
                if (bottomSheetContext.mounted) {
                  Navigator.of(bottomSheetContext).pop();
                }

                // Execute the lock operation
                final lockService = ref.read(ideabookLockServiceProvider);
                final result = await lockService.executeLockOperation(
                  operation,
                );

                // Handle the result
                if (context.mounted) {
                  await _handleLockResult(context, result);
                }

                // Call success callback
                onSuccess?.call();

                // Complete with success
                if (!completer.isCompleted) {
                  completer.complete(result.isSuccess);
                }
              },
              onCancel: () {
                Logger.debug('LockUIHelper: Passcode entry cancelled');

                // Close the passcode screen
                if (bottomSheetContext.mounted) {
                  Navigator.of(bottomSheetContext).pop();
                }

                // Call cancel callback
                onCancel?.call();

                // Complete with failure
                if (!completer.isCompleted) {
                  completer.complete(false);
                }
              },
            ),
          );
        },
      );

      return await completer.future;
    } catch (e) {
      Logger.error('LockUIHelper: Error showing passcode screen', e);
      return false;
    }
  }

  /// Handles the result of a lock operation by showing appropriate UI feedback
  static Future<void> _handleLockResult(
    BuildContext context,
    LockResult result,
  ) async {
    if (!context.mounted) return;

    final messenger = ScaffoldMessenger.of(context);
    final colors = NoejiTheme.colorsOf(context);

    if (result.isSuccess) {
      // Show success message
      messenger.showSnackBar(
        SnackBar(
          content: Text(
            result.displayMessage,
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.green, // Use standard green for success
        ),
      );
    } else {
      // Show error message with appropriate styling
      Color backgroundColor;
      String message = result.displayMessage;

      // Customize message and color based on error type
      if (result.isPasscodeError) {
        backgroundColor = colors.error;
        if (result.hasError(LockErrorCode.noPasscodeSet)) {
          message = 'Please set a passcode first in Settings';
        }
      } else if (result.isStateError) {
        backgroundColor = Colors.orange; // Use orange for state errors
      } else {
        backgroundColor = colors.error;
      }

      messenger.showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: NoejiTheme.textStylesOf(context).bodyMedium,
          ),
          duration: const Duration(seconds: 2),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }

  /// Shows a confirmation dialog for lock operations
  static Future<bool> showLockConfirmationDialog({
    required BuildContext context,
    required LockOperationType operationType,
    required String ideabookName,
  }) async {
    if (!context.mounted) return false;

    final colors = NoejiTheme.colorsOf(context);
    final textStyles = NoejiTheme.textStylesOf(context);

    String title;
    String content;
    String confirmText;

    switch (operationType) {
      case LockOperationType.lock:
        title = 'Lock Ideabook';
        content =
            'Are you sure you want to lock "$ideabookName"? You\'ll need your passcode to access it.';
        confirmText = 'Lock';
        break;
      case LockOperationType.unlock:
        title = 'Unlock Ideabook';
        content =
            'Are you sure you want to unlock "$ideabookName"? Anyone with access to your device can view it.';
        confirmText = 'Unlock';
        break;
      case LockOperationType.toggle:
        title = 'Toggle Lock';
        content =
            'Are you sure you want to change the lock state of "$ideabookName"?';
        confirmText = 'Continue';
        break;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: Text(title, style: textStyles.titleMedium),
          content: Text(content, style: textStyles.bodyMedium),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: textStyles.buttonText.copyWith(
                  color: colors.textSecondary,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                confirmText,
                style: textStyles.buttonText.copyWith(
                  color: colors.textPrimary,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// Gets an appropriate icon for the lock state
  static IconData getLockIcon(bool isLocked) {
    return isLocked ? Icons.lock : Icons.lock_open;
  }

  /// Gets an appropriate color for the lock state
  static Color getLockColor(BuildContext context, bool isLocked) {
    final colors = NoejiTheme.colorsOf(context);
    return isLocked ? colors.error : Colors.green;
  }

  /// Gets a human-readable description for a lock operation
  static String getOperationDescription(LockOperationType type) {
    switch (type) {
      case LockOperationType.lock:
        return 'Locking ideabook...';
      case LockOperationType.unlock:
        return 'Unlocking ideabook...';
      case LockOperationType.toggle:
        return 'Updating lock state...';
    }
  }

  /// Shows a loading indicator for lock operations
  static void showLockOperationLoading(
    BuildContext context,
    LockOperationType type,
  ) {
    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                getOperationDescription(type),
                style: NoejiTheme.textStylesOf(context).bodyMedium,
              ),
            ],
          ),
        );
      },
    );
  }

  /// Hides the loading indicator
  static void hideLockOperationLoading(BuildContext context) {
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Validates if a lock operation can be performed and shows appropriate error messages
  static Future<bool> validateLockOperation({
    required BuildContext context,
    required WidgetRef ref,
    required LockOperation operation,
  }) async {
    try {
      final lockService = ref.read(ideabookLockServiceProvider);
      final result = await lockService.validateLockOperation(operation);

      if (!result.isSuccess) {
        if (context.mounted) {
          await _handleLockResult(context, result);
        }
        return false;
      }

      return true;
    } catch (e) {
      Logger.error('LockUIHelper: Error validating lock operation', e);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unable to validate operation. Please try again.',
              style: NoejiTheme.textStylesOf(context).bodyMedium,
            ),
            backgroundColor: NoejiTheme.colorsOf(context).error,
          ),
        );
      }
      return false;
    }
  }
}
