import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/ideabook_color_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for selecting a color for an ideabook
class ColorPicker extends ConsumerWidget {
  /// The ideabook ID to update
  final String? ideabookId;

  /// The current color of the ideabook
  final IdeabookColor currentColor;

  /// Callback when a color is selected
  final Function(IdeabookColor)? onColorSelected;

  /// Constructor
  const ColorPicker({
    super.key,
    this.ideabookId,
    required this.currentColor,
    this.onColorSelected,
  });

  /// Get the ordered list of colors excluding the current color
  /// Order: purple → blue → green → yellow → orange → red
  List<IdeabookColor> _getOrderedColors() {
    final orderedColors = [
      IdeabookColor.purple,
      IdeabookColor.blue,
      IdeabookColor.green,
      IdeabookColor.yellow,
      IdeabookColor.orange,
      IdeabookColor.red,
    ];

    // Filter out the current color
    return orderedColors.where((color) => color != currentColor).toList();
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final orderedColors = _getOrderedColors();

    // Calculate width for each color (6 total colors including current)
    final colorWidth = screenWidth / 6;

    return GestureDetector(
      // Handle taps outside color squares to close the menu
      onTap: () {
        if (onColorSelected != null) {
          // Call the callback to close the menu
          onColorSelected!(currentColor);
        }
      },
      child: Container(
        width: screenWidth,
        height: double.infinity,
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Row(
          children: [
            // Show the 5 other colors first (left to right)
            ...orderedColors.map((color) {
              return GestureDetector(
                onTap: () {
                  if (ideabookId != null) {
                    // Update the ideabook color using the provider
                    final updateColor = ref.read(updateIdeabookColorProvider);
                    Logger.debug(
                      'Color selected: ${color.name} for ideabook $ideabookId',
                    );
                    updateColor(ideabookId!, color);
                  }

                  // Close the menu after selection
                  if (onColorSelected != null) {
                    onColorSelected!(color);
                  }
                },
                // Prevent tap events from propagating to parent widgets
                behavior: HitTestBehavior.opaque,
                child: Container(
                  width: colorWidth,
                  height: double.infinity,
                  color: NoejiTheme.getIdeabookColor(context, color.index),
                ),
              );
            }),

            // Current color on the rightmost position
            GestureDetector(
              onTap: () {
                // Clicking current color does nothing except close the menu
                if (onColorSelected != null) {
                  onColorSelected!(currentColor);
                }
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: colorWidth,
                height: double.infinity,
                color: NoejiTheme.getIdeabookColor(context, currentColor.index),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
