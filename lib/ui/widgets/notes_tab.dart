import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/combined_note_provider.dart';
import 'package:noeji/ui/providers/firestore_note_listener_controller.dart';
import 'package:noeji/ui/providers/note_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/reorderable_notes_list.dart';
import 'package:noeji/utils/error_utils.dart';

/// Widget for displaying the Notes tab in the ideabook detail screen
class NotesTab extends ConsumerStatefulWidget {
  /// The ideabook to display notes for
  final Ideabook ideabook;

  /// Constructor
  const NotesTab({super.key, required this.ideabook});

  @override
  ConsumerState<NotesTab> createState() => _NotesTabState();
}

/// State for the NotesTab widget
class _NotesTabState extends ConsumerState<NotesTab> {
  // Store the controller reference as a class field
  FirestoreNotesListenerController? _firestoreController;

  @override
  void initState() {
    super.initState();
    // Start listening to Firestore updates when the tab is shown
    // Use a post-frame callback to ensure the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Store the controller reference for later use in dispose
        _firestoreController = ref.read(
          firestoreNotesListenerControllerProvider(widget.ideabook.id).notifier,
        );
        _firestoreController?.startListening();
      }
    });
  }

  @override
  void dispose() {
    // Don't stop the listener when the tab is hidden
    // The listener pool will handle TTL-based cleanup
    // Just clear the reference
    _firestoreController = null;
    super.dispose();
  }

  /// Build loading indicator
  Widget _buildLoadingIndicator(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }

  /// Build error indicator
  Widget _buildErrorIndicator(
    BuildContext context,
    Object error,
    WidgetRef ref,
  ) {
    // Sanitize the error message
    final sanitizedError = ErrorUtils.sanitizeErrorMessage(error);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: NoejiTheme.colorsOf(context).error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading notes',
            style: NoejiTheme.textStylesOf(context).bodyLarge,
          ),
          const SizedBox(height: 8),
          Text(
            sanitizedError,
            style: NoejiTheme.textStylesOf(context).bodySmall,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Refresh the notes using the notifier
              ref
                  .read(
                    ideabookNotesNotifierProvider(widget.ideabook.id).notifier,
                  )
                  .refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get notes for this ideabook using the combined notifier provider for real-time updates
    // This matches the pattern used in the ideas tab
    final notesProvider = ref.watch(
      combinedNotesNotifierProvider(widget.ideabook.id),
    );
    final notesAsync = ref.watch(notesProvider);

    return Scaffold(
      body: notesAsync.when(
        loading: () => _buildLoadingIndicator(context),
        error: (error, _) => _buildErrorIndicator(context, error, ref),
        data: (notes) {
          // Use the reorderable notes list widget
          return ReorderableNotesList(ideabook: widget.ideabook, notes: notes);
        },
      ),
    );
  }
}
