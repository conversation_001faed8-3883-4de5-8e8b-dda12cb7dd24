import 'dart:async';
import 'package:flutter/material.dart';
import 'package:waveform_flutter/waveform_flutter.dart';

/// A widget that displays a waveform visualization using the waveform_flutter package
///
/// This widget can be used in two ways:
/// 1. With a direct amplitude value that changes over time
/// 2. With a stream of amplitude values
class WaveformVisualization extends StatefulWidget {
  /// The current amplitude value (0.0 to 1.0)
  final double? amplitude;

  /// Stream of amplitude values (0.0 to 1.0)
  final Stream<double>? amplitudeStream;

  /// The color of the waveform
  final Color color;

  /// The height of the waveform container
  final double height;

  /// The maximum height of the waveform bars
  final int barMaxHeight;

  /// The sensitivity of the waveform (higher values make small sounds more visible)
  final double sensitivity;

  /// Constructor
  const WaveformVisualization({
    super.key,
    this.amplitude,
    this.amplitudeStream,
    required this.color,
    this.height = 40,
    this.barMaxHeight = 3,
    this.sensitivity = 1.0,
  }) : assert(amplitude != null || amplitudeStream != null,
             'Either amplitude or amplitudeStream must be provided');

  @override
  State<WaveformVisualization> createState() => _WaveformVisualizationState();
}

class _WaveformVisualizationState extends State<WaveformVisualization> {
  // Stream controller for amplitude updates to the waveform package
  late StreamController<Amplitude> _amplitudeController;

  // Subscription to external amplitude stream if provided
  StreamSubscription<double>? _amplitudeStreamSubscription;

  @override
  void initState() {
    super.initState();
    _amplitudeController = StreamController<Amplitude>.broadcast();

    // If using amplitude stream, subscribe to it
    if (widget.amplitudeStream != null) {
      _subscribeToAmplitudeStream();
    } else if (widget.amplitude != null) {
      // Otherwise use the direct amplitude value
      _updateAmplitude(widget.amplitude!);
    }
  }

  @override
  void didUpdateWidget(WaveformVisualization oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle changes in amplitude or stream
    if (widget.amplitudeStream != oldWidget.amplitudeStream) {
      _amplitudeStreamSubscription?.cancel();
      if (widget.amplitudeStream != null) {
        _subscribeToAmplitudeStream();
      }
    } else if (widget.amplitude != oldWidget.amplitude && widget.amplitude != null) {
      _updateAmplitude(widget.amplitude!);
    }
  }

  /// Subscribe to the external amplitude stream
  void _subscribeToAmplitudeStream() {
    _amplitudeStreamSubscription = widget.amplitudeStream!.listen((value) {
      _updateAmplitude(value);
    });
  }

  /// Update the waveform with a new amplitude value
  void _updateAmplitude(double value) {
    // Scale and amplify the value based on sensitivity
    final amplifiedValue = _amplifyAmplitude(value);

    _amplitudeController.add(
      Amplitude(
        current: amplifiedValue,
        max: 100,
      ),
    );
  }

  /// Converts the amplitude value to the range expected by the waveform package
  /// Takes a value between 0.0 and 1.0 and returns a value between 0.0 and 100.0
  double _amplifyAmplitude(double value) {
    // Apply sensitivity factor to make the visualization more responsive
    double amplifiedValue = value * widget.sensitivity;

    // Ensure value stays in 0-1 range after sensitivity is applied
    amplifiedValue = amplifiedValue.clamp(0.0, 1.0);

    // Scale to 0-100 range for the waveform package
    return (amplifiedValue * 100).clamp(0.0, 100.0);
  }

  @override
  void dispose() {
    _amplitudeStreamSubscription?.cancel();
    _amplitudeController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      width: double.infinity,
      child: AnimatedWaveList(
        stream: _amplitudeController.stream,
        barBuilder: (animation, amplitude) {
          return WaveFormBar(
            animation: animation,
            amplitude: amplitude,
            color: widget.color,
            maxHeight: widget.barMaxHeight,
          );
        },
      ),
    );
  }
}


