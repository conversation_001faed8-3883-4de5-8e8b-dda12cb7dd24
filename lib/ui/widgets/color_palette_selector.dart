import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/services/color_palette/color_palette_provider.dart';
import 'package:noeji/services/color_palette/color_palette_service.dart';
import 'package:noeji/services/preferences/color_palette_preference_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

/// Widget for selecting a color palette
class ColorPaletteSelector extends ConsumerWidget {
  const ColorPaletteSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorPalettes = ref.watch(colorPalettesProvider);
    final selectedPaletteName = ref.watch(selectedPaletteNameProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Build palette options
        ...colorPalettes.entries.map((entry) {
          final paletteName = entry.key;
          final palette = entry.value;
          final isSelected = paletteName == selectedPaletteName;

          return _buildPaletteOption(
            context,
            ref,
            paletteName,
            palette,
            isSelected,
          );
        }),
      ],
    );
  }

  Widget _buildPaletteOption(
    BuildContext context,
    WidgetRef ref,
    String paletteName,
    ColorPalette palette,
    bool isSelected,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Update the selected color palette
            ref
                .read(colorPalettePreferenceProvider.notifier)
                .setSelectedColorPalette(paletteName);
            Logger.debug('Selected color palette: $paletteName');
          },
          borderRadius: BorderRadius.circular(4),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Palette name
                Expanded(
                  child: Text(
                    _formatPaletteName(paletteName),
                    style: GoogleFonts.afacad(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: NoejiTheme.colorsOf(context).textPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Color spectrum
                _buildColorSpectrum(context, palette),
                const SizedBox(width: 16),
                // Check mark for selected palette
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: NoejiTheme.colorsOf(context).textPrimary,
                    size: 24,
                  )
                else
                  const SizedBox(
                    width: 24,
                  ), // Placeholder to maintain alignment
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildColorSpectrum(BuildContext context, ColorPalette palette) {
    // Order colors as: red, orange, yellow, green, blue, purple
    final orderedColorNames = [
      'red',
      'orange',
      'yellow',
      'green',
      'blue',
      'purple',
    ];

    return Row(
      mainAxisSize: MainAxisSize.min,
      children:
          orderedColorNames.map((colorName) {
            final color = palette.colors[colorName];
            if (color == null) return const SizedBox.shrink();

            return Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(color: color),
            );
          }).toList(),
    );
  }

  String _formatPaletteName(String paletteName) {
    // Capitalize first letter and replace underscores with spaces
    return paletteName
        .split('_')
        .map(
          (word) =>
              word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1),
        )
        .join(' ');
  }
}
