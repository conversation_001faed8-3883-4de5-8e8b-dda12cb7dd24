import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/audio_recorder_controller.dart';
import 'package:noeji/ui/widgets/audio_recording_waveform.dart';
import 'package:noeji/utils/logger.dart';

/// A widget that displays the audio recording panel with waveform visualization
/// and control buttons
class AudioRecordingPanel extends ConsumerWidget {
  /// Callback when recording is completed
  final Function(String filePath)? onRecordingCompleted;

  /// Callback when recording is cancelled
  final VoidCallback? onRecordingCancelled;

  /// Callback when recording fails
  final Function(String errorMessage)? onRecordingFailed;

  /// Callback when permission is denied
  final VoidCallback? onPermissionDenied;

  /// Constructor
  const AudioRecordingPanel({
    super.key,
    this.onRecordingCompleted,
    this.onRecordingCancelled,
    this.onRecordingFailed,
    this.onPermissionDenied,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AudioRecorderController(
      onRecordingCompleted: (filePath, duration) {
        Logger.debug(
          'AudioRecordingPanel: recording completed, duration: ${duration.inSeconds}s',
        );
        if (onRecordingCompleted != null) {
          onRecordingCompleted!(filePath);
        }
      },
      onRecordingCancelled: onRecordingCancelled,
      onRecordingFailed: onRecordingFailed,
      onPermissionDenied: onPermissionDenied,
      builder: (
        context,
        recordingState,
        amplitude,
        duration,
        onCancel,
        onComplete, {
        bool isInCountdown = false,
        int? countdownSeconds,
      }) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              // Cancel button
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: onCancel,
              ),

              // Waveform visualization with timer or countdown warning
              Expanded(
                child: AudioRecordingWaveform(
                  amplitude: amplitude,
                  duration: duration,
                  showTimer: false,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  showCountdownWarning: isInCountdown,
                  countdownSeconds: countdownSeconds,
                ),
              ),

              // Complete button
              IconButton(
                icon: Icon(
                  Icons.check,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: onComplete,
              ),
            ],
          ),
        );
      },
    );
  }
}
