import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/services/audio/idea_recording_controller.dart';
import 'package:noeji/services/audio/ideabook_edit_recording_controller.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/tour/tour_service.dart';
import 'package:noeji/services/security/passcode_storage.dart';
import 'package:noeji/ui/providers/combined_filter_provider.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';

import 'package:noeji/ui/providers/ideabook_edit_provider.dart';
import 'package:noeji/ui/providers/ideabook_edit_recording_provider.dart';
import 'package:noeji/ui/providers/ideabook_notification_provider.dart';
import 'package:noeji/ui/providers/ideabook_provider.dart';
import 'package:noeji/ui/providers/ideabook_recording_provider.dart';
import 'package:noeji/ui/providers/ideabook_swipe_provider.dart';
import 'package:noeji/ui/screens/ideabook_detail_screen.dart';
import 'package:noeji/ui/screens/passcode_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:noeji/ui/widgets/color_picker.dart';
import 'package:noeji/ui/widgets/common/swipeable_item.dart';
import 'package:noeji/ui/widgets/showcase_tour.dart';
import 'package:noeji/ui/widgets/paywall_handler.dart';
import 'package:noeji/ui/widgets/common/reusable_waveform.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/utils/logger.dart';

/// UI feedback state for press-and-hold recording
enum UIFeedbackState { idle, recording, processing }

/// Widget for displaying an ideabook in the list
class IdeabookListItem extends ConsumerStatefulWidget {
  /// The ideabook to display
  final Ideabook ideabook;

  /// Constructor
  const IdeabookListItem({super.key, required this.ideabook});

  @override
  ConsumerState<IdeabookListItem> createState() => _IdeabookListItemState();
}

class _IdeabookListItemState extends ConsumerState<IdeabookListItem>
    with TickerProviderStateMixin {
  // Text editing controller for edit mode
  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  // Flag to track if we're in recording mode during edit
  bool _isRecordingInEditMode = false;

  // Flag to track if we've initialized the text controller for this edit session
  bool _hasInitializedTextController = false;

  // Recording controller for ideabook name edit
  IdeabookEditRecordingController? _recordingController;

  // Cached max ideas per ideabook limit
  int? _maxIdeasPerIdeabook;

  // Press-and-hold recording state
  bool _isLongPressActive = false;
  bool _isRecording = false;
  String? _currentRecordingPath;

  // UI feedback state for press-and-hold recording
  UIFeedbackState _uiFeedbackState = UIFeedbackState.idle;

  // Recording controller for press-and-hold recording
  IdeaRecordingController? _longPressRecordingController;

  // Recording mode UI state
  double _currentAmplitude = 0.0;
  Timer? _amplitudeUpdateTimer;
  final GlobalKey _cancelAreaKey = GlobalKey();
  bool _isFingerOverCancelArea = false;

  // Swipe constants
  static const double _swipeThreshold = 80.0;
  static const double _maxSwipeExtent =
      170.0; // Maximum swipe distance for context menu

  // Context menu constants
  static const double _iconWidth = 40.0; // Width of each icon
  static const double _iconSpacing =
      16.0; // Spacing between icons for visual separation

  @override
  void initState() {
    super.initState();
    // Initialize the max ideas per ideabook limit
    _initializeMaxIdeasPerIdeabook();
  }

  /// Initialize the maximum ideas per ideabook limit from the provider
  Future<void> _initializeMaxIdeasPerIdeabook() async {
    try {
      _maxIdeasPerIdeabook = await ref.read(maxIdeasPerIdeabookProvider.future);
      Logger.debug(
        'IdeabookListItem: Max ideas per ideabook initialized: $_maxIdeasPerIdeabook',
      );
    } catch (e) {
      Logger.error(
        'IdeabookListItem: Failed to get max ideas per ideabook, using fallback',
        e,
      );
      _maxIdeasPerIdeabook = 10; // Fallback to free tier limit
    }
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _focusNode.dispose();
    _stopAmplitudeUpdates();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use the ideabook passed to the widget
    final currentIdeabook = widget.ideabook;

    // Check if this ideabook is in recording mode
    final recordingId = ref.watch(ideabookRecordingProvider);
    final isRecording = recordingId == currentIdeabook.id;

    // Check if this ideabook is in edit mode
    final editingId = ref.watch(ideabookEditProvider);
    final isEditing = editingId == currentIdeabook.id;

    // Check if this ideabook is in edit recording mode
    final editRecordingId = ref.watch(ideabookEditRecordingProvider);
    final isEditRecording = editRecordingId == currentIdeabook.id;

    // Update local recording state
    if (_isRecordingInEditMode != isEditRecording) {
      if (mounted) {
        try {
          setState(() {
            _isRecordingInEditMode = isEditRecording;
          });
        } catch (e) {
          Logger.debug(
            'setState error during recording state update in build (likely during disposal): $e',
          );
        }
      }
    }

    // Check if this ideabook is showing a notification
    final notification = ref.watch(ideabookNotificationProvider);
    final isShowingNotification =
        notification != null && notification.ideabookId == currentIdeabook.id;

    // Fixed height that can accommodate two lines of text
    const double fixedRowHeight = 72;

    // If in recording mode, show the recording UI
    if (isRecording) {
      return Container(
        height: fixedRowHeight - 1, // Subtract 1 to match normal mode
        color: Theme.of(context).scaffoldBackgroundColor,
        child: _buildRecordingRow(context, currentIdeabook),
      );
    }

    // If in edit mode, show the edit UI
    if (isEditing) {
      return Container(
        height:
            130, // Increased height to accommodate both edit and recording modes
        color: Theme.of(context).scaffoldBackgroundColor,
        child: _buildEditRow(context, currentIdeabook),
      );
    }

    // If showing a notification, show the notification UI
    if (isShowingNotification) {
      return Container(
        height: fixedRowHeight - 1, // Subtract 1 to match normal mode
        color: Theme.of(context).scaffoldBackgroundColor,
        child: _buildNotificationRow(
          context,
          currentIdeabook,
          notification.message,
        ),
      );
    }

    // Use the generic SwipeableItem with bidirectional swipe support
    return SwipeableItem(
      itemId: currentIdeabook.id,
      swipedItemProvider: swipedIdeabookIdProvider,
      leftSwipedItemProvider: leftSwipedIdeabookIdProvider,
      editItemProvider: ideabookEditProvider,
      maxSwipeExtent: _maxSwipeExtent,
      maxLeftSwipeExtent:
          MediaQuery.of(
            context,
          ).size.width, // Use full screen width for color picker
      swipeThreshold: _swipeThreshold,
      showDivider:
          false, // Don't show divider in SwipeableItem to avoid double borders
      contextMenuBuilder:
          (context, totalMenuWidth) =>
              _buildContextMenu(context, currentIdeabook),
      leftSwipeBuilder:
          (context, totalMenuWidth) =>
              _buildColorPickingContent(context, currentIdeabook),
      contentBuilder:
          (context) => SizedBox(
            height: fixedRowHeight - 1, // Subtract 1 for divider
            child: _buildNormalRow(context, currentIdeabook),
          ),
    );
  }

  /// Build the context menu that appears when swiped
  Widget _buildContextMenu(BuildContext context, Ideabook ideabook) {
    // Get the right padding from the main row
    const double rightPadding = 16.0;

    // Calculate the total width needed for the context menu
    final double totalMenuWidth =
        (_iconWidth * 3) + (_iconSpacing * 2) + rightPadding;

    return Container(
      width: totalMenuWidth,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Add idea (pencil) icon (leftmost of the three)
          SizedBox(
            width: _iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.edit,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Enter edit mode for the ideabook
                  ref.read(ideabookEditProvider.notifier).state = ideabook.id;

                  // Close the context menu
                  ref.read(swipedIdeabookIdProvider.notifier).state = null;
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),

          // Spacing
          SizedBox(width: _iconSpacing),

          // Lock/unlock icon (middle)
          SizedBox(
            width: _iconWidth,
            child: Center(
              child: Consumer(
                builder: (context, ref, child) {
                  // Check if ideabooks are still loading
                  final ideabooksState = ref.watch(ideabooksNotifierProvider);
                  final isLoading = ideabooksState is AsyncLoading;

                  return IconButton(
                    icon: Icon(
                      ideabook.isLocked ? Icons.lock : Icons.lock_open_outlined,
                      color:
                          isLoading
                              ? NoejiTheme.colorsOf(context).textDisabled
                              : NoejiTheme.colorsOf(context).textPrimary,
                      size: 24,
                    ),
                    onPressed:
                        isLoading
                            ? null // Disable the button when loading
                            : () {
                              Logger.debug(
                                'IdeabookListItem: Lock/unlock button pressed for ideabook: ${ideabook.id}',
                              );

                              // Check if we're locking or unlocking
                              if (ideabook.isLocked) {
                                // Unlocking - show passcode screen
                                _showPasscodeScreenForLockToggle(
                                  context,
                                  ideabook,
                                );
                              } else {
                                // Locking - check if passcode is already set
                                _handleLockIdeabook(context, ideabook);
                              }

                              // Close the context menu
                              ref
                                  .read(swipedIdeabookIdProvider.notifier)
                                  .state = null;
                            },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  );
                },
              ),
            ),
          ),

          // Spacing
          SizedBox(width: _iconSpacing),

          // Delete icon (rightmost)
          SizedBox(
            width: _iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.delete,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Show confirmation dialog before deleting, regardless of lock state
                  _showDeleteConfirmationDialog(context, ideabook);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),

          // Right padding
          SizedBox(width: rightPadding),
        ],
      ),
    );
  }

  /// Build the normal row display for the ideabook
  Widget _buildNormalRow(BuildContext context, Ideabook ideabook) {
    // Get the ideabooks list to check if this is the first one
    final ideabooksAsync = ref.watch(combinedFilteredIdeabooksProvider);

    // Check if this is the first ideabook in the list
    bool isFirstIdeabook = false;
    if (ideabooksAsync.hasValue && ideabooksAsync.value!.isNotEmpty) {
      isFirstIdeabook = ideabooksAsync.value!.first.id == ideabook.id;
    }

    return Row(
      children: [
        // Color indicator - narrow strip extending to screen edge
        _buildColorIndicator(context, ideabook, isFirstIdeabook),

        // Ideabook name with conditional padding based on recording state
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final longPressToRecordNewIdea = ref.watch(
                longPressToRecordNewIdeaProvider,
              );

              return GestureDetector(
                onTap: () {
                  // Check if this ideabook is in right-to-left swipe mode
                  final swipedId = ref.read(swipedIdeabookIdProvider);
                  final isSwiped = swipedId == ideabook.id;

                  // Check if this ideabook is in left-to-right swipe mode
                  final leftSwipedId = ref.read(leftSwipedIdeabookIdProvider);
                  final isLeftSwiped = leftSwipedId == ideabook.id;

                  if (isSwiped) {
                    // If right-swiped, close the context menu instead of navigating
                    ref.read(swipedIdeabookIdProvider.notifier).state = null;
                  } else if (isLeftSwiped) {
                    // If left-swiped, close the color picker instead of navigating
                    ref.read(leftSwipedIdeabookIdProvider.notifier).state =
                        null;
                  } else {
                    // Check if the ideabook is locked
                    if (ideabook.isLocked) {
                      // Show passcode screen before navigating
                      _showPasscodeScreen(context, ideabook);
                    } else {
                      // Navigate directly to the ideabook detail screen
                      // Pass the ideabook object to avoid unnecessary Firestore reads
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder:
                              (context) => IdeabookDetailScreen(
                                ideabookId: ideabook.id,
                                ideabook:
                                    ideabook, // Pass the ideabook object directly
                              ),
                        ),
                      );
                    }
                  }
                },
                // Press-and-hold-to-record gesture handling
                onLongPressStart:
                    longPressToRecordNewIdea
                        ? (details) {
                          _handleLongPressStart(ideabook);
                        }
                        : null,
                onLongPressMoveUpdate:
                    longPressToRecordNewIdea
                        ? (details) {
                          _handleLongPressMoveUpdate(details);
                        }
                        : null,
                onLongPressEnd:
                    longPressToRecordNewIdea
                        ? (details) {
                          _handleLongPressEnd(ideabook, details);
                        }
                        : null,
                onLongPressCancel:
                    longPressToRecordNewIdea
                        ? () {
                          _handleLongPressCancel(ideabook);
                        }
                        : null,
                child: _buildRowContentWithConditionalPadding(
                  context,
                  ideabook,
                ),
              );
            },
          ),
        ),

        // Microphone button with reduced padding (hidden when long press mode is enabled)
        Consumer(
          builder: (context, ref, child) {
            final longPressToRecordNewIdea = ref.watch(
              longPressToRecordNewIdeaProvider,
            );

            if (longPressToRecordNewIdea) {
              // Hide mic button when long press mode is enabled
              return const SizedBox.shrink();
            }

            return Padding(
              padding: const EdgeInsets.only(right: 16, top: 12, bottom: 12),
              child: SizedBox(
                width: 36, // Reduced width to prevent overlap
                child: _buildMicButton(context, ideabook),
              ),
            );
          },
        ),
      ],
    );
  }

  /// Build the microphone button with showcase for the first ideabook
  Widget _buildMicButton(BuildContext context, Ideabook ideabook) {
    // Get the ideabooks list to check if this is the first one
    final ideabooksAsync = ref.watch(combinedFilteredIdeabooksProvider);

    // Check if this is the first ideabook in the list
    bool isFirstIdeabook = false;
    if (ideabooksAsync.hasValue && ideabooksAsync.value!.isNotEmpty) {
      isFirstIdeabook = ideabooksAsync.value!.first.id == ideabook.id;
    }

    // If this is the first ideabook, wrap it with the showcase
    if (isFirstIdeabook) {
      return Showcase(
        key: ShowcaseKeys.firstIdeabookMicButton,
        description:
            'Welcome to your first Ideabook. Tap 🎙️ to capture ideas into it. 📚',
        targetShapeBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
          side: BorderSide(
            color: NoejiTheme.colorsOf(context).border,
            width: 1,
          ),
        ),
        tooltipPadding: const EdgeInsets.all(16.0),
        tooltipBackgroundColor: NoejiTheme.colorsOf(context).tooltipBackground,
        textColor: NoejiTheme.colorsOf(context).textPrimary,
        descTextStyle: GoogleFonts.afacad(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: NoejiTheme.colorsOf(context).textPrimary,
        ),
        child: _buildMicButtonContent(context, ideabook),
      );
    } else {
      // For all other ideabooks, just show the button without showcase
      return _buildMicButtonContent(context, ideabook);
    }
  }

  /// Build the color indicator with showcase for the first ideabook
  Widget _buildColorIndicator(
    BuildContext context,
    Ideabook ideabook,
    bool isFirstIdeabook,
  ) {
    // Create the color indicator widget - narrow strip extending full height
    Widget colorIndicator = Container(
      width: 8, // Much narrower strip
      height: double.infinity, // Full height of the row
      color: NoejiTheme.getIdeabookColor(context, ideabook.color.index),
      // No border decoration
    );

    // If this is the first ideabook, wrap it with the showcase
    if (isFirstIdeabook) {
      return TourService.createShowcaseWidget(
        context: context,
        key: ShowcaseKeys.ideabookColorSystemTour,
        description:
            'Pull out to change color. 🌈 Use colors to categorize your ideabooks. 📚',
        child: colorIndicator,
      );
    } else {
      // For all other ideabooks, just show the color indicator without showcase
      return colorIndicator;
    }
  }

  /// Build the actual mic button content
  Widget _buildMicButtonContent(BuildContext context, Ideabook ideabook) {
    return IconButton(
      icon: Icon(Icons.mic, color: NoejiTheme.colorsOf(context).textPrimary),
      onPressed: () async {
        // Check if this ideabook is in right-to-left swipe mode
        final swipedId = ref.read(swipedIdeabookIdProvider);
        final isSwiped = swipedId == ideabook.id;

        // Check if this ideabook is in left-to-right swipe mode
        final leftSwipedId = ref.read(leftSwipedIdeabookIdProvider);
        final isLeftSwiped = leftSwipedId == ideabook.id;

        if (isSwiped) {
          // If right-swiped, close the context menu but continue to recording mode
          ref.read(swipedIdeabookIdProvider.notifier).state = null;
          // Don't return here, continue to recording mode
        } else if (isLeftSwiped) {
          // If left-swiped, close the color picker but continue to recording mode
          ref.read(leftSwipedIdeabookIdProvider.notifier).state = null;
          // Don't return here, continue to recording mode
        }

        // Check ideas limit and handle paywall if needed
        try {
          // Check if the ideabook is full
          final ideaRepository = ref.read(ideaRepositoryProvider);
          final isFull = await ideaRepository.isIdeabookFull(ideabook.id);

          if (isFull) {
            // Create a fake exception to trigger paywall handling
            final limitsHelper = ref.read(repositoryLimitsHelperProvider);
            await limitsHelper.throwIdeasLimitException();
          }
        } catch (e) {
          if (e is IdeasLimitException) {
            // Handle the limit exception with paywall logic
            if (!mounted) return;

            // Use a post-frame callback to handle the paywall after the current frame
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              if (!mounted) return;

              final shouldContinue = await PaywallHandler.handleLimitException(
                context: context,
                ref: ref,
                exception: e,
              );

              // If user upgraded, continue with recording
              if (shouldContinue && mounted) {
                // Continue with the recording flow
                _continueWithRecording(ideabook);
              }
            });
            return;
          } else {
            // Handle other exceptions
            Logger.error('Unexpected error checking ideas limit', e);
            return;
          }
        }

        // Continue with recording if no limit was hit
        _continueWithRecording(ideabook);
      },
      padding: EdgeInsets.zero, // Remove default padding
      constraints: const BoxConstraints(), // Remove default constraints
    );
  }

  /// Build the color picking content that appears when swiped left-to-right
  Widget _buildColorPickingContent(BuildContext context, Ideabook ideabook) {
    // Use full screen width for the color picker to show all colors
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: ColorPicker(
        ideabookId: ideabook.id,
        currentColor: ideabook.color,
        onColorSelected: (color) {
          // Close the left swipe when color is selected
          ref.read(leftSwipedIdeabookIdProvider.notifier).state = null;
        },
      ),
    );
  }

  /// Build the notification row display
  Widget _buildNotificationRow(
    BuildContext context,
    Ideabook ideabook,
    String message,
  ) {
    return Row(
      children: [
        // Color indicator - narrow strip
        Container(
          width: 8, // Match the narrow strip width
          height: double.infinity, // Full height of the row
          color: NoejiTheme.getIdeabookColor(context, ideabook.color.index),
          // No border decoration
        ),

        // Notification content with padding
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Notification message
                Expanded(
                  child: Text(
                    message,
                    style: NoejiTheme.textStylesOf(
                      context,
                    ).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),

                // Check icon to indicate success
                Icon(
                  Icons.check_circle,
                  color: NoejiTheme.getIdeabookColor(
                    context,
                    IdeabookColor.green.index,
                  ),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build the edit row display
  Widget _buildEditRow(BuildContext context, Ideabook ideabook) {
    Logger.debug(
      'Building edit row for ideabook: ${ideabook.id}, current text: "${_textEditingController.text}", initialized: $_hasInitializedTextController',
    );
    // Initialize the text controller with the current name ONLY when first entering edit mode
    // We track this with a static variable to avoid resetting the text after recording
    if (!_hasInitializedTextController) {
      _textEditingController.text = ideabook.name;
      // Position cursor at the end of the text
      _textEditingController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textEditingController.text.length),
      );
      _hasInitializedTextController = true;
      Logger.debug(
        'Initialized text controller with ideabook name: ${ideabook.name}',
      );
    }

    // Request focus when entering edit mode
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(ideabookEditProvider) == ideabook.id) {
        _focusNode.requestFocus();
      }
    });

    // Add a listener to handle clicks outside the text field
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        // Get the trimmed text
        final newName = _textEditingController.text.trim();

        // Only save changes when focus is lost if the content has actually changed
        if (newName.isNotEmpty && newName != ideabook.name) {
          _saveChanges(ideabook);
        } else {
          // If no changes, just exit edit mode without saving or showing notification
          ref.read(ideabookEditProvider.notifier).state = null;
          // Reset the initialization flag when exiting edit mode
          _hasInitializedTextController = false;
        }
      }
    });

    // If in recording mode, show the recording UI
    if (_isRecordingInEditMode) {
      return _buildEditRecordingRow(context, ideabook);
    }

    // Use the original two-row layout with more space
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Top row with text field - maintain same layout as normal row
        Row(
          children: [
            // Color indicator (non-clickable in edit mode) - narrow strip
            Container(
              width: 8, // Match the narrow strip width
              height: 48, // Fixed height for edit mode
              color: NoejiTheme.getIdeabookColor(context, ideabook.color.index),
              // No border decoration
            ),

            // Text field for editing the name with padding
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  key: ValueKey(
                    'ideabook_edit_normal_${ideabook.id}_${_textEditingController.text.length}',
                  ),
                  controller: _textEditingController,
                  focusNode: _focusNode,
                  style: NoejiTheme.textStylesOf(context).ideabookName,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLines: 1,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) {
                    // Get the trimmed text
                    final newName = _textEditingController.text.trim();

                    // Only save changes when Enter is pressed if the content has actually changed
                    if (newName.isNotEmpty && newName != ideabook.name) {
                      _saveChanges(ideabook);
                    } else {
                      // If no changes, just exit edit mode without saving or showing notification
                      ref.read(ideabookEditProvider.notifier).state = null;
                    }
                  },
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12), // Reduced spacing from 16 to 12
        // Buttons row with padding to align with content
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
          ), // 8 (color strip) + 16 (content padding)
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side - Mic button
              IconButton(
                icon: Icon(
                  Icons.mic,
                  color: NoejiTheme.colorsOf(context).textPrimary,
                ),
                onPressed: () => _startRecordingInEditMode(ideabook),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Right side - Cancel and Save buttons
              Row(
                children: [
                  // Cancel button
                  TextButton(
                    onPressed: () {
                      // Exit edit mode without saving
                      ref.read(ideabookEditProvider.notifier).state = null;
                      // Reset the initialization flag when exiting edit mode
                      _hasInitializedTextController = false;
                    },
                    child: Text(
                      'Cancel',
                      style: TextStyle(
                        color: NoejiTheme.colorsOf(context).textPrimary,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Save button
                  TextButton(
                    onPressed: () {
                      // Get the trimmed text
                      final newName = _textEditingController.text.trim();

                      // Only save changes when save button is pressed if the content has actually changed
                      if (newName.isNotEmpty && newName != ideabook.name) {
                        _saveChanges(ideabook);
                        // Reset the initialization flag when saving changes
                        _hasInitializedTextController = false;
                      } else {
                        // If no changes, just exit edit mode without saving or showing notification
                        ref.read(ideabookEditProvider.notifier).state = null;
                        // Reset the initialization flag when exiting edit mode
                        _hasInitializedTextController = false;
                      }
                    },
                    child: Text(
                      'Save',
                      style: TextStyle(
                        color: NoejiTheme.colorsOf(context).textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Start voice recording in edit mode
  void _startRecordingInEditMode(Ideabook ideabook) async {
    // Check if microphone permission is granted
    final recordingService = ref.read(audioRecordingServiceProvider);
    final hasPermission = await recordingService.checkPermission();

    if (hasPermission) {
      // Enter recording mode
      _startRecordingWithController(ideabook);
      return;
    }

    // Request permission if not granted
    final status = await recordingService.requestPermission();
    if (status.isGranted) {
      // Enter recording mode
      _startRecordingWithController(ideabook);
    } else {
      // Show permission denied dialog
      if (mounted) {
        // Use a post-frame callback to show the dialog after the current frame is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            showDialog(
              context: context,
              builder:
                  (dialogContext) => AlertDialog(
                    title: Text(
                      'Microphone Permission Required',
                      style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
                    ),
                    content: Text(
                      'Microphone permission is required to record audio. '
                      'Please enable it in app settings.',
                      style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.zero,
                      side: BorderSide(
                        color: NoejiTheme.colorsOf(dialogContext).border,
                        width: 1,
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        child: Text(
                          'OK',
                          style:
                              NoejiTheme.textStylesOf(dialogContext).buttonText,
                        ),
                      ),
                    ],
                  ),
            );
          }
        });
      }
    }
  }

  /// Safely use the ref to avoid errors when the widget is disposed
  T? safelyUseRef<T>(WidgetRef ref, T Function(WidgetRef) callback) {
    try {
      return callback(ref);
    } catch (e) {
      Logger.debug('Error using ref: $e');
      return null;
    }
  }

  /// Start recording with controller
  void _startRecordingWithController(Ideabook ideabook) {
    Logger.debug('Starting recording for ideabook edit: ${ideabook.id}');

    // Cancel any existing recording first
    if (_recordingController != null) {
      _recordingController = null;
    }

    // Update state to indicate we're in recording mode
    if (mounted) {
      try {
        setState(() {
          _isRecordingInEditMode = true;
        });
      } catch (e) {
        Logger.debug(
          'setState error during recording start (likely during disposal): $e',
        );
        return;
      }
    } else {
      return;
    }

    // Enter recording mode in the provider
    safelyUseRef(
      ref,
      (r) => r.read(ideabookEditRecordingProvider.notifier).state = ideabook.id,
    );

    // Create a recording controller and store it for reuse
    _recordingController = IdeabookEditRecordingController(
      ideabookId: ideabook.id,
      onTranscriptionComplete: _handleTranscriptionComplete,
    );

    // No need to start recording here - the AdvancedAudioRecordingPanel will handle it
    Logger.debug('Recording controller created for ideabook: ${ideabook.id}');
  }

  /// Handle transcription completion
  void _handleTranscriptionComplete(String transcribedText) {
    Logger.debug('Transcription complete: $transcribedText');

    // Get the current text and cursor position
    final currentText = _textEditingController.text;
    final cursorPosition = _textEditingController.selection.baseOffset;

    // Calculate the new text and cursor position
    String newText;
    int newCursorPosition;

    if (cursorPosition >= 0) {
      // Insert at cursor position
      newText =
          currentText.substring(0, cursorPosition) +
          transcribedText +
          currentText.substring(cursorPosition);
      newCursorPosition = cursorPosition + transcribedText.length;
    } else {
      // Append to the end if no cursor position
      newText = currentText + transcribedText;
      newCursorPosition = newText.length;
    }

    Logger.debug(
      'Current text: "$currentText", Cursor position: $cursorPosition',
    );
    Logger.debug(
      'New text will be: "$newText", New cursor position: $newCursorPosition',
    );

    // Update the text field and force a rebuild
    Logger.debug('Setting text field value to: "$newText"');
    if (mounted) {
      try {
        setState(() {
          _textEditingController.text = newText;

          // Set cursor position after the inserted text
          _textEditingController.selection = TextSelection.fromPosition(
            TextPosition(offset: newCursorPosition),
          );

          // Exit recording mode
          _isRecordingInEditMode = false;
          _recordingController = null;

          Logger.debug(
            'Text field value after setState: "${_textEditingController.text}"',
          );
        });
      } catch (e) {
        Logger.debug(
          'setState error during transcription complete (likely during disposal): $e',
        );
        return;
      }
    } else {
      return;
    }

    // Schedule focus request after the state update is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Re-focus the text field to allow immediate editing
      _focusNode.requestFocus();

      // Log the action
      Logger.debug(
        'Transcribed text inserted at cursor position. User can now edit and save manually.',
      );
    });
  }

  /// Build the recording panel for edit mode
  Widget _buildEditRecordingRow(BuildContext context, Ideabook ideabook) {
    Logger.debug(
      'Building edit recording row for ideabook: ${ideabook.id}, current text: "${_textEditingController.text}"',
    );
    // Use the original two-row layout with more space
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Top row with text field - maintain same layout as normal row
        Row(
          children: [
            // Color indicator (non-clickable in edit mode) - narrow strip
            Container(
              width: 8, // Match the narrow strip width
              height: 48, // Fixed height for edit mode
              color: NoejiTheme.getIdeabookColor(context, ideabook.color.index),
              // No border decoration
            ),
            // Text field for editing the name - keep enabled to preserve cursor position
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  key: ValueKey(
                    'ideabook_edit_${ideabook.id}_${_textEditingController.text.length}',
                  ),
                  controller: _textEditingController,
                  focusNode: _focusNode,
                  style: NoejiTheme.textStylesOf(context).ideabookName,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLines: 1,
                  enabled: true, // Keep enabled to preserve cursor position
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12), // Reduced spacing from 16 to 12
        // Recording panel row
        AdvancedAudioRecordingPanel(
          layout: AudioRecordingPanelLayout.horizontal,
          showTimer: false,
          onRecordingCompleted: (filePath, duration) async {
            Logger.debug(
              'Ideabook edit recording completed: $filePath, duration: ${duration.inSeconds}s',
            );

            // Manually call the recording controller's handleRecordingCompleted method
            if (_recordingController != null) {
              await _recordingController!.handleRecordingCompleted(
                ref,
                filePath,
                duration,
              );
            } else {
              Logger.error(
                'Recording controller is null when recording completed',
              );
              // Show error message
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Error processing recording'),
                  duration: Duration(seconds: 2),
                ),
              );

              // Exit recording mode
              ref.read(ideabookEditRecordingProvider.notifier).state = null;
            }
          },
          onRecordingCancelled: () {
            Logger.debug('Ideabook edit recording cancelled');

            // Call the recording controller's handleRecordingCancelled method
            if (_recordingController != null) {
              _recordingController!.handleRecordingCancelled(ref);
            } else {
              // Exit recording mode directly if controller is null
              ref.read(ideabookEditRecordingProvider.notifier).state = null;
            }
          },
          onRecordingFailed: (errorMessage) {
            Logger.error('Ideabook edit recording failed: $errorMessage');

            // Call the recording controller's handleRecordingFailed method
            if (_recordingController != null) {
              _recordingController!.handleRecordingFailed(ref, errorMessage);
            } else {
              // Show error message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Recording failed: $errorMessage'),
                  duration: const Duration(seconds: 2),
                ),
              );

              // Exit recording mode
              ref.read(ideabookEditRecordingProvider.notifier).state = null;
            }
          },
          onPermissionDenied: () {
            Logger.error('Ideabook edit recording permission denied');

            // Call the recording controller's handlePermissionDenied method
            if (_recordingController != null) {
              _recordingController!.handlePermissionDenied(ref);
            } else {
              // Show permission dialog
              showDialog(
                context: context,
                builder:
                    (context) => AlertDialog(
                      title: const Text('Microphone Permission Required'),
                      content: const Text(
                        'Microphone permission is required to record audio. '
                        'Please enable it in app settings.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            openAppSettings();
                          },
                          child: const Text('Open Settings'),
                        ),
                      ],
                    ),
              );

              // Exit recording mode
              ref.read(ideabookEditRecordingProvider.notifier).state = null;
            }
          },
        ),
      ],
    );
  }

  /// Save the edited ideabook name
  /// This method is only called when we've already verified that:
  /// 1. The name has changed
  /// 2. The new name is not empty
  Future<void> _saveChanges(Ideabook ideabook) async {
    final newName = _textEditingController.text.trim();

    // Get the maximum ideabook name length from the provider
    int maxNameLength = 100; // Fallback to free tier limit
    try {
      maxNameLength = await ref.read(ideabookNameMaxWordsProvider.future);
    } catch (e) {
      Logger.error('Failed to get max ideabook name length, using fallback', e);
    }

    // Check if name is too long
    if (newName.length > maxNameLength) {
      // Show error notification
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Ideabook name must be $maxNameLength characters or less',
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
      // Don't exit edit mode or save changes
      return;
    }

    // Create updated ideabook
    final updatedIdeabook = ideabook.copyWith(
      name: newName,
      updatedAt: DateTime.now(),
    );

    // Update the ideabook
    ref.read(ideabooksNotifierProvider.notifier).updateIdeabook(updatedIdeabook).then((
      success,
    ) {
      // Only show notification if the update was successful and the widget is still mounted
      // The FirestoreService will return success=true even if no update was needed
      // (when content is the same), but in that case we don't want to show a notification
      if (success && mounted) {
        // Show notification as a SnackBar (breadcrumb style)
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Ideabook name updated'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    });

    // Exit edit mode
    ref.read(ideabookEditProvider.notifier).state = null;

    // Reset the initialization flag when exiting edit mode
    _hasInitializedTextController = false;
  }

  /// Build the recording row display
  Widget _buildRecordingRow(BuildContext context, Ideabook ideabook) {
    // Create a recording controller for this ideabook
    final recordingController = ref.read(
      ideaRecordingControllerProvider(ideabook.id),
    );

    return Row(
      children: [
        // Color indicator - narrow strip
        Container(
          width: 8, // Match the narrow strip width
          height: double.infinity, // Full height of the row
          color: NoejiTheme.getIdeabookColor(context, ideabook.color.index),
          // No border decoration
        ),

        // Recording panel with padding
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Center(
              child: AdvancedAudioRecordingPanel(
                layout: AudioRecordingPanelLayout.compact,
                showTimer: false,
                onRecordingCompleted: (filePath, duration) async {
                  // Let the controller handle the recording completion
                  // This will handle transcription, idea creation, and notifications
                  await recordingController.handleRecordingCompleted(
                    ref,
                    filePath,
                    duration,
                  );
                },
                onRecordingCancelled: () {
                  // Let the controller handle the recording cancellation
                  recordingController.handleRecordingCancelled(ref);
                },
                onRecordingFailed: (errorMessage) {
                  // Let the controller handle the recording failure
                  recordingController.handleRecordingFailed(ref, errorMessage);
                },
                onPermissionDenied: () {
                  // Let the controller handle the permission denial
                  recordingController.handlePermissionDenied(ref);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Show the passcode screen for navigating to a locked ideabook
  void _showPasscodeScreen(BuildContext context, Ideabook ideabook) {
    // Store local references before any async operations
    final currentContext = context;

    // Use an async function to handle the passcode check
    _checkPasscodeAndShowScreen(ideabook, currentContext);
  }

  /// Helper method to check passcode and show appropriate screen
  Future<void> _checkPasscodeAndShowScreen(
    Ideabook ideabook,
    BuildContext context,
  ) async {
    Logger.debug(
      'Checking passcode for ideabook: ${ideabook.id}, isLocked: ${ideabook.isLocked}',
    );

    final isPasscodeSet = await PasscodeStorage.isPasscodeSet();
    Logger.debug('Passcode is set: $isPasscodeSet');

    // Check if widget is still mounted before showing UI
    if (!mounted) return;

    // If the ideabook is locked but no passcode is set, show an error message
    if (ideabook.isLocked && !isPasscodeSet) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Cannot access locked ideabook: No passcode is set',
              style: NoejiTheme.textStylesOf(context).bodyMedium,
            ),
            duration: const Duration(seconds: 2),
            backgroundColor: NoejiTheme.colorsOf(context).error,
          ),
        );
      }
      return;
    }

    // Show the appropriate passcode screen
    if (mounted && context.mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        builder:
            (bottomSheetContext) => SizedBox(
              height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
              child: PasscodeScreen(
                ideabook: ideabook,
                isAccessingDetail: true, // This is for accessing detail page
                // Force validation mode since we know a passcode is set (we checked above)
                onSuccess: () {
                  // Close the passcode screen FIRST to prevent rebuilds
                  Navigator.of(bottomSheetContext).pop();

                  // Use a post-frame callback to handle navigation after the modal is closed
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted && context.mounted) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder:
                              (ctx) => IdeabookDetailScreen(
                                ideabookId: ideabook.id,
                                ideabook:
                                    ideabook, // Pass the ideabook object directly
                              ),
                        ),
                      );
                    }
                  });
                },
                onCancel: () {
                  Navigator.of(
                    bottomSheetContext,
                  ).pop(); // Close the passcode screen
                },
              ),
            ),
      );
    }
  }

  /// Show the passcode screen for toggling lock state
  void _showPasscodeScreenForLockToggle(
    BuildContext context,
    Ideabook ideabook,
  ) {
    // Store local references before any async operations
    final currentContext = context;
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Use an async function to handle the passcode validation
    _validatePasscodeForLockToggle(ideabook, currentContext, scaffoldMessenger);
  }

  /// Helper method to validate passcode for lock toggle
  Future<void> _validatePasscodeForLockToggle(
    Ideabook ideabook,
    BuildContext context,
    ScaffoldMessengerState scaffoldMessenger,
  ) async {
    // Check if widget is still mounted
    if (!mounted) return;

    // Add logging to track the flow
    Logger.debug(
      'IdeabookListItem: Validating passcode for lock toggle, ideabook: ${ideabook.id}, isLocked: ${ideabook.isLocked}',
    );

    // Capture all necessary references BEFORE showing the modal
    final snackBarTextColor = NoejiTheme.getSnackBarTextColor(context);
    final textStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: snackBarTextColor);
    final ideabooksNotifier = ref.read(ideabooksNotifierProvider.notifier);

    // For unlocking, we need to validate the passcode
    if (context.mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        builder: (bottomSheetContext) {
          return SizedBox(
            height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            child: PasscodeScreen(
              ideabook: ideabook,
              isUnlocking:
                  ideabook.isLocked, // If already locked, we're unlocking
              isLocking: !ideabook.isLocked, // If not locked, we're locking
              onSuccess: () {
                // Add logging to track the callback execution
                Logger.debug(
                  'IdeabookListItem: onSuccess callback called for unlocking ideabook: ${ideabook.id}',
                );

                Logger.debug(
                  'IdeabookListItem: About to close modal and toggle lock state for ideabook: ${ideabook.id}',
                );

                // Close the passcode screen FIRST to prevent rebuilds
                Navigator.of(bottomSheetContext).pop();

                // Execute the toggle immediately instead of using post-frame callback
                Logger.debug(
                  'IdeabookListItem: Calling toggleIdeabookLock for ideabook: ${ideabook.id}',
                );

                // Update the ideabook lock state
                ideabooksNotifier
                    .toggleIdeabookLock(ideabook.id)
                    .then((success) {
                      Logger.debug(
                        'IdeabookListItem: toggleIdeabookLock completed with result: $success',
                      );

                      if (mounted && scaffoldMessenger.mounted) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              ideabook.isLocked
                                  ? 'Ideabook unlocked'
                                  : 'Ideabook locked',
                              style: textStyle,
                            ),
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      }
                    })
                    .catchError((error) {
                      Logger.error(
                        'IdeabookListItem: Error in toggleIdeabookLock: $error',
                      );
                    });
              },
              onCancel: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
            ),
          );
        },
      );
    }
  }

  /// Handle locking an ideabook
  void _handleLockIdeabook(BuildContext context, Ideabook ideabook) {
    Logger.debug(
      'IdeabookListItem: _handleLockIdeabook called for ideabook: ${ideabook.id}',
    );

    // Store a reference to the widget's context before showing the dialog
    final widgetContext = this.context;

    // Show confirmation dialog first
    showDialog<bool>(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text(
              'Lock Ideabook',
              style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
            ),
            content: Text(
              'Are you sure you want to lock this ideabook? '
              'If you forget your passcode, it cannot be recovered.',
              style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.zero,
              side: BorderSide(
                color: NoejiTheme.colorsOf(dialogContext).border,
                width: 1,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Logger.debug(
                    'IdeabookListItem: Lock confirmation dialog - Cancel pressed',
                  );
                  Navigator.of(dialogContext).pop(false);
                },
                child: Text(
                  'Cancel',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
              TextButton(
                onPressed: () {
                  Logger.debug(
                    'IdeabookListItem: Lock confirmation dialog - Lock pressed',
                  );
                  Navigator.of(dialogContext).pop(true);
                },
                child: Text(
                  'Lock',
                  style: NoejiTheme.textStylesOf(dialogContext).buttonText,
                ),
              ),
            ],
          ),
    ).then((confirmLock) {
      Logger.debug(
        'IdeabookListItem: Lock confirmation dialog result: $confirmLock',
      );

      // If user cancelled, return
      if (confirmLock != true) {
        Logger.debug('IdeabookListItem: Lock confirmation cancelled');
        return;
      }

      Logger.debug(
        'IdeabookListItem: Lock confirmation accepted, proceeding with lock',
      );
      Logger.debug(
        'IdeabookListItem: Widget mounted: $mounted, Widget context mounted: ${widgetContext.mounted}',
      );

      // Use a post-frame callback to ensure we're not in the middle of a build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Check again after the frame using the widget's context
        Logger.debug(
          'IdeabookListItem: Post-frame - Widget mounted: $mounted, Widget context mounted: ${widgetContext.mounted}',
        );

        if (mounted && widgetContext.mounted) {
          _handleLockIdeabookAfterConfirmation(ideabook, widgetContext);
        } else {
          Logger.debug(
            'IdeabookListItem: Widget context or widget not mounted after post-frame, cannot proceed with lock',
          );
        }
      });
    });
  }

  /// Handle the async operations after lock confirmation
  Future<void> _handleLockIdeabookAfterConfirmation(
    Ideabook ideabook,
    BuildContext context,
  ) async {
    Logger.debug(
      'IdeabookListItem: _handleLockIdeabookAfterConfirmation called for ideabook: ${ideabook.id}',
    );

    // Check if the widget is still mounted
    if (!mounted) {
      Logger.debug('IdeabookListItem: Widget not mounted, returning early');
      return;
    }

    // Get a fresh ScaffoldMessenger reference
    ScaffoldMessengerState? scaffoldMessenger;
    if (mounted && context.mounted) {
      scaffoldMessenger = ScaffoldMessenger.of(context);
      Logger.debug('IdeabookListItem: ScaffoldMessenger obtained successfully');
    } else {
      Logger.debug(
        'IdeabookListItem: Cannot get ScaffoldMessenger - widget mounted: $mounted, context mounted: ${context.mounted}',
      );
    }

    Logger.debug(
      'IdeabookListItem: Handling lock ideabook confirmation for ideabook: ${ideabook.id}',
    );

    Logger.debug('IdeabookListItem: Checking if passcode is set...');
    final isPasscodeSet = await PasscodeStorage.isPasscodeSet();
    Logger.debug('IdeabookListItem: Passcode is set: $isPasscodeSet');

    // Add additional logging to track the flow
    Logger.debug(
      'IdeabookListItem: Current ideabook lock state: ${ideabook.isLocked}',
    );

    // Check again if widget is still mounted
    if (!mounted) {
      Logger.debug(
        'IdeabookListItem: Widget not mounted after passcode check, returning early',
      );
      return;
    }

    // Capture all necessary references BEFORE showing the modal
    final lockSnackBarTextColor = NoejiTheme.getSnackBarTextColor(context);
    final lockTextStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: lockSnackBarTextColor);
    final lockIdeabooksNotifier = ref.read(ideabooksNotifierProvider.notifier);

    // Show the appropriate passcode screen based on whether a passcode is set
    if (mounted && context.mounted) {
      Logger.debug(
        'IdeabookListItem: Showing passcode screen modal bottom sheet',
      );
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        builder: (bottomSheetContext) {
          return SizedBox(
            height: MediaQuery.of(bottomSheetContext).size.height * 0.9,
            child: PasscodeScreen(
              ideabook: ideabook,
              isLocking: true, // We're locking the ideabook
              onSuccess: () {
                // Add logging to track the callback execution
                Logger.debug(
                  'IdeabookListItem: onSuccess callback called for locking ideabook: ${ideabook.id}',
                );

                Logger.debug(
                  'IdeabookListItem: About to close modal and toggle lock state for ideabook: ${ideabook.id}',
                );

                // Close the passcode screen FIRST to prevent rebuilds
                Navigator.of(bottomSheetContext).pop();

                // Execute the toggle immediately instead of using post-frame callback
                Logger.debug(
                  'IdeabookListItem: Calling toggleIdeabookLock for ideabook: ${ideabook.id}',
                );

                // Update the ideabook lock state
                lockIdeabooksNotifier
                    .toggleIdeabookLock(ideabook.id)
                    .then((success) {
                      Logger.debug(
                        'IdeabookListItem: toggleIdeabookLock completed with result: $success',
                      );

                      if (mounted &&
                          scaffoldMessenger != null &&
                          scaffoldMessenger.mounted) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              'Ideabook locked',
                              style: lockTextStyle,
                            ),
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      }
                    })
                    .catchError((error) {
                      Logger.error(
                        'IdeabookListItem: Error in toggleIdeabookLock: $error',
                      );
                    });
              },
              onCancel: () {
                Navigator.of(
                  bottomSheetContext,
                ).pop(); // Close the passcode screen
              },
            ),
          );
        },
      );
    } else {
      Logger.debug(
        'IdeabookListItem: Cannot show passcode screen - widget mounted: $mounted, context mounted: ${context.mounted}',
      );
    }
  }

  /// Show a confirmation dialog before deleting an ideabook
  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    Ideabook ideabook,
  ) async {
    // Store local references before any async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final errorColor = NoejiTheme.colorsOf(context).error;

    // Get appropriate text styles for regular and error messages
    final snackBarTextColor = NoejiTheme.getSnackBarTextColor(context);
    final errorSnackBarTextColor = NoejiTheme.getSnackBarTextColor(
      context,
      backgroundColor: errorColor,
    );

    final textStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: snackBarTextColor);
    final errorTextStyle = NoejiTheme.textStylesOf(
      context,
    ).bodyMedium.copyWith(color: errorSnackBarTextColor);

    // Show confirmation dialog
    final bool? confirmDelete = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Delete Ideabook',
            style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to delete "${ideabook.name}"?',
            style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: NoejiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Delete',
                style: NoejiTheme.textStylesOf(dialogContext).buttonText
                    .copyWith(color: NoejiTheme.colorsOf(dialogContext).error),
              ),
            ),
          ],
        );
      },
    );

    // Check if the widget is still mounted after the dialog
    if (!mounted) return;

    // If user confirmed deletion
    if (confirmDelete == true) {
      // Delete the ideabook
      try {
        Logger.debug('Using Firestore provider for ideabook deletion');

        // Delete the ideabook using Firestore service directly
        final firestoreService = ref.read(firestoreServiceProvider);
        final success = await firestoreService.deleteIdeabook(ideabook.id);

        // Check if widget is still mounted before showing snackbar
        if (mounted && success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Ideabook deleted', style: textStyle),
              duration: const Duration(seconds: 1),
            ),
          );
        } else if (mounted && !success) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Failed to delete ideabook', style: errorTextStyle),
              duration: const Duration(seconds: 2),
              backgroundColor: errorColor,
            ),
          );
        }
      } catch (e) {
        Logger.error('Error deleting ideabook', e);
        // Show error message if deletion fails
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'Failed to delete ideabook: ${e.toString()}',
                style: errorTextStyle,
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: errorColor,
            ),
          );
        }
      }
    }

    // Close the context menu regardless of deletion confirmation
    if (mounted) {
      ref.read(swipedIdeabookIdProvider.notifier).state = null;
    }
  }

  /// Build the ideabook name text with visual feedback for recording state
  Widget _buildIdeabookNameText(BuildContext context, Ideabook ideabook) {
    // Apply visual feedback based on recording state
    Color? backgroundColor;
    if (_uiFeedbackState == UIFeedbackState.recording) {
      backgroundColor = Colors.red.withValues(alpha: 0.1);
    } else if (_uiFeedbackState == UIFeedbackState.processing) {
      backgroundColor = Colors.orange.withValues(alpha: 0.1);
    }

    return Container(
      decoration:
          backgroundColor != null
              ? BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(4),
              )
              : null,
      padding:
          backgroundColor != null
              ? const EdgeInsets.symmetric(horizontal: 4, vertical: 2)
              : null,
      child: Text(
        ideabook.name,
        style: NoejiTheme.textStylesOf(context).ideabookName,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Handle long press start - begin recording
  Future<void> _handleLongPressStart(Ideabook ideabook) async {
    Logger.debug('Long press start for ideabook: ${ideabook.id}');

    // Set long press active flag
    if (mounted) {
      try {
        setState(() {
          _isLongPressActive = true;
          _uiFeedbackState = UIFeedbackState.recording;
        });
      } catch (e) {
        Logger.debug(
          'setState error during long press start (likely during disposal): $e',
        );
        return;
      }
    } else {
      return;
    }

    // Provide strong haptic feedback for recording start
    HapticFeedback.heavyImpact();

    // Check ideas limit and handle paywall if needed
    try {
      // Check if the ideabook is full
      final ideaRepository = ref.read(ideaRepositoryProvider);
      final isFull = await ideaRepository.isIdeabookFull(ideabook.id);

      if (isFull) {
        // Create a fake exception to trigger paywall handling
        final limitsHelper = ref.read(repositoryLimitsHelperProvider);
        await limitsHelper.throwIdeasLimitException();
      }
    } catch (e) {
      if (e is IdeasLimitException) {
        // Handle the limit exception with paywall logic
        if (!mounted) return;

        // Reset recording state
        _resetRecordingState('Ideas limit reached');

        // Use a post-frame callback to handle the paywall after the current frame
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (!mounted) return;

          final shouldContinue = await PaywallHandler.handleLimitException(
            context: context,
            ref: ref,
            exception: e,
          );

          // If user upgraded, restart the recording process
          if (shouldContinue && mounted) {
            _handleLongPressStart(ideabook);
          }
        });
        return;
      } else {
        // Handle other exceptions
        Logger.error(
          'Unexpected error checking ideas limit for long press recording',
          e,
        );
        _resetRecordingState('Error checking limits');
        return;
      }
    }

    // Start audio recording
    await _startAudioRecording(ideabook);
  }

  /// Handle long press end - stop recording and process
  Future<void> _handleLongPressEnd(
    Ideabook ideabook,
    LongPressEndDetails details,
  ) async {
    Logger.debug('Long press end for ideabook: ${ideabook.id}');

    if (_isLongPressActive && _isRecording) {
      // Check if finger was over cancel area
      final fingerWasOverCancelArea = _checkFingerOverCancelArea(
        details.globalPosition,
      );

      if (fingerWasOverCancelArea) {
        Logger.debug(
          'Long press ended over cancel area. Cancelling recording.',
        );
        _cancelAudioRecording();
        _resetRecordingState('Cancelled by user gesture');
      } else {
        Logger.debug(
          'Long press ended (finger lifted), stopping recording for ${ideabook.id}',
        );

        if (mounted) {
          try {
            setState(() {
              _uiFeedbackState = UIFeedbackState.processing;
            });
          } catch (e) {
            Logger.debug(
              'setState error during long press end processing (likely during disposal): $e',
            );
            return;
          }
        } else {
          return;
        }

        await _stopAudioRecordingAndProcess(ideabook);
      }
    } else {
      Logger.debug('Long press ended but not in an active recording state.');
      _resetRecordingState('Recording not active on release');
    }

    if (mounted) {
      try {
        setState(() {
          _isLongPressActive = false;
          _isRecording = false;
        });
      } catch (e) {
        Logger.debug(
          'setState error during long press end cleanup (likely during disposal): $e',
        );
      }
    }
  }

  /// Handle long press cancel - cancel recording
  void _handleLongPressCancel(Ideabook ideabook) {
    Logger.debug('Long press cancelled for ideabook: ${ideabook.id}');

    if (_isRecording) {
      _cancelAudioRecording();
    }

    _resetRecordingState('Long press cancelled');
  }

  /// Reset recording state to idle
  void _resetRecordingState(String reason) {
    Logger.debug('Resetting recording state due to: $reason');
    _stopAmplitudeUpdates();
    if (mounted) {
      try {
        setState(() {
          _isLongPressActive = false;
          _isRecording = false;
          _currentRecordingPath = null;
          _uiFeedbackState = UIFeedbackState.idle;
          _isFingerOverCancelArea = false;
        });
      } catch (e) {
        Logger.debug(
          'setState error during recording state reset (likely during disposal): $e',
        );
      }
    }

    // Clean up recording controller
    _longPressRecordingController = null;
  }

  /// Start audio recording
  Future<void> _startAudioRecording(Ideabook ideabook) async {
    try {
      // Use the audioRecordingStateProvider to start recording
      // This ensures amplitude data flows correctly to the waveform
      final recordingStateNotifier = ref.read(
        audioRecordingStateProvider.notifier,
      );
      final result = await recordingStateNotifier.startRecording();

      if (!result) {
        Logger.error('Failed to start recording');
        _resetRecordingState('Failed to start recording');
        return;
      }

      // Get the recording state to access the file path
      final recordingState = ref.read(audioRecordingStateProvider);
      final filePath = recordingState.filePath;

      if (mounted) {
        try {
          setState(() {
            _isRecording = true;
            _currentRecordingPath = filePath;
          });
        } catch (e) {
          Logger.debug(
            'setState error during audio recording start (likely during disposal): $e',
          );
          return;
        }
      } else {
        return;
      }

      // Create recording controller
      _longPressRecordingController = IdeaRecordingController(
        ideabookId: ideabook.id,
      );

      // Start amplitude monitoring
      _startAmplitudeUpdates();

      Logger.debug('Audio recording started at $filePath');
    } catch (e) {
      Logger.error('Error starting audio recording', e);
      _resetRecordingState('Error starting recording: $e');
    }
  }

  /// Stop audio recording and process transcription
  Future<void> _stopAudioRecordingAndProcess(Ideabook ideabook) async {
    try {
      // Use the audioRecordingStateProvider to stop recording
      final recordingStateNotifier = ref.read(
        audioRecordingStateProvider.notifier,
      );
      final result = await recordingStateNotifier.stopRecording();

      if (!result) {
        Logger.error('Failed to stop recording');
        _resetRecordingState('Failed to stop recording');
        return;
      }

      // Get the recording state to access the file path and duration
      final recordingState = ref.read(audioRecordingStateProvider);
      final filePath = recordingState.filePath;
      final duration = recordingState.duration;

      Logger.debug('Recording stopped. Duration: ${duration.inSeconds}s');

      // Process the recording using the controller
      if (_longPressRecordingController != null &&
          filePath != null &&
          filePath.isNotEmpty) {
        await _longPressRecordingController!.handleRecordingCompleted(
          ref,
          filePath,
          duration,
        );
      }

      // Reset state after processing
      _resetRecordingState('Recording completed');
    } catch (e) {
      Logger.error('Error stopping and processing recording', e);
      _resetRecordingState('Error processing recording: $e');
    }
  }

  /// Cancel audio recording
  void _cancelAudioRecording() {
    try {
      final recordingStateNotifier = ref.read(
        audioRecordingStateProvider.notifier,
      );
      // Note: AudioRecordingStateNotifier doesn't have a cancel method, so we stop it
      recordingStateNotifier
          .stopRecording()
          .then((result) {
            Logger.debug('Recording cancelled/stopped');
          })
          .catchError((error) {
            Logger.error('Error cancelling recording', error);
          });
    } catch (e) {
      Logger.error('Error cancelling recording', e);
    }
  }

  /// Continue with recording after limit checks pass
  Future<void> _continueWithRecording(Ideabook ideabook) async {
    // Request microphone permission before entering recording mode
    final recordingService = ref.read(audioRecordingServiceProvider);

    // Check if permission is already granted
    final hasPermission = await recordingService.checkPermission();

    if (hasPermission) {
      // Permission already granted, enter recording mode
      ref.read(ideabookRecordingProvider.notifier).state = ideabook.id;
      return;
    }

    // For all platforms, directly request the permission
    final status = await recordingService.requestPermission();
    Logger.debug('Permission request result: ${status.name}');

    if (status.isGranted) {
      // Permission granted, enter recording mode
      ref.read(ideabookRecordingProvider.notifier).state = ideabook.id;
      return;
    }

    // If permission is denied, show settings dialog
    if (mounted) {
      // Use a post-frame callback to show the dialog after the current frame is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          showDialog(
            context: context,
            builder:
                (dialogContext) => AlertDialog(
                  title: Text(
                    'Microphone Permission Required',
                    style: NoejiTheme.textStylesOf(dialogContext).bodyLarge,
                  ),
                  content: Text(
                    'Microphone permission is required to record audio. '
                    'Please enable it in app settings.',
                    style: NoejiTheme.textStylesOf(dialogContext).bodyMedium,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                    side: BorderSide(
                      color: NoejiTheme.colorsOf(dialogContext).border,
                      width: 1,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(),
                      child: Text(
                        'Cancel',
                        style:
                            NoejiTheme.textStylesOf(dialogContext).buttonText,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(dialogContext).pop();
                        openAppSettings();
                      },
                      child: Text(
                        'Open Settings',
                        style:
                            NoejiTheme.textStylesOf(dialogContext).buttonText,
                      ),
                    ),
                  ],
                ),
          );
        }
      });
    }
  }

  /// Handle long press move update - track finger movement over cancel area
  void _handleLongPressMoveUpdate(LongPressMoveUpdateDetails details) {
    Logger.debug(
      'Long press move update - position: ${details.globalPosition}, active: $_isLongPressActive, recording: $_isRecording',
    );

    if (_isLongPressActive && _isRecording) {
      final fingerIsOverCancelArea = _checkFingerOverCancelArea(
        details.globalPosition,
      );
      Logger.debug(
        'Finger over cancel area: $fingerIsOverCancelArea, previous state: $_isFingerOverCancelArea',
      );

      if (_isFingerOverCancelArea != fingerIsOverCancelArea) {
        if (mounted) {
          try {
            setState(() {
              _isFingerOverCancelArea = fingerIsOverCancelArea;
            });
          } catch (e) {
            Logger.debug(
              'setState error during long press move update (likely during disposal): $e',
            );
            return;
          }
        } else {
          return;
        }

        // Provide haptic feedback when entering or leaving cancel area
        if (fingerIsOverCancelArea) {
          // Finger entered cancel area - try multiple haptic types for reliability
          HapticFeedback.mediumImpact();
          HapticFeedback.selectionClick();
          Logger.debug(
            'Finger entered cancel area - haptic feedback triggered (medium + selection)',
          );
        } else {
          // Finger left cancel area - try multiple haptic types for reliability
          HapticFeedback.lightImpact();
          HapticFeedback.selectionClick();
          Logger.debug(
            'Finger left cancel area - haptic feedback triggered (light + selection)',
          );
        }
      }
    }
  }

  /// Check if finger is over cancel area
  bool _checkFingerOverCancelArea(Offset globalFingerPosition) {
    if (!_isRecording || _cancelAreaKey.currentContext == null) return false;

    final RenderBox? cancelRenderBox =
        _cancelAreaKey.currentContext?.findRenderObject() as RenderBox?;
    if (cancelRenderBox == null) return false;

    final Offset cancelAreaGlobalOffset = cancelRenderBox.localToGlobal(
      Offset.zero,
    );
    final Rect cancelAreaRect = Rect.fromLTWH(
      cancelAreaGlobalOffset.dx,
      cancelAreaGlobalOffset.dy,
      cancelRenderBox.size.width,
      cancelRenderBox.size.height,
    );

    return cancelAreaRect.contains(globalFingerPosition);
  }

  /// Start amplitude monitoring
  void _startAmplitudeUpdates() {
    _amplitudeUpdateTimer = Timer.periodic(const Duration(milliseconds: 100), (
      timer,
    ) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      try {
        final recordingState = ref.read(audioRecordingStateProvider);
        final amplitude = recordingState.amplitude;

        if (mounted) {
          try {
            setState(() {
              _currentAmplitude = amplitude;
            });
          } catch (e) {
            // Ignore setState errors during disposal and cancel timer
            Logger.debug(
              'setState error during amplitude updates (likely during disposal): $e',
            );
            timer.cancel();
          }
        } else {
          // Widget is no longer mounted, cancel the timer
          timer.cancel();
        }
      } catch (e) {
        Logger.error('Error getting amplitude', e);
      }
    });
  }

  /// Stop amplitude monitoring
  void _stopAmplitudeUpdates() {
    _amplitudeUpdateTimer?.cancel();
    _amplitudeUpdateTimer = null;
    // Only call setState if the widget is still mounted and not being disposed
    if (mounted) {
      try {
        setState(() {
          _currentAmplitude = 0.0;
        });
      } catch (e) {
        // Ignore setState errors during disposal
        Logger.debug(
          'setState error during amplitude updates stop (likely during disposal): $e',
        );
      }
    }
  }

  /// Build row content with conditional padding based on recording state
  Widget _buildRowContentWithConditionalPadding(
    BuildContext context,
    Ideabook ideabook,
  ) {
    switch (_uiFeedbackState) {
      case UIFeedbackState.recording:
        // Recording mode: no padding, full width
        return _buildRecordingModeContent(context, ideabook);
      case UIFeedbackState.processing:
        // Processing mode: with padding
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: _buildProcessingModeContent(context, ideabook),
        );
      case UIFeedbackState.idle:
        // Normal mode: with padding
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: _buildIdeabookNameText(context, ideabook),
        );
    }
  }

  /// Build recording mode content with cancel area and waveform
  Widget _buildRecordingModeContent(BuildContext context, Ideabook ideabook) {
    return Row(
      crossAxisAlignment:
          CrossAxisAlignment.stretch, // KEY: Make children fill full height
      children: [
        // Cancel area with X icon and text - takes up left third of the screen
        Expanded(
          flex: 1,
          child: GestureDetector(
            key: _cancelAreaKey,
            onTap: () {
              Logger.debug('Cancel button tapped explicitly');
              _cancelAudioRecording();
              _resetRecordingState('Cancelled by X tap');
            },
            child: Container(
              // No padding - fill entire left area
              decoration: BoxDecoration(
                color:
                    _isFingerOverCancelArea
                        ? Colors.red.withValues(alpha: 0.2)
                        : Colors.transparent, // No background when not active
                // No border
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.close,
                      color:
                          _isFingerOverCancelArea
                              ? Colors.red
                              : Colors.grey[600],
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _isFingerOverCancelArea
                          ? 'Release\nto cancel'
                          : 'Drag here\nto cancel',
                      style: TextStyle(
                        color:
                            _isFingerOverCancelArea
                                ? Colors.red
                                : Colors.grey[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.left,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Waveform visualization - takes up right two-thirds of the screen
        Expanded(
          flex: 2,
          child: Container(
            // Minimal padding for waveform
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: ReusableWaveformConsumer(
              amplitude: _currentAmplitude,
              config: WaveformConfig(
                color:
                    NoejiTheme.colorsOf(context).textPrimary, // Use theme color
                height:
                    55, // Reduced height to fit within container (71 - 8 padding = 63 available)
                barMaxHeight: 3, // Match inline recording mode
                sensitivity: 1.2, // Match inline recording mode
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build processing mode content
  Widget _buildProcessingModeContent(BuildContext context, Ideabook ideabook) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Processing...',
          style: NoejiTheme.textStylesOf(context).bodyMedium,
        ),
      ],
    );
  }
}
