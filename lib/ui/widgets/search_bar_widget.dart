import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/providers/search_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Custom search bar widget
class SearchBarWidget extends ConsumerStatefulWidget {
  /// Constructor
  const SearchBarWidget({super.key});

  @override
  ConsumerState<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends ConsumerState<SearchBarWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    ref.read(isSearchingProvider.notifier).state = _focusNode.hasFocus;
  }

  void _clearSearch() {
    _controller.clear();
    ref.read(searchQueryProvider.notifier).state = '';
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final isSearching = ref.watch(isSearchingProvider);
    final searchQuery = ref.watch(searchQueryProvider);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
        // No border radius for strict rectangle
      ),
      child: Focus(
        onKeyEvent: (node, event) {
          // Handle Escape key to clear search and unfocus
          if (event is KeyDownEvent &&
              event.logicalKey == LogicalKeyboardKey.escape) {
            _clearSearch();
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: InputDecoration(
            hintText: 'Search',
            prefixIcon: Icon(
              Icons.search,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
            suffixIcon:
                isSearching && searchQuery.isNotEmpty
                    ? IconButton(
                      icon: Icon(
                        Icons.clear,
                        color: NoejiTheme.colorsOf(context).textPrimary,
                      ),
                      onPressed: _clearSearch,
                    )
                    : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(vertical: 12),
            hintStyle: NoejiTheme.textStylesOf(context).searchHint,
          ),
          style: NoejiTheme.textStylesOf(context).bodyMedium,
          onChanged: (value) {
            ref.read(searchQueryProvider.notifier).state = value;
          },
          // Handle Enter key to unfocus
          onSubmitted: (_) {
            _focusNode.unfocus();
          },
          textInputAction: TextInputAction.search,
        ),
      ),
    );
  }
}
