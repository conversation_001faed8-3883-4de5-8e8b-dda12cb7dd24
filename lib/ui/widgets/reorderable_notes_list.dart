import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/firestore_note_listener_controller.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/swipeable_note_item.dart';
import 'package:noeji/utils/logger.dart';

/// Provider to track which note is currently being dragged
final draggedNoteIdProvider = StateProvider<String?>((ref) => null);

/// Widget for displaying a reorderable list of notes
class ReorderableNotesList extends ConsumerStatefulWidget {
  /// The ideabook to display notes for
  final Ideabook ideabook;

  /// The list of notes to display
  final List<Note> notes;

  /// Constructor
  const ReorderableNotesList({
    super.key,
    required this.ideabook,
    required this.notes,
  });

  @override
  ConsumerState<ReorderableNotesList> createState() =>
      _ReorderableNotesListState();
}

/// State for the ReorderableNotesList widget
class _ReorderableNotesListState extends ConsumerState<ReorderableNotesList> {
  /// ScrollController for the notes list
  /// Using keepScrollOffset: true to maintain position during rebuilds
  final ScrollController _scrollController = ScrollController(
    keepScrollOffset: true,
  );
  @override
  void dispose() {
    // Dispose the scroll controller when the widget is removed
    _scrollController.dispose();
    super.dispose();
  }

  /// Log the current state of the notes list for debugging
  void _logNotesSortingDetails() {
    final notes = widget.notes;
    Logger.debug('Current notes list state:');
    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final title =
          note.title.length > 15
              ? '${note.title.substring(0, 15)}...'
              : note.title;
      Logger.debug(
        '  [$i]: effective=${note.getEffectiveSortValue()} | t=${note.createdAt.millisecondsSinceEpoch} | s=${note.sortOrder ?? "n/a"} | "$title"',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the dragged note ID provider
    final draggedNoteId = ref.watch(draggedNoteIdProvider);

    // Log the current state of the notes list
    _logNotesSortingDetails();

    // If there are no notes, show an empty state
    if (widget.notes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.note_alt_outlined,
              size: 64,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No notes yet',
              style: NoejiTheme.textStylesOf(context).bodyLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Save chat responses to create notes',
              style: NoejiTheme.textStylesOf(context).bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ReorderableListView.builder(
      scrollController: _scrollController,
      itemCount: widget.notes.length,
      onReorder: _handleReorder,
      buildDefaultDragHandles: false, // We'll handle drag detection ourselves
      itemBuilder: (context, index) {
        final note = widget.notes[index];
        final isBeingDragged = draggedNoteId == note.id;

        return ReorderableDelayedDragStartListener(
          key: ValueKey(note.id),
          index: index,
          enabled: true, // Always enable reordering for notes
          child: _buildNoteItem(note, isBeingDragged, index),
        );
      },
      proxyDecorator: (child, index, animation) {
        // Make the dragged item slightly bigger and brighter
        return AnimatedBuilder(
          animation: animation,
          builder: (BuildContext context, Widget? child) {
            final double scale = 1.0 + 0.05 * animation.value;
            return Transform.scale(
              scale: scale,
              child: Material(
                elevation: 4.0 * animation.value,
                color: Colors.transparent,
                shadowColor: Theme.of(context).shadowColor,
                child: child,
              ),
            );
          },
          child: child,
        );
      },
    );
  }

  /// Build an individual note item
  Widget _buildNoteItem(Note note, bool isBeingDragged, int index) {
    // When dragging, make the item visually distinct
    final noteItem = Container(
      decoration: BoxDecoration(
        color:
            isBeingDragged
                ? Theme.of(context).brightness == Brightness.light
                    ? Colors.grey.shade100
                    : Colors.grey.shade800
                : null,
        // Add a subtle border when being dragged
        border:
            isBeingDragged
                ? Border.all(
                  color:
                      Theme.of(context).brightness == Brightness.light
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                  width: 1.0,
                )
                : null,
      ),
      // No transform here - we'll use the proxyDecorator for scaling during drag
      child: SwipeableNoteItem(note: note, ideabook: widget.ideabook),
    );

    // Add a divider after each item except the last one
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        noteItem,
        // Add divider after each item except the last one
        if (index < widget.notes.length - 1)
          Divider(
            height: 1,
            thickness: 0.5,
            color: NoejiTheme.colorsOf(context).divider,
          ),
      ],
    );
  }

  /// Handle reordering of notes
  void _handleReorder(int oldIndex, int newIndex) {
    Logger.debug('Reordering note from index $oldIndex to $newIndex');

    // Adjust the newIndex if it's after the oldIndex
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // Get the moved note
    final movedNote = widget.notes[oldIndex];
    final shortTitle =
        movedNote.title.length > 20
            ? '${movedNote.title.substring(0, 20)}...'
            : movedNote.title;
    Logger.debug('Moving note ${movedNote.id} with title: "$shortTitle"');
    Logger.debug(
      'Moved note details: effective=${movedNote.getEffectiveSortValue()} | t=${movedNote.createdAt.millisecondsSinceEpoch} | s=${movedNote.sortOrder ?? "n/a"}',
    );

    // Calculate the new sort order value based on the algorithm
    final newSortOrder = _calculateNewSortOrder(oldIndex, newIndex);
    Logger.debug('Calculated new sort order: $newSortOrder');

    // Update the sort order in Firestore
    if (newSortOrder != null) {
      Logger.debug('Updating sort order in Firestore for note ${movedNote.id}');

      // Use the listener controller to update the sort order
      // This will update both the UI and Firestore
      ref
          .read(
            firestoreNotesListenerControllerProvider(
              widget.ideabook.id,
            ).notifier,
          )
          .updateNoteSortOrder(movedNote.id, newSortOrder)
          .then((success) {
            Logger.debug('Sort order update result: $success');

            // Log the list again after the update to see the changes
            Logger.debug(
              'List after reordering (before Firestore update is applied):',
            );
            _logNotesSortingDetails();

            // Schedule another log after a short delay to capture the updated list
            Future.delayed(const Duration(milliseconds: 500), () {
              Logger.debug(
                'List after reordering (with delay to capture Firestore update):',
              );
              _logNotesSortingDetails();
            });
          })
          .catchError((error) {
            Logger.error('Error updating sort order', error);
          });
    }
  }

  /// Calculate the new sort order value for a note being moved
  /// Returns null if the sort order cannot be calculated
  double? _calculateNewSortOrder(int oldIndex, int newIndex) {
    final notes = widget.notes;
    if (notes.isEmpty) {
      Logger.debug('Cannot calculate sort order: Notes list is empty');
      return null;
    }

    // Log the current state of the list before calculating
    Logger.debug('Current list state before calculating new sort order:');
    for (int i = 0; i < notes.length; i++) {
      final note = notes[i];
      final shortTitle =
          note.title.length > 15
              ? '${note.title.substring(0, 15)}...'
              : note.title;
      Logger.debug(
        '  [$i]: effective=${note.getEffectiveSortValue()} | t=${note.createdAt.millisecondsSinceEpoch} | s=${note.sortOrder ?? "n/a"} | "$shortTitle"',
      );
    }

    // Get the moved note
    final movedNote = notes[oldIndex];
    Logger.debug(
      'Moved note: effective=${movedNote.getEffectiveSortValue()} | t=${movedNote.createdAt.millisecondsSinceEpoch} | s=${movedNote.sortOrder ?? "n/a"} | "${movedNote.title.substring(0, movedNote.title.length.clamp(0, 15))}..."',
    );

    // Case 1: Moved to the absolute top of the list
    if (newIndex == 0) {
      Logger.debug('Case 1: Moving to the absolute top of the list');
      final topNote = notes[0];
      final topValue = topNote.getEffectiveSortValue();

      Logger.debug(
        'Top note: effective=$topValue | t=${topNote.createdAt.millisecondsSinceEpoch} | s=${topNote.sortOrder ?? "n/a"} | "${topNote.title.substring(0, topNote.title.length.clamp(0, 15))}..."',
      );

      // New sort value should be higher than the current top note
      final newValue = topValue + 1.0;
      Logger.debug('New sort value (top + 1.0): $newValue');
      return newValue;
    }

    // Case 2: Moved to the absolute bottom of the list
    if (newIndex == notes.length - 1) {
      Logger.debug('Case 2: Moving to the absolute bottom of the list');
      final bottomNote = notes[notes.length - 1];
      final bottomValue = bottomNote.getEffectiveSortValue();

      Logger.debug(
        'Bottom note: effective=$bottomValue | t=${bottomNote.createdAt.millisecondsSinceEpoch} | s=${bottomNote.sortOrder ?? "n/a"} | "${bottomNote.title.substring(0, bottomNote.title.length.clamp(0, 15))}..."',
      );

      // New sort value should be lower than the current bottom note
      final newValue = bottomValue - 1.0;
      Logger.debug('New sort value (bottom - 1.0): $newValue');
      return newValue;
    }

    // Case 3: Moved between two existing notes
    Logger.debug('Case 3: Moving between two existing notes');

    // Create a copy of the notes list without the moved note
    final notesWithoutMoved = List<Note>.from(notes);
    notesWithoutMoved.removeAt(oldIndex);

    // Now get the notes that will be above and below the moved note in its new position
    final noteAbove =
        notesWithoutMoved[newIndex - 1]; // -1 because we removed an item
    final noteBelow = notesWithoutMoved[newIndex];

    final aboveValue = noteAbove.getEffectiveSortValue();
    final belowValue = noteBelow.getEffectiveSortValue();

    Logger.debug(
      'Note above: effective=$aboveValue | t=${noteAbove.createdAt.millisecondsSinceEpoch} | s=${noteAbove.sortOrder ?? "n/a"} | "${noteAbove.title.substring(0, noteAbove.title.length.clamp(0, 15))}..."',
    );
    Logger.debug(
      'Note below: effective=$belowValue | t=${noteBelow.createdAt.millisecondsSinceEpoch} | s=${noteBelow.sortOrder ?? "n/a"} | "${noteBelow.title.substring(0, noteBelow.title.length.clamp(0, 15))}..."',
    );

    // Calculate the average of the two values
    final newValue = (aboveValue + belowValue) / 2.0;
    Logger.debug(
      'New sort value ((above + below) / 2): $newValue = ($aboveValue + $belowValue) / 2.0',
    );
    return newValue;
  }
}
