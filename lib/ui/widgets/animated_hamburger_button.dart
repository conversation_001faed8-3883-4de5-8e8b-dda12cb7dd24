import 'dart:math' as math;
import 'package:flutter/material.dart';

/// An animated button that transitions between hamburger (two lines) and close (X) icons
class AnimatedHamburgerButton extends StatefulWidget {
  /// Whether the button should show the close (X) state
  final bool isOpen;

  /// Callback when the button is pressed
  final VoidCallback onPressed;

  /// Color of the icon
  final Color? color;

  /// Size of the icon
  final double size;

  /// Duration of the animation
  final Duration animationDuration;

  /// Constructor
  const AnimatedHamburgerButton({
    super.key,
    required this.isOpen,
    required this.onPressed,
    this.color,
    this.size = 24.0,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedHamburgerButton> createState() => _AnimatedHamburgerButtonState();
}

class _AnimatedHamburgerButtonState extends State<AnimatedHamburgerButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Set initial state
    if (widget.isOpen) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedHamburgerButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpen != oldWidget.isOpen) {
      if (widget.isOpen) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).iconTheme.color ?? Colors.black;
    final lineWidth = widget.size * 0.6;
    final lineHeight = 2.0;
    final lineSpacing = 4.0;

    return IconButton(
      onPressed: widget.onPressed,
      icon: SizedBox(
        width: widget.size,
        height: widget.size,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            // Calculate rotations: top line rotates +45°, bottom line rotates -45°
            final topRotation = _animation.value * (math.pi / 4); // 45 degrees
            final bottomRotation = _animation.value * -(math.pi / 4); // -45 degrees

            // Calculate translations: both lines move toward center
            final initialTopY = -(lineSpacing / 2 + lineHeight / 2);
            final initialBottomY = (lineSpacing / 2 + lineHeight / 2);
            final topYTranslation = _animation.value * (-initialTopY);
            final bottomYTranslation = _animation.value * (-initialBottomY);

            return Stack(
              alignment: Alignment.center,
              children: [
                // Top line
                Transform.translate(
                  offset: Offset(0, initialTopY + topYTranslation),
                  child: Transform.rotate(
                    angle: topRotation,
                    child: _buildLine(color, lineWidth, lineHeight),
                  ),
                ),
                // Bottom line
                Transform.translate(
                  offset: Offset(0, initialBottomY + bottomYTranslation),
                  child: Transform.rotate(
                    angle: bottomRotation,
                    child: _buildLine(color, lineWidth, lineHeight),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildLine(Color color, double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(height / 2),
      ),
    );
  }
}


