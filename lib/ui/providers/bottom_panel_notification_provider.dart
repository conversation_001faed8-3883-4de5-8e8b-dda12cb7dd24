import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/providers/recording_mode_provider.dart';
import 'package:noeji/ui/widgets/bottom_panel_controller.dart';

/// Class to represent a bottom panel notification
class BottomPanelNotification {
  /// The notification message to display
  final String message;

  /// When the notification was created
  final DateTime createdAt;

  /// Creates a new BottomPanelNotification
  const BottomPanelNotification({
    required this.message,
    required this.createdAt,
  });
}

/// Provider to track when to show a notification in the bottom panel
/// When in notification mode, the bottom panel will show a notification message
/// instead of the "New Ideabook" button
final bottomPanelNotificationProvider = StateProvider<BottomPanelNotification?>(
  (ref) => null,
);

/// Provider function to show a notification in the bottom panel
/// The notification will automatically be dismissed after the specified duration
final showBottomPanelNotificationProvider =
    Provider<Function(String, {Duration? duration})>((ref) {
      return (String message, {Duration? duration}) {
        // Create the notification
        final notification = BottomPanelNotification(
          message: message,
          createdAt: DateTime.now(),
        );

        // Set the notification
        ref.read(bottomPanelNotificationProvider.notifier).state = notification;

        // Schedule automatic dismissal
        final autoDismissDuration =
            duration ?? const Duration(seconds: 1, milliseconds: 500);
        Future.delayed(autoDismissDuration, () {
          // Only dismiss if this notification is still active
          final currentNotification = ref.read(bottomPanelNotificationProvider);
          if (currentNotification != null &&
              currentNotification.createdAt == notification.createdAt) {
            ref.read(bottomPanelNotificationProvider.notifier).state = null;

            // Return to normal state after notification is dismissed
            ref.read(bottomPanelStateProvider.notifier).state =
                BottomPanelState.normal;

            // Also reset the recording mode provider for compatibility
            ref.read(recordingModeProvider.notifier).state = false;
          }
        });
      };
    });
