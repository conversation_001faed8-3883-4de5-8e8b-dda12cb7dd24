import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Class to represent an ideabook notification
class IdeabookNotification {
  /// The ID of the ideabook showing the notification
  final String ideabookId;

  /// The notification message to display
  final String message;

  /// When the notification was created
  final DateTime createdAt;

  /// Creates a new IdeabookNotification
  const IdeabookNotification({
    required this.ideabookId,
    required this.message,
    required this.createdAt,
  });
}

/// Provider to track which ideabook is currently showing a notification
/// When an ideabook is in notification mode, the row will show a notification message
/// instead of the normal ideabook row
final ideabookNotificationProvider = StateProvider<IdeabookNotification?>((ref) => null);

/// Provider function to show a notification for an ideabook
/// The notification will automatically be dismissed after the specified duration
final showIdeabookNotificationProvider = Provider<Function(String, String, {Duration? duration})>((ref) {
  return (String ideabookId, String message, {Duration? duration}) {
    // Create the notification
    final notification = IdeabookNotification(
      ideabookId: ideabookId,
      message: message,
      createdAt: DateTime.now(),
    );

    // Set the notification
    ref.read(ideabookNotificationProvider.notifier).state = notification;

    // Schedule automatic dismissal
    final autoDismissDuration = duration ?? const Duration(seconds: 1, milliseconds: 500);
    Future.delayed(autoDismissDuration, () {
      // Only dismiss if this notification is still active
      final currentNotification = ref.read(ideabookNotificationProvider);
      if (currentNotification != null &&
          currentNotification.ideabookId == ideabookId &&
          currentNotification.createdAt == notification.createdAt) {
        ref.read(ideabookNotificationProvider.notifier).state = null;
      }
    });
  };
});
