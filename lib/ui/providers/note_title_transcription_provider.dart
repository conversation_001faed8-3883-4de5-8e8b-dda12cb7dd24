import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Class to hold transcription data for note title editing
class NoteTitleTranscription {
  /// The note ID
  final String noteId;

  /// The transcribed text
  final String transcribedText;

  /// Constructor
  const NoteTitleTranscription({
    required this.noteId,
    required this.transcribedText,
  });
}

/// Provider to store the transcription result for note title editing
final noteTitleTranscriptionProvider = StateProvider<NoteTitleTranscription?>((ref) => null);
