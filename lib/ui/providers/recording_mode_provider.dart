import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enum representing the state of the recording mode
enum RecordingModeState {
  /// Not in recording mode
  inactive,

  /// Recording is in progress
  recording,

  /// Recording is complete, processing the audio
  processing,

  /// Showing notification after processing
  notification,
}

/// Provider to track the recording mode state
final recordingModeStateProvider = StateProvider<RecordingModeState>((ref) => RecordingModeState.inactive);

/// Provider to track if the app is in recording mode (for backward compatibility)
final recordingModeProvider = StateProvider<bool>((ref) => false);
