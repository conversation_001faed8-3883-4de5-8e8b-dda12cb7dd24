import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/common/filter_provider.dart';

/// Provider to track the currently selected color filter
final colorFilterProvider = StateProvider<IdeabookColor?>((ref) => null);

/// Provider for the ideabook color filter
final ideabookColorFilterProvider = Provider<FilterProvider<Ideabook>>((ref) {
  final colorFilter = ref.watch(colorFilterProvider);
  final filter = FilterProvider<Ideabook>();

  // Add an equality filter criterion for the ideabook color
  filter.addCriterion(
    EqualityFilterCriterion<Ideabook, IdeabookColor>(
      value: colorFilter,
      propertyExtractor: (ideabook) => ideabook.color,
    ),
  );

  return filter;
});

/// Provider for ideabooks filtered by color
/// This maintains backward compatibility with existing code
final colorFilteredIdeabooksProvider =
    Provider.family<List<Ideabook>, List<Ideabook>>((ref, ideabooks) {
      final filter = ref.watch(ideabookColorFilterProvider);
      return filter.apply(ideabooks);
    });
