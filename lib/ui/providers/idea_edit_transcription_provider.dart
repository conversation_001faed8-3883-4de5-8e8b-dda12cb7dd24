import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider to store the transcription result for idea edit recording
/// This provider is used to pass the transcribed text from the recording controller
/// to the idea edit widget, even if the recording widget is disposed
final ideaEditTranscriptionProvider = StateProvider<IdeaEditTranscription?>((ref) => null);

/// Class to store the transcription result and the idea ID
class IdeaEditTranscription {
  /// The ID of the idea being edited
  final String ideaId;
  
  /// The transcribed text
  final String transcribedText;
  
  /// Constructor
  const IdeaEditTranscription({
    required this.ideaId,
    required this.transcribedText,
  });
}
