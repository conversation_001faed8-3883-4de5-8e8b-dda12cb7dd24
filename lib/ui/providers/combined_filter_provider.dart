import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/color_filter_provider.dart';
import 'package:noeji/ui/providers/combined_ideabook_provider.dart';
import 'package:noeji/ui/providers/common/filter_provider.dart';
import 'package:noeji/ui/providers/group_provider.dart';
import 'package:noeji/ui/providers/search_provider.dart';
import 'package:noeji/ui/providers/sort_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for a combined filter that includes both search and color filters
final combinedIdeabookFilterProvider = Provider<FilterProvider<Ideabook>>((
  ref,
) {
  final searchFilter = ref.watch(ideabookSearchFilterProvider);
  final colorFilter = ref.watch(ideabookColorFilterProvider);

  // Create a new filter that combines both filters
  final combinedFilter = FilterProvider<Ideabook>();

  // Add the search filter criteria
  for (final criterion in searchFilter.criteria) {
    combinedFilter.addCriterion(criterion);
  }

  // Add the color filter criteria
  for (final criterion in colorFilter.criteria) {
    combinedFilter.addCriterion(criterion);
  }

  return combinedFilter;
});

/// Provider to check if any filter is active
final isFilterActiveProvider = Provider<bool>((ref) {
  final combinedFilter = ref.watch(combinedIdeabookFilterProvider);
  return combinedFilter.isActive;
});

/// Provider for ideabooks filtered by both search query and color, and sorted by creation date
/// Optionally grouped by color when groupByColor is enabled
final combinedFilteredIdeabooksProvider = Provider<AsyncValue<List<Ideabook>>>((
  ref,
) {
  // Get the actual ideabooks provider based on the feature flag
  final actualProvider = ref.watch(combinedIdeabooksNotifierProvider);
  final ideabooksAsync = ref.watch(actualProvider);

  final combinedFilter = ref.watch(combinedIdeabookFilterProvider);
  final sortOrder = ref.watch(ideabookSortOrderProvider);
  final groupByColor = ref.watch(groupByColorProvider);

  Logger.debug(
    'combinedFilteredIdeabooksProvider rebuilding with groupByColor: $groupByColor',
  );

  return ideabooksAsync.when(
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
    data: (ideabooks) {
      // Apply the combined filter
      final filteredIdeabooks = combinedFilter.apply(ideabooks);

      // Sort the ideabooks by creation date
      Logger.debug(
        'Sorting ideabooks by creation date, order: ${sortOrder.name}, count: ${filteredIdeabooks.length}',
      );

      // Log a few ideabooks before sorting
      if (filteredIdeabooks.isNotEmpty) {
        Logger.debug('Before sorting - First few ideabooks:');
        for (
          int i = 0;
          i < (filteredIdeabooks.length > 3 ? 3 : filteredIdeabooks.length);
          i++
        ) {
          final ideabook = filteredIdeabooks[i];
          Logger.debug(
            '  $i: ${ideabook.name}, created: ${ideabook.createdAt}, id: ${ideabook.id}',
          );
        }
      }

      // Create a new list to avoid modifying the original list
      final sortedIdeabooks = List<Ideabook>.from(filteredIdeabooks);

      // If grouping by color is enabled, group ideabooks by color
      if (groupByColor) {
        Logger.debug('Grouping ideabooks by color');

        // Define the color order as specified
        final colorOrder = [
          IdeabookColor.red, // 1
          IdeabookColor.green, // 2
          IdeabookColor.blue, // 3
          IdeabookColor.yellow, // 4
          IdeabookColor.orange, // 5
          IdeabookColor.purple, // 6
          IdeabookColor.none, // Put "none" color at the end
        ];

        // First sort by creation date within each color group
        sortedIdeabooks.sort((a, b) {
          if (sortOrder == SortOrder.ascending) {
            return a.createdAt.compareTo(b.createdAt); // Oldest first
          } else {
            return b.createdAt.compareTo(a.createdAt); // Newest first
          }
        });

        // Then sort by color according to the specified order
        sortedIdeabooks.sort((a, b) {
          final aIndex = colorOrder.indexOf(a.color);
          final bIndex = colorOrder.indexOf(b.color);
          return aIndex.compareTo(bIndex);
        });

        // Log the grouping result
        if (sortedIdeabooks.isNotEmpty) {
          Logger.debug('After grouping by color - First few ideabooks:');
          for (
            int i = 0;
            i < (sortedIdeabooks.length > 3 ? 3 : sortedIdeabooks.length);
            i++
          ) {
            final ideabook = sortedIdeabooks[i];
            Logger.debug(
              '  $i: ${ideabook.name}, color: ${ideabook.color.name}, created: ${ideabook.createdAt}',
            );
          }
        }
      } else {
        // If not grouping by color, just sort by creation date
        sortedIdeabooks.sort((a, b) {
          if (sortOrder == SortOrder.ascending) {
            return a.createdAt.compareTo(b.createdAt); // Oldest first
          } else {
            return b.createdAt.compareTo(a.createdAt); // Newest first
          }
        });

        // Log a few ideabooks after sorting
        if (sortedIdeabooks.isNotEmpty) {
          Logger.debug('After sorting - First few ideabooks:');
          for (
            int i = 0;
            i < (sortedIdeabooks.length > 3 ? 3 : sortedIdeabooks.length);
            i++
          ) {
            final ideabook = sortedIdeabooks[i];
            Logger.debug(
              '  $i: ${ideabook.name}, created: ${ideabook.createdAt}, id: ${ideabook.id}',
            );
          }
        }
      }

      // Replace the filtered list with the sorted list
      filteredIdeabooks.clear();
      filteredIdeabooks.addAll(sortedIdeabooks);

      return AsyncValue.data(filteredIdeabooks);
    },
  );
});
