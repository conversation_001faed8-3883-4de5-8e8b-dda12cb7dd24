import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/chat.dart';
import 'package:noeji/models/chat_message.dart';
import 'package:noeji/models/idea.dart';
import 'package:noeji/repositories/chat_file_repository.dart';
import 'package:noeji/repositories/chat_repository_provider.dart';
import 'package:noeji/repositories/idea_repository.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/services/llm/gemini_chat_service.dart' as gemini;
import 'package:noeji/services/llm/llm_providers.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/id_utils.dart';

/// Provider for a chat by ideabook ID
final chatProvider = FutureProvider.family<Chat?, String>((
  ref,
  ideabookId,
) async {
  final repository = ref.watch(chatRepositoryProvider);
  return repository.getChatByIdeabookId(ideabookId);
});

/// State class for chat
class ChatState {
  /// The chat messages
  final List<ChatMessage> messages;

  /// Whether a message is currently being sent
  final bool isLoading;

  /// Error message if any
  final String? errorMessage;

  /// Creates a new ChatState
  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  /// Creates a copy of this ChatState with the given fields replaced with new values
  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Creates a new ChatState with the error message cleared
  ChatState clearError() {
    return ChatState(
      messages: messages,
      isLoading: isLoading,
      errorMessage: null,
    );
  }
}

/// Notifier for managing chat state
class ChatNotifier extends StateNotifier<ChatState> {
  final ChatFileRepository _repository;
  final gemini.GeminiChatService _geminiChatService;
  final LlmPrompts _llmPrompts;
  final IdeaRepository _ideaRepository;
  final String _ideabookId;
  final Ref _ref;

  /// Constructor
  ChatNotifier(
    this._repository,
    this._geminiChatService,
    this._llmPrompts,
    this._ideaRepository,
    this._ideabookId,
    this._ref,
  ) : super(const ChatState()) {
    _loadChat();
  }

  /// Load the chat from the database
  Future<void> _loadChat() async {
    try {
      // Get the chat from the database
      final chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat != null) {
        // Update the state with the loaded messages
        state = state.copyWith(messages: chat.messages);
      } else {
        // Create a new chat if none exists
        await _repository.createChat(ideabookId: _ideabookId);
      }
    } catch (e) {
      Logger.error('Error loading chat', e);
      state = state.copyWith(errorMessage: 'Error loading chat: $e');
    }
  }

  /// Send a message to the chat
  Future<void> sendMessage(
    String content, {
    required List<Idea> ideas,
    required String ideabookName,
  }) async {
    if (content.trim().isEmpty) return;

    try {
      // Set loading state
      state = state.copyWith(isLoading: true);

      // Create a new user message
      final userMessage = ChatMessage(
        id: IdUtils.generateId(),
        role: MessageRole.user,
        content: content,
        timestamp: DateTime.now(),
      );

      // Add the user message to the state
      state = state.copyWith(messages: [...state.messages, userMessage]);

      // Get the chat from the database
      var chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat == null) {
        // Create a new chat if none exists
        chat = await _repository.createChat(
          ideabookId: _ideabookId,
          messages: [userMessage],
        );
      } else {
        // Add the message to the existing chat
        await _repository.addMessageToChat(
          chat: chat,
          role: MessageRole.user,
          content: content,
        );

        // Update the chat with the new message
        chat = await _repository.getChatByIdeabookId(_ideabookId);
      }

      // FETCH LATEST IDEAS FROM DATABASE instead of using passed parameter
      // This ensures the system instruction always has the most up-to-date ideas
      Logger.debug('======= CHAT DEBUGGING =======');
      Logger.debug('Ideabook ID: $_ideabookId, Name: $ideabookName');
      Logger.debug('Fetching latest ideas from database...');

      final latestIdeas = await _ideaRepository.getIdeasByIdeabookId(
        _ideabookId,
      );
      Logger.debug('Total latest ideas from database: ${latestIdeas.length}');
      Logger.debug('Total ideas passed as parameter: ${ideas.length}');

      // Log each latest idea individually for debugging
      if (latestIdeas.isNotEmpty) {
        Logger.debug('Latest ideas details:');
        for (int i = 0; i < latestIdeas.length; i++) {
          final idea = latestIdeas[i];
          Logger.debug('  Idea $i - ID: ${idea.id}');
          // IdeabookId is now implicit from the collection structure
          Logger.debug(
            '  Idea $i - Content: "${idea.content.substring(0, idea.content.length.clamp(0, 50))}${idea.content.length > 50 ? "..." : ""}"',
          );
          Logger.debug('  Idea $i - CreatedAt: ${idea.createdAt}');
        }
      } else {
        Logger.debug('WARNING: No latest ideas found in the database!');
      }

      // No need to verify ideas belong to the correct ideabook anymore
      // Since ideabookId is now implicit from the collection structure
      // All ideas retrieved from the ideabook's subcollection already belong to that ideabook

      // Format the latest ideas for the prompt with date only (no time, using local timezone)
      final ideasText =
          latestIdeas.isEmpty
              ? "No ideas found in this ideabook yet."
              : latestIdeas
                  .map((idea) {
                    // Extract only the date part (YYYY-MM-DD) in local timezone
                    final dateOnly =
                        idea.createdAt.toLocal().toString().split(' ')[0];
                    return '* $dateOnly | ${idea.content}';
                  })
                  .join('\n');

      // Log the formatted ideas text
      Logger.debug('Formatted ideas text for prompt:');
      Logger.debug(ideasText);

      // CRITICAL CHECK: Verify the ideas text is not empty
      if (ideasText == "No ideas found in this ideabook yet." &&
          latestIdeas.isNotEmpty) {
        Logger.debug(
          'CRITICAL ERROR: Ideas text is empty even though we have ${latestIdeas.length} ideas!',
        );
      }

      // Get the past 25 chat messages for context (excluding the current user message we just added)
      // These will be formatted as text and embedded in the user instruction
      final pastChatMessages =
          state.messages.length > 1
              ? (state.messages.length > 26
                  ? state.messages.sublist(
                    state.messages.length - 26,
                    state.messages.length - 1,
                  )
                  : state.messages.sublist(0, state.messages.length - 1))
              : <ChatMessage>[];

      // Format the past chat messages as text
      final previousChatText =
          pastChatMessages.isEmpty
              ? ""
              : pastChatMessages
                  .map(
                    (msg) =>
                        // Gemini uses "model" instead of "assistant". But ok to keep the enum name as "assistant" for readability.
                        "${msg.role == MessageRole.user ? 'User' : 'Model'}: ${msg.content}",
                  )
                  .join('\n');

      // Get chat response style configuration
      final responseStyle = _llmPrompts.getChatResponseStyle();

      // Get user's selected chat style preferences
      final appBehavior = _ref.read(appBehaviorProvider);
      final selectedVerbosity = appBehavior.chatStyleVerbosity;
      final selectedTone = appBehavior.chatStyleTone;
      final selectedOutputFormat = appBehavior.chatStyleOutputFormat;

      // Extract style text from configuration using user's preferences
      final verbosityData =
          responseStyle['verbosity']?[selectedVerbosity]
              as Map<String, dynamic>?;
      final verbosityText =
          verbosityData?['prompt'] as String? ??
          'Balanced: provide a reasonable amount of detail without being overly lengthy or too short. Max output: 4000 tokens';

      final toneData =
          responseStyle['tone']?[selectedTone] as Map<String, dynamic>?;
      final toneText =
          toneData?['prompt'] as String? ??
          'Casual: use a friendly, approachable, and conversational style. You can use contractions and more relaxed language. Use fun engaging emojis emoticons unless user asks not to';

      final outputFormatData =
          responseStyle['output_format']?[selectedOutputFormat]
              as Map<String, dynamic>?;
      final outputFormatText =
          outputFormatData?['prompt'] as String? ??
          'Free form: provide your response in well-structured paragraphs';

      // Get the system instruction template and replace variables
      final systemInstructionTemplate = _llmPrompts.getChatSystemInstruction();
      final systemInstruction = _llmPrompts
          .replaceVariables(systemInstructionTemplate, {
            'ideabook_name': ideabookName,
            'ideas_text': ideasText,
            'verbosity': verbosityText,
            'tone': toneText,
            'output_format': outputFormatText,
          });

      // Get the user instruction template and replace variables
      final userInstructionTemplate = _llmPrompts.getChatUserInstruction();
      final userInstruction = _llmPrompts.replaceVariables(
        userInstructionTemplate,
        {'user_content': content, 'previous_chat_messages': previousChatText},
      );

      // Create the current user message with wrapped instruction
      // This is the only message we'll send to the API
      final currentUserMessage = ChatMessage(
        id: IdUtils.generateId(),
        role: MessageRole.user,
        content: userInstruction,
        timestamp: DateTime.now(),
      );

      // Log the complete system instruction and user instruction for debugging
      Logger.debug('System instruction:');
      Logger.debug(systemInstruction);
      Logger.debug('User instruction:');
      Logger.debug(userInstruction);
      Logger.debug('Past chat messages count: ${pastChatMessages.length}');
      Logger.debug(
        'Previous chat text length: ${previousChatText.length} characters',
      );
      Logger.debug(
        'Sending single wrapped message to API (no chat history in contents)',
      );
      Logger.debug('======= END CHAT DEBUGGING =======');

      // Send only the single wrapped message to Gemini with system instruction
      final response = await _geminiChatService.chatWithSystemInstruction(
        messages: [currentUserMessage],
        systemInstruction: systemInstruction,
      );

      if (response.isSuccess && response.content != null) {
        // The response content is already parsed by the Gemini service
        String responseContent = response.content!;

        // Variables to store parsed data
        String userPromptSummary = "";

        // Check if we have the original JSON from the Gemini service
        if (response.originalJson != null) {
          Logger.debug('Using originalJson from Gemini service');

          // Extract the user_prompt if it exists
          if (response.originalJson!.containsKey('user_prompt')) {
            userPromptSummary = response.originalJson!['user_prompt'] as String;
            Logger.debug(
              'User prompt summary from originalJson: $userPromptSummary',
            );
          } else {
            Logger.debug('No user_prompt field found in originalJson');
          }
        } else {
          Logger.debug(
            'No originalJson available from Gemini service, trying to parse from content',
          );

          // Check if the content might still be JSON or wrapped in markdown code blocks
          String contentToProcess = responseContent;

          // Check for markdown code blocks with ```json or ``` pattern
          if (contentToProcess.trim().startsWith('```')) {
            Logger.debug(
              'Content appears to be wrapped in markdown code blocks, extracting...',
            );

            // Find the end of the code block
            final endBlockIndex = contentToProcess.lastIndexOf('```');
            if (endBlockIndex > 3) {
              // Extract content between the code blocks
              final startContentIndex =
                  contentToProcess.indexOf(
                    '\n',
                    contentToProcess.indexOf('```'),
                  ) +
                  1;
              if (startContentIndex > 0 && startContentIndex < endBlockIndex) {
                contentToProcess =
                    contentToProcess
                        .substring(startContentIndex, endBlockIndex)
                        .trim();
                Logger.debug(
                  'Extracted content from markdown code block: ${contentToProcess.substring(0, contentToProcess.length.clamp(0, 100))}${contentToProcess.length > 100 ? "..." : ""}',
                );
              }
            }
          }

          // Check if the content might be JSON
          if (contentToProcess.trim().startsWith('{') &&
              contentToProcess.trim().endsWith('}')) {
            Map<String, dynamic>? jsonResponse;

            // First attempt: try parsing as-is
            try {
              jsonResponse =
                  json.decode(contentToProcess) as Map<String, dynamic>;
              Logger.debug(
                'Successfully parsed JSON on first attempt in chat provider',
              );
            } catch (e) {
              Logger.debug(
                'First JSON parse attempt failed in chat provider: $e',
              );

              // Second attempt: try to fix common JSON issues
              try {
                String cleanedContent = _cleanJsonContent(contentToProcess);
                jsonResponse =
                    json.decode(cleanedContent) as Map<String, dynamic>;
                Logger.debug(
                  'Successfully parsed JSON after cleaning in chat provider',
                );
              } catch (e2) {
                Logger.debug(
                  'Second JSON parse attempt failed in chat provider: $e2',
                );

                // Third attempt: try to extract JSON fields manually using regex
                jsonResponse = _extractJsonFieldsManually(contentToProcess);
                if (jsonResponse != null) {
                  Logger.debug(
                    'Successfully extracted JSON fields manually in chat provider',
                  );
                } else {
                  Logger.debug(
                    'Manual JSON extraction failed in chat provider',
                  );
                }
              }
            }

            // Process the successfully parsed JSON
            if (jsonResponse != null) {
              // Extract the response content if it exists
              if (jsonResponse.containsKey('response')) {
                responseContent = jsonResponse['response'] as String;

                // Extract and store the user prompt summary if it exists
                if (jsonResponse.containsKey('user_prompt')) {
                  userPromptSummary = jsonResponse['user_prompt'] as String;
                  Logger.debug(
                    'User prompt summary from parsed content: $userPromptSummary',
                  );
                }

                // Log the parsed response
                Logger.debug(
                  'Successfully parsed JSON response in chat provider:',
                );
                Logger.debug('Response content: $responseContent');
              }
            } else {
              Logger.debug(
                'All JSON parsing attempts failed, using content as is',
              );
            }
          }
        }

        // Create metadata with user prompt if available
        Map<String, dynamic>? metadata;
        if (userPromptSummary.isNotEmpty) {
          metadata = {'user_prompt': userPromptSummary};
          Logger.debug(
            'Created metadata with user_prompt: "$userPromptSummary"',
          );
        } else {
          Logger.debug('No user_prompt summary available for metadata');
        }

        // Create a new assistant message
        final assistantMessage = ChatMessage(
          id: IdUtils.generateId(),
          role: MessageRole.assistant,
          content: responseContent,
          timestamp: DateTime.now(),
          metadata: metadata,
        );

        // Add the assistant message to the database
        await _repository.addMessageToChat(
          chat: chat!,
          role: MessageRole.assistant,
          content: responseContent,
          metadata: metadata,
        );

        // Update the state with the assistant message
        state = state.copyWith(
          messages: [...state.messages, assistantMessage],
          isLoading: false,
        );
      } else {
        // Handle error
        Logger.error('Error from Gemini API', response.errorMessage);
        state = state.copyWith(
          isLoading: false,
          errorMessage:
              'Error from AI: ${response.errorMessage ?? "Something went wrong. Please try again later."}',
        );
      }
    } catch (e) {
      Logger.error('Error sending message', e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error sending message: $e',
      );
    }
  }

  /// Clear the error message
  void clearError() {
    state = state.clearError();
  }

  /// Update a message in the chat
  Future<bool> updateMessage(
    String messageId, {
    required bool isSavedAsNote,
  }) async {
    try {
      // Find the message in the current state
      final messageIndex = state.messages.indexWhere((m) => m.id == messageId);
      if (messageIndex == -1) {
        Logger.error('Message not found in chat state: $messageId');
        return false;
      }

      // Get the original message
      final originalMessage = state.messages[messageIndex];

      // Create an updated message
      final updatedMessage = originalMessage.copyWith(
        isSavedAsNote: isSavedAsNote,
      );

      // Get the chat from the database
      final chat = await _repository.getChatByIdeabookId(_ideabookId);
      if (chat == null) {
        Logger.error('Chat not found for ideabook: $_ideabookId');
        return false;
      }

      // Update the message in the repository
      final success = await _repository.updateMessage(
        chat: chat,
        messageId: messageId,
        updatedMessage: updatedMessage,
      );

      if (success) {
        // Update the message in the state
        final updatedMessages = List<ChatMessage>.from(state.messages);
        updatedMessages[messageIndex] = updatedMessage;

        // Update the state
        state = state.copyWith(messages: updatedMessages);
        Logger.debug('Message $messageId updated successfully in state');
      }

      return success;
    } catch (e) {
      Logger.error('Error updating message', e);
      return false;
    }
  }

  /// Clear all chat history
  Future<void> clearChatHistory() async {
    try {
      Logger.debug('Clearing chat history for ideabook: $_ideabookId');

      // Get the current chat
      final chat = await _repository.getChatByIdeabookId(_ideabookId);

      if (chat != null) {
        // Delete the existing chat from the database
        await _repository.deleteChat(chat.id);
        Logger.debug('Deleted chat with ID: ${chat.id}');

        // Create a new empty chat
        await _repository.createChat(ideabookId: _ideabookId);
        Logger.debug('Created new empty chat for ideabook: $_ideabookId');
      }

      // Clear the messages in the UI
      state = state.copyWith(messages: []);
      Logger.debug('Chat history cleared successfully');
    } catch (e) {
      Logger.error('Error clearing chat history', e);
      state = state.copyWith(errorMessage: 'Error clearing chat history: $e');
    }
  }

  /// Set an error message in the state
  void setError(String message) {
    state = state.copyWith(errorMessage: message);
  }

  /// Clean JSON content to fix common formatting issues
  String _cleanJsonContent(String content) {
    try {
      // Remove any leading/trailing whitespace
      String cleaned = content.trim();

      // Fix common issues with JSON strings containing unescaped characters
      // Replace literal newlines with escaped newlines in JSON string values
      cleaned = _escapeJsonStringValues(cleaned);

      // Remove any trailing commas before closing braces/brackets
      cleaned = cleaned.replaceAll(RegExp(r',(\s*[}\]])'), r'$1');

      return cleaned;
    } catch (e) {
      Logger.debug('Error cleaning JSON content: $e');
      return content;
    }
  }

  /// Escape special characters in JSON string values
  String _escapeJsonStringValues(String content) {
    try {
      // This is a simplified approach to escape newlines and other control characters
      // in JSON string values while preserving the JSON structure

      // Split by quotes to identify string values
      final parts = <String>[];
      bool inString = false;
      String current = '';

      for (int i = 0; i < content.length; i++) {
        final char = content[i];

        if (char == '"' && (i == 0 || content[i - 1] != '\\')) {
          // Toggle string state
          inString = !inString;
          current += char;

          if (!inString) {
            // End of string, add to parts
            parts.add(current);
            current = '';
          }
        } else if (inString) {
          // Inside a string, escape special characters
          switch (char) {
            case '\n':
              current += '\\n';
              break;
            case '\r':
              current += '\\r';
              break;
            case '\t':
              current += '\\t';
              break;
            case '\\':
              if (i + 1 < content.length &&
                  content[i + 1] != 'n' &&
                  content[i + 1] != 'r' &&
                  content[i + 1] != 't' &&
                  content[i + 1] != '"' &&
                  content[i + 1] != '\\') {
                current += '\\\\';
              } else {
                current += char;
              }
              break;
            default:
              current += char;
          }
        } else {
          // Outside string, add as-is
          current += char;
        }
      }

      // Add any remaining content
      if (current.isNotEmpty) {
        parts.add(current);
      }

      return parts.join('');
    } catch (e) {
      Logger.debug('Error escaping JSON string values: $e');
      return content;
    }
  }

  /// Extract JSON fields manually using regex as a fallback
  Map<String, dynamic>? _extractJsonFieldsManually(String content) {
    try {
      final result = <String, dynamic>{};

      // Extract user_prompt field
      final userPromptMatch = RegExp(
        r'"user_prompt"\s*:\s*"([^"]*(?:\\.[^"]*)*)"',
        dotAll: true,
      ).firstMatch(content);
      if (userPromptMatch != null) {
        result['user_prompt'] =
            userPromptMatch.group(1)?.replaceAll(r'\"', '"') ?? '';
      }

      // Extract response field - this is more complex as it can contain newlines and quotes
      final responseMatch = RegExp(
        r'"response"\s*:\s*"(.*)"(?:\s*}?\s*$)',
        dotAll: true,
      ).firstMatch(content);
      if (responseMatch != null) {
        String responseContent = responseMatch.group(1) ?? '';

        // Clean up the response content
        responseContent = responseContent
            .replaceAll(r'\"', '"') // Unescape quotes
            .replaceAll(
              r'\\n',
              '\n',
            ) // Convert escaped newlines to actual newlines
            .replaceAll(r'\\r', '\r') // Convert escaped carriage returns
            .replaceAll(r'\\t', '\t') // Convert escaped tabs
            .replaceAll(r'\\\\', '\\'); // Convert escaped backslashes

        // Remove trailing quote and brace if present
        if (responseContent.endsWith('"}')) {
          responseContent = responseContent.substring(
            0,
            responseContent.length - 2,
          );
        } else if (responseContent.endsWith('"')) {
          responseContent = responseContent.substring(
            0,
            responseContent.length - 1,
          );
        }

        result['response'] = responseContent;
      }

      // Return result if we found at least one field
      if (result.isNotEmpty) {
        Logger.debug(
          'Manual extraction found ${result.keys.length} fields: ${result.keys.join(', ')}',
        );
        return result;
      }

      return null;
    } catch (e) {
      Logger.debug('Error in manual JSON field extraction: $e');
      return null;
    }
  }
}

/// Provider for the chat notifier
final chatNotifierProvider =
    StateNotifierProvider.family<ChatNotifier, ChatState, String>((
      ref,
      ideabookId,
    ) {
      final repository = ref.watch(chatRepositoryProvider);
      final geminiChatService = ref.watch(geminiChatServiceProvider);
      final llmPrompts = ref.watch(llmPromptsProvider);
      final ideaRepository = ref.watch(ideaRepositoryProvider);
      return ChatNotifier(
        repository,
        geminiChatService,
        llmPrompts,
        ideaRepository,
        ideabookId,
        ref,
      );
    });
