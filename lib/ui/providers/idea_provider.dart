import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/repositories/repositories.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for all ideas for a specific ideabook
/// This provider is automatically invalidated when the ideabookIdeasNotifierProvider changes
final ideabookIdeasProvider = FutureProvider.family<List<Idea>, String>((
  ref,
  ideabookId,
) async {
  // Watch the notifier provider to automatically refresh when it changes
  final notifierState = ref.watch(ideabookIdeasNotifierProvider(ideabookId));

  // If the notifier has data, use it directly
  if (notifierState is AsyncData<List<Idea>>) {
    return notifierState.value;
  }

  // Otherwise, fetch from repository
  final repository = ref.watch(ideaRepositoryProvider);
  return repository.getIdeasByIdeabookId(ideabookId);
});

/// Provider for a specific idea by ID
/// Note: This provider requires the ideabook ID to be known in advance
final ideaProvider = FutureProvider.family<Idea?, Map<String, String>>((
  ref,
  params,
) async {
  final repository = ref.watch(ideaRepositoryProvider);
  final ideabookId = params['ideabookId']!;
  final ideaId = params['ideaId']!;
  return repository.getIdeaById(ideabookId, ideaId);
});

/// Notifier for managing ideas for a specific ideabook
class IdeabookIdeasNotifier extends StateNotifier<AsyncValue<List<Idea>>> {
  final IdeaRepository _repository;
  final String _ideabookId;

  IdeabookIdeasNotifier(this._repository, this._ideabookId)
    : super(const AsyncValue.loading()) {
    _loadIdeas();
  }

  /// Load all ideas for the ideabook from the database
  Future<void> _loadIdeas() async {
    Logger.debug('Loading ideas for ideabook $_ideabookId');
    try {
      state = const AsyncValue.loading();
      final ideas = await _repository.getIdeasByIdeabookId(_ideabookId);
      Logger.debug('Loaded ${ideas.length} ideas for ideabook $_ideabookId');
      state = AsyncValue.data(ideas);
    } catch (e, stack) {
      Logger.error('Error loading ideas for ideabook $_ideabookId', e);
      state = AsyncValue.error(e, stack);
    }
  }

  /// Refresh the ideas list
  Future<void> refresh() async {
    await _loadIdeas();
  }

  /// Create a new idea
  Future<Idea> createIdea({required String content}) async {
    Logger.debug('Creating new idea for ideabook $_ideabookId');
    Logger.debug('Content: $content');

    try {
      final newIdea = await _repository.createIdea(
        ideabookId: _ideabookId,
        content: content,
      );

      Logger.debug('New idea created with ID: ${newIdea.id}');

      // Refresh the list
      _loadIdeas();

      return newIdea;
    } catch (e) {
      Logger.error('Failed to create new idea', e);
      rethrow;
    }
  }

  /// Update an idea
  Future<bool> updateIdea(Idea idea) async {
    final result = await _repository.updateIdea(_ideabookId, idea);

    // Refresh the list
    if (result) {
      _loadIdeas();
    }

    return result;
  }

  /// Delete an idea
  Future<bool> deleteIdea(String id) async {
    final result = await _repository.deleteIdea(_ideabookId, id);

    // Refresh the list
    if (result) {
      _loadIdeas();
    }

    return result;
  }
}

/// Provider for the ideas notifier for a specific ideabook
final ideabookIdeasNotifierProvider = StateNotifierProvider.family<
  IdeabookIdeasNotifier,
  AsyncValue<List<Idea>>,
  String
>((ref, ideabookId) {
  final repository = ref.watch(ideaRepositoryProvider);
  return IdeabookIdeasNotifier(repository, ideabookId);
});
