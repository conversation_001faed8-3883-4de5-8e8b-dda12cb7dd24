import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enum for audio processing state
enum AudioProcessingState {
  /// Not processing
  idle,

  /// Processing in progress
  processing,

  /// Processing completed
  completed,

  /// Processing failed
  failed,
}

/// Provider for the chat recording mode
/// When not null, the chat input is in recording mode
final chatRecordingModeProvider = StateProvider<bool>((ref) => false);

/// Provider for the chat transcription result
/// When not null, the transcription was successful and contains the transcribed text
final chatTranscriptionResultProvider = StateProvider<String?>((ref) => null);

/// Provider for the audio processing state
/// Used to track the state of audio processing (transcription)
final audioProcessingStateProvider = StateProvider<AudioProcessingState>((ref) => AudioProcessingState.idle);
