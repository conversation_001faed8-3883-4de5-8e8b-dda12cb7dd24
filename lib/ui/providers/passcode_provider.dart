import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/security/passcode_storage.dart';
import 'package:noeji/utils/logger.dart';

/// Provider to check if a passcode has been set (async version)
final passcodeSetProvider = FutureProvider<bool>((ref) async {
  return PasscodeStorage.isPasscodeSet();
});

/// Cached state of whether a passcode is set
/// This avoids the delay when checking in the UI
class PasscodeSetState {
  /// Whether a passcode is set
  final bool isSet;

  /// Constructor
  const PasscodeSetState({this.isSet = false});

  /// Create a copy with updated values
  PasscodeSetState copyWith({bool? isSet}) {
    return PasscodeSetState(isSet: isSet ?? this.isSet);
  }
}

/// Notifier for the cached passcode set state
class PasscodeSetNotifier extends StateNotifier<PasscodeSetState> {
  /// Constructor
  PasscodeSetNotifier() : super(const PasscodeSetState()) {
    // Initialize the state when created
    _initializeState();
  }

  /// Initialize the state by checking Firestore
  Future<void> _initializeState() async {
    Logger.debug('Initializing cached passcode set state');
    final isSet = await PasscodeStorage.isPasscodeSet();
    state = PasscodeSetState(isSet: isSet);
    Logger.debug('Cached passcode set state initialized: $isSet');
  }

  /// Update the state when passcode is set or cleared
  void updateState(bool isSet) {
    Logger.debug('Updating cached passcode set state: $isSet');
    state = state.copyWith(isSet: isSet);
  }
}

/// Provider for cached passcode set state
final passcodeSetCachedProvider =
    StateNotifierProvider<PasscodeSetNotifier, PasscodeSetState>((ref) {
      return PasscodeSetNotifier();
    });

/// Passcode state
class PasscodeState {
  /// Whether the passcode is being validated
  final bool isValidating;

  /// Whether the passcode is being set for the first time
  final bool isSettingNew;

  /// Whether the passcode is being confirmed
  final bool isConfirming;

  /// Whether the passcode is being changed
  final bool isChangingPasscode;

  /// The current passcode being entered
  final String currentPasscode;

  /// The passcode to confirm
  final String passcodeToConfirm;

  /// The old passcode when changing passcode
  final String oldPasscode;

  /// Error message, if any
  final String? errorMessage;

  /// Whether validation was successful
  final bool validationSuccess;

  /// Whether setting the passcode was successful
  final bool setSuccess;

  /// Whether we're in the step of entering the old passcode
  bool get isEnteringOldPasscode =>
      isChangingPasscode && oldPasscode.isEmpty && !isConfirming;

  /// Whether we're in the step of entering the new passcode
  bool get isEnteringNewPasscode =>
      isChangingPasscode && oldPasscode.isNotEmpty && !isConfirming;

  /// Constructor
  const PasscodeState({
    this.isValidating = false,
    this.isSettingNew = false,
    this.isConfirming = false,
    this.isChangingPasscode = false,
    this.currentPasscode = '',
    this.passcodeToConfirm = '',
    this.oldPasscode = '',
    this.errorMessage,
    this.validationSuccess = false,
    this.setSuccess = false,
  });

  /// Create a copy with updated values
  PasscodeState copyWith({
    bool? isValidating,
    bool? isSettingNew,
    bool? isConfirming,
    bool? isChangingPasscode,
    String? currentPasscode,
    String? passcodeToConfirm,
    String? oldPasscode,
    String? errorMessage,
    bool? validationSuccess,
    bool? setSuccess,
  }) {
    return PasscodeState(
      isValidating: isValidating ?? this.isValidating,
      isSettingNew: isSettingNew ?? this.isSettingNew,
      isConfirming: isConfirming ?? this.isConfirming,
      isChangingPasscode: isChangingPasscode ?? this.isChangingPasscode,
      currentPasscode: currentPasscode ?? this.currentPasscode,
      passcodeToConfirm: passcodeToConfirm ?? this.passcodeToConfirm,
      oldPasscode: oldPasscode ?? this.oldPasscode,
      errorMessage: errorMessage,
      validationSuccess: validationSuccess ?? this.validationSuccess,
      setSuccess: setSuccess ?? this.setSuccess,
    );
  }
}

/// Notifier for managing passcode state
class PasscodeNotifier extends StateNotifier<PasscodeState> {
  /// Constructor
  PasscodeNotifier() : super(const PasscodeState());

  /// Start setting a new passcode
  void startSettingPasscode() {
    Logger.debug('PasscodeNotifier: Starting passcode setup mode');
    state = const PasscodeState(
      isSettingNew: true,
      currentPasscode: '',
      passcodeToConfirm: '',
      errorMessage: null,
    );
  }

  /// Start validating an existing passcode
  void startValidatingPasscode() {
    Logger.debug('PasscodeNotifier: Starting passcode validation mode');
    state = const PasscodeState(
      isValidating: true,
      currentPasscode: '',
      errorMessage: null,
    );
  }

  /// Start changing an existing passcode
  void startChangingPasscode() {
    Logger.debug('PasscodeNotifier: Starting passcode change mode');
    state = const PasscodeState(
      isChangingPasscode: true,
      currentPasscode: '',
      passcodeToConfirm: '',
      oldPasscode: '',
      errorMessage: null,
    );
  }

  /// Add a digit to the current passcode
  void addDigit(String digit) {
    Logger.debug('PasscodeNotifier: Adding digit: $digit');
    Logger.debug(
      'PasscodeNotifier: Current state - isConfirming: ${state.isConfirming}, currentPasscode length: ${state.currentPasscode.length}, passcodeToConfirm length: ${state.passcodeToConfirm.length}',
    );

    if (state.isConfirming) {
      // Adding to confirmation passcode
      if (state.passcodeToConfirm.length < 6) {
        Logger.debug(
          'PasscodeNotifier: Adding digit to confirmation passcode (current length: ${state.passcodeToConfirm.length})',
        );
        state = state.copyWith(
          passcodeToConfirm: state.passcodeToConfirm + digit,
          errorMessage: null,
        );
        Logger.debug(
          'PasscodeNotifier: Confirmation passcode new length: ${state.passcodeToConfirm.length}',
        );
      } else {
        Logger.debug(
          'PasscodeNotifier: Cannot add digit to confirmation passcode - already at max length (6)',
        );
      }
    } else {
      // Adding to initial passcode
      if (state.currentPasscode.length < 6) {
        Logger.debug(
          'PasscodeNotifier: Adding digit to current passcode (current length: ${state.currentPasscode.length})',
        );
        state = state.copyWith(
          currentPasscode: state.currentPasscode + digit,
          errorMessage: null,
        );
        Logger.debug(
          'PasscodeNotifier: Current passcode new length: ${state.currentPasscode.length}',
        );
      } else {
        Logger.debug(
          'PasscodeNotifier: Cannot add digit to current passcode - already at max length (6)',
        );
      }
    }
  }

  /// Remove the last digit from the current passcode
  void removeLastDigit() {
    if (state.isConfirming && state.passcodeToConfirm.isNotEmpty) {
      state = state.copyWith(
        passcodeToConfirm: state.passcodeToConfirm.substring(
          0,
          state.passcodeToConfirm.length - 1,
        ),
        errorMessage: null,
      );
    } else if (!state.isConfirming && state.currentPasscode.isNotEmpty) {
      state = state.copyWith(
        currentPasscode: state.currentPasscode.substring(
          0,
          state.currentPasscode.length - 1,
        ),
        errorMessage: null,
      );
    }
  }

  /// Clear the current passcode
  void clearPasscode() {
    if (state.isConfirming) {
      state = state.copyWith(passcodeToConfirm: '', errorMessage: null);
    } else {
      state = state.copyWith(currentPasscode: '', errorMessage: null);
    }
  }

  /// Move to confirmation step when setting a new passcode
  void moveToConfirmation() {
    if (state.currentPasscode.length < 4) {
      state = state.copyWith(
        errorMessage: 'Passcode must be at least 4 digits',
      );
      return;
    }

    state = state.copyWith(
      isConfirming: true,
      passcodeToConfirm: '',
      errorMessage: null,
    );
  }

  /// Validate the old passcode when changing passcode
  Future<bool> validateOldPasscode() async {
    Logger.debug(
      'PasscodeNotifier: Validating old passcode with length: ${state.currentPasscode.length}',
    );

    if (state.currentPasscode.length != 6) {
      Logger.debug(
        'PasscodeNotifier: Old passcode invalid length (${state.currentPasscode.length} digits)',
      );
      state = state.copyWith(errorMessage: 'Passcode must be exactly 6 digits');

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    Logger.debug(
      'PasscodeNotifier: Calling PasscodeStorage.validatePasscode for old passcode',
    );
    final isValid = await PasscodeStorage.validatePasscode(
      state.currentPasscode,
    );
    Logger.debug('PasscodeNotifier: Old passcode validation result: $isValid');

    if (isValid) {
      Logger.debug(
        'PasscodeNotifier: Old passcode valid, storing and clearing for new passcode entry',
      );
      // Store the old passcode and clear the current passcode for entering the new one
      state = state.copyWith(
        oldPasscode: state.currentPasscode,
        currentPasscode: '',
        errorMessage: null,
      );
    } else {
      Logger.debug('PasscodeNotifier: Old passcode invalid, showing error');
      state = state.copyWith(errorMessage: 'Incorrect passcode');

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });
    }

    return isValid;
  }

  /// Validate the entered passcode
  Future<bool> validatePasscode() async {
    Logger.debug(
      'PasscodeNotifier: Validating passcode with length: ${state.currentPasscode.length}',
    );

    if (state.currentPasscode.length != 6) {
      Logger.debug(
        'PasscodeNotifier: Passcode invalid length (${state.currentPasscode.length} digits)',
      );
      state = state.copyWith(errorMessage: 'Passcode must be exactly 6 digits');

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    Logger.debug(
      'PasscodeNotifier: Calling PasscodeStorage.validatePasscode with passcode: ${state.currentPasscode}',
    );
    final isValid = await PasscodeStorage.validatePasscode(
      state.currentPasscode,
    );
    Logger.debug(
      'PasscodeNotifier: PasscodeStorage.validatePasscode returned: $isValid',
    );

    state = state.copyWith(
      validationSuccess: isValid,
      errorMessage: isValid ? null : 'Incorrect passcode',
    );

    if (!isValid) {
      Logger.debug(
        'PasscodeNotifier: Passcode validation failed, resetting input',
      );
      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });
    } else {
      Logger.debug('PasscodeNotifier: Passcode validation successful');
    }

    return isValid;
  }

  /// Save the new passcode
  Future<bool> savePasscode() async {
    if (state.passcodeToConfirm.length != 6) {
      state = state.copyWith(errorMessage: 'Passcode must be exactly 6 digits');

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    if (state.currentPasscode != state.passcodeToConfirm) {
      state = state.copyWith(
        errorMessage: 'Passcodes do not match',
        isConfirming: false,
        currentPasscode: '',
        passcodeToConfirm: '',
      );

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    final success = await PasscodeStorage.savePasscode(state.currentPasscode);

    state = state.copyWith(
      setSuccess: success,
      errorMessage: success ? null : 'Failed to save passcode',
    );

    if (!success) {
      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });
    }

    return success;
  }

  /// Save the new passcode when changing passcode
  Future<bool> saveChangedPasscode() async {
    if (state.passcodeToConfirm.length != 6) {
      state = state.copyWith(errorMessage: 'Passcode must be exactly 6 digits');

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    if (state.currentPasscode != state.passcodeToConfirm) {
      state = state.copyWith(
        errorMessage: 'Passcodes do not match',
        isConfirming: false,
        currentPasscode: '',
        passcodeToConfirm: '',
      );

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    // Ensure the new passcode is different from the old one
    if (state.currentPasscode == state.oldPasscode) {
      state = state.copyWith(
        errorMessage: 'New passcode must be different from the old one',
        isConfirming: false,
        currentPasscode: '',
        passcodeToConfirm: '',
      );

      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });

      return false;
    }

    final success = await PasscodeStorage.savePasscode(state.currentPasscode);

    state = state.copyWith(
      setSuccess: success,
      errorMessage: success ? null : 'Failed to save passcode',
    );

    if (!success) {
      // Reset input after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        resetInput();
      });
    }

    return success;
  }

  /// Reset the state
  void reset() {
    Logger.debug('PasscodeNotifier: Resetting passcode state');
    state = const PasscodeState();
  }

  /// Reset just the passcode input, keeping the current mode
  void resetInput() {
    if (state.isValidating) {
      state = state.copyWith(currentPasscode: '', errorMessage: null);
    } else if (state.isChangingPasscode) {
      if (state.isEnteringOldPasscode) {
        state = state.copyWith(currentPasscode: '', errorMessage: null);
      } else if (state.isEnteringNewPasscode) {
        state = state.copyWith(currentPasscode: '', errorMessage: null);
      } else if (state.isConfirming) {
        state = state.copyWith(passcodeToConfirm: '', errorMessage: null);
      }
    } else if (state.isSettingNew) {
      if (state.isConfirming) {
        state = state.copyWith(passcodeToConfirm: '', errorMessage: null);
      } else {
        state = state.copyWith(currentPasscode: '', errorMessage: null);
      }
    }
  }
}

/// Provider for passcode state
final passcodeProvider = StateNotifierProvider<PasscodeNotifier, PasscodeState>(
  (ref) {
    return PasscodeNotifier();
  },
);
