/// A generic interface for filter criteria
abstract class FilterCriteria<T> {
  /// Check if the filter is active
  bool get isActive;

  /// Apply the filter to an item
  bool apply(T item);
}

/// A generic filter provider that can be used with any type of data
class FilterProvider<T> {
  /// The list of filter criteria
  final List<FilterCriteria<T>> _criteria = [];

  /// Add a filter criterion
  void addCriterion(FilterCriteria<T> criterion) {
    _criteria.add(criterion);
  }

  /// Remove a filter criterion
  void removeCriterion(FilterCriteria<T> criterion) {
    _criteria.remove(criterion);
  }

  /// Clear all filter criteria
  void clearCriteria() {
    _criteria.clear();
  }

  /// Get all criteria
  List<FilterCriteria<T>> get criteria => List.unmodifiable(_criteria);

  /// Check if any filter is active
  bool get isActive => _criteria.any((criterion) => criterion.isActive);

  /// Apply all active filters to a list of items
  List<T> apply(List<T> items) {
    // If no filters are active, return the original list
    if (!isActive) {
      return items;
    }

    // Apply all active filters
    return items.where((item) {
      // An item passes if it satisfies all active criteria
      return _criteria.every((criterion) {
        // Skip inactive criteria
        if (!criterion.isActive) {
          return true;
        }
        // Apply the criterion
        return criterion.apply(item);
      });
    }).toList();
  }
}

/// A provider for a generic filter
/// This is a template that should be specialized for specific types
/// Example: final myFilterProvider = Provider((ref) => FilterProvider&lt;MyType&gt;());
///
/// Not used directly in this implementation

/// A string filter criterion that checks if a string contains a query
class StringContainsFilterCriterion<T> implements FilterCriteria<T> {
  /// The query to search for
  final String query;

  /// A function that extracts the string to search from the item
  final String Function(T item) stringExtractor;

  /// Creates a new StringContainsFilterCriterion
  StringContainsFilterCriterion({
    required this.query,
    required this.stringExtractor,
  });

  @override
  bool get isActive => query.trim().isNotEmpty;

  @override
  bool apply(T item) {
    final string = stringExtractor(item);
    return string.toLowerCase().contains(query.toLowerCase());
  }
}

/// An equality filter criterion that checks if a property equals a value
class EqualityFilterCriterion<T, V> implements FilterCriteria<T> {
  /// The value to compare against
  final V? value;

  /// A function that extracts the property to compare from the item
  final V Function(T item) propertyExtractor;

  /// Creates a new EqualityFilterCriterion
  EqualityFilterCriterion({
    required this.value,
    required this.propertyExtractor,
  });

  @override
  bool get isActive => value != null;

  @override
  bool apply(T item) {
    final property = propertyExtractor(item);
    return property == value;
  }
}
