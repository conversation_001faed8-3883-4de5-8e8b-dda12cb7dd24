import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/ui/providers/firestore_listener_provider.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';

/// Controller for managing the Firestore notes listener
/// This provider is used to start and stop the Firestore listener
/// when navigating to and from the notes tab
class FirestoreNotesListenerController extends StateNotifier<bool> {
  final Ref _ref;
  final String _ideabookId;

  /// Constructor
  FirestoreNotesListenerController(this._ref, this._ideabookId) : super(false);

  /// Start listening to Firestore updates
  void startListening() {
    if (state) {
      Logger.debug(
        'Firestore notes listener already active for ideabook $_ideabookId',
      );
      return;
    }

    Logger.debug(
      'Starting Firestore notes listener for ideabook $_ideabookId with optimized listener pool',
    );

    try {
      // Start listening
      final notifier = _ref.read(
        firestoreNotesListenerNotifierProvider(_ideabookId).notifier,
      );
      notifier.startListening();

      // Listen for errors in the notes provider
      _ref.listen(firestoreNotesListenerNotifierProvider(_ideabookId), (
        previous,
        current,
      ) {
        if (current is AsyncError) {
          // Check if this is a permission error
          final isPermissionError =
              current.error != null &&
              ErrorUtils.isPermissionError(current.error!);

          if (isPermissionError) {
            Logger.error(
              'Permission error detected in notes listener controller',
              current.error,
            );
            // Force stop listening to avoid continuous permission errors
            try {
              // Call directly to the notifier to stop listening
              final notifier = _ref.read(
                firestoreNotesListenerNotifierProvider(_ideabookId).notifier,
              );
              notifier.stopListening();

              // Update our state
              state = false;

              Logger.debug(
                'Successfully stopped notes listener due to permission error',
              );

              // Handle the permission error globally
              ErrorUtils.handleGlobalPermissionError(_ref, current.error!);
            } catch (e) {
              Logger.error(
                'Error stopping notes listener after permission error',
                e,
              );
            }
          }
        }
      });

      state = true;
    } catch (e) {
      Logger.error('Error starting Firestore notes listener', e);

      // Check if this is a permission error
      if (ErrorUtils.isPermissionError(e)) {
        // Handle the permission error globally
        ErrorUtils.handleGlobalPermissionError(_ref, e);
      }

      state = false;
    }
  }

  /// Stop listening to Firestore updates
  /// Note: This method is intentionally empty to prevent stopping the listener
  /// when navigating away from the tab. The listener pool will handle TTL-based cleanup.
  void stopListening() {
    // Don't stop the listener when navigating away from the tab
    // The listener pool will handle TTL-based cleanup
    Logger.debug(
      'Not stopping Firestore notes listener for ideabook $_ideabookId - using listener pool TTL instead',
    );

    // Just update the state to indicate we're not actively listening in this controller
    state = false;
  }

  /// Update the sort order of a note
  /// This is used when reordering notes via drag-and-drop
  Future<bool> updateNoteSortOrder(String noteId, double newSortOrder) async {
    Logger.debug(
      'FirestoreNotesListenerController: Updating sort order for note $noteId to $newSortOrder',
    );

    try {
      // Update the listener state immediately to reflect the reordering in the UI
      final listenerNotifier = _ref.read(
        firestoreNotesListenerNotifierProvider(_ideabookId).notifier,
      );
      listenerNotifier.updateWithReorderedNotes(noteId, newSortOrder);

      // Update the sort order in Firestore using the service directly
      final firestoreService = _ref.read(firestoreServiceProvider);
      final result = await firestoreService.updateNoteSortOrder(
        _ideabookId,
        noteId,
        newSortOrder,
      );

      Logger.debug(
        'FirestoreNotesListenerController: Updated sort order for note $noteId, result: $result',
      );
      return result;
    } catch (e) {
      Logger.error(
        'FirestoreNotesListenerController: Error updating sort order for note $noteId',
        e,
      );
      return false;
    }
  }
}

/// Provider for the Firestore notes listener controller
final firestoreNotesListenerControllerProvider = StateNotifierProvider.family<
  FirestoreNotesListenerController,
  bool,
  String
>((ref, ideabookId) {
  return FirestoreNotesListenerController(ref, ideabookId);
});
