import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enum representing the state of audio processing
enum AudioProcessingState {
  /// Recording is in progress
  recording,
  
  /// Audio is being processed (transcribed)
  processing,
  
  /// Processing is complete
  completed,
}

/// Provider to track the state of audio processing
final audioProcessingStateProvider = StateProvider<AudioProcessingState>(
  (ref) => AudioProcessingState.recording,
);
