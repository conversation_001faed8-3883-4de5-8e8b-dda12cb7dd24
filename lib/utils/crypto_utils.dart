import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:noeji/utils/logger.dart';

/// Utility functions for cryptographic operations
class CryptoUtils {
  /// Creates a SHA-256 hash of the given passcode
  ///
  /// This is a one-way hash, so the original passcode cannot be recovered
  /// The hash is returned as a hexadecimal string
  static String hashPasscode(String passcode) {
    try {
      final bytes = utf8.encode(passcode);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      Logger.error('Error hashing passcode', e);
      // Return a placeholder in case of error
      return '';
    }
  }

  /// Validates a passcode against a stored hash
  ///
  /// Returns true if the hash of the provided passcode matches the stored hash
  static bool validatePasscode(String passcode, String storedHash) {
    try {
      final hash = hashPasscode(passcode);
      return hash == storedHash;
    } catch (e) {
      Logger.error('Error validating passcode', e);
      return false;
    }
  }
}
