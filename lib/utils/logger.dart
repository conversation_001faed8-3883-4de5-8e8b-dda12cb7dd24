/// Simple logger utility for the app
class Logger {
  /// Log a debug message
  static void debug(String message) {
    // In a real app, this would use a proper logging framework
    // For now, we'll just print to the console in debug mode
    // ignore: avoid_print
    print('[DEBUG] $message');
  }

  /// Log an error message
  static void error(String message, [Object? error]) {
    // In a real app, this would use a proper logging framework
    // For now, we'll just print to the console in debug mode
    // ignore: avoid_print
    print('[ERROR] $message');
    if (error != null) {
      // ignore: avoid_print
      print('[ERROR] $error');
    }
  }
}
