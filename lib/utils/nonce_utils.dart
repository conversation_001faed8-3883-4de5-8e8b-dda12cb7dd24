import 'dart:math';

/// Utility functions for generating cryptographically secure nonces
/// Used for Sign in with Apple authentication flow
class NonceUtils {
  /// Generates a cryptographically secure random nonce string
  /// 
  /// [length] - The length of the nonce string (default: 32)
  /// Returns a random string using alphanumeric characters and safe symbols
  static String generateNonce([int length = 32]) {
    const charset = 
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(
      length, 
      (_) => charset[random.nextInt(charset.length)],
    ).join();
  }
}
