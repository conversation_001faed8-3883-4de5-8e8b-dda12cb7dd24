import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/utils/logger.dart';

/// Provider to track global permission error state
final globalPermissionErrorProvider = StateProvider<bool>((ref) => false);

/// Utility class for handling errors in a consistent way
class ErrorUtils {
  /// Generic permission denied message to show to users
  static const String permissionDeniedMessage =
      'Hmm, looks like we hit a permission bump! 🤔';

  /// Check if an error is a permission error
  ///
  /// Returns true if the error is related to permissions or App Check
  static bool isPermissionError(Object? error) {
    try {
      if (error == null) {
        return false;
      }

      final errorString = error.toString().toLowerCase();
      return errorString.contains('permission') ||
          errorString.contains('permission_denied') ||
          errorString.contains('unauthorized') ||
          errorString.contains('access_denied') ||
          errorString.contains('insufficient_permissions') ||
          errorString.contains('cloud_firestore/permission-denied') ||
          errorString.contains('app check token is invalid') ||
          errorString.contains('firebase_auth/internal-error') ||
          errorString.contains('firautherrordomaincode=17999');
    } catch (e) {
      // If there's an error checking the error, log it but don't crash
      Logger.error('Error in isPermissionError', e);
      return false;
    }
  }

  /// Sanitize error message for display to users
  ///
  /// If the error is a permission error, returns a generic message
  /// Otherwise, returns the original error message
  static String sanitizeErrorMessage(Object? error) {
    try {
      if (error == null) {
        return 'Unknown error';
      }

      // Log the original error for debugging
      Logger.error('Original error before sanitizing', error);

      // Check if this is a permission error
      if (isPermissionError(error)) {
        Logger.debug('Sanitizing permission error message');
        return permissionDeniedMessage;
      }

      // For other errors, return the original message
      return error.toString();
    } catch (e) {
      // If there's an error sanitizing the error, log it but don't crash
      Logger.error('Error in sanitizeErrorMessage', e);
      return 'Error processing error message';
    }
  }

  /// Handle a permission error globally
  ///
  /// This will set the global permission error state to true,
  /// which will trigger the error overlay to be displayed
  static void handleGlobalPermissionError(dynamic ref, Object error) {
    try {
      if (ref == null) {
        Logger.error('Cannot handle global permission error: ref is null');
        return;
      }

      if (isPermissionError(error)) {
        Logger.error('Global permission error detected', error);
        // Set the global permission error state to true
        try {
          if (ref is WidgetRef) {
            ref.read(globalPermissionErrorProvider.notifier).state = true;
          } else if (ref is Ref) {
            ref.read(globalPermissionErrorProvider.notifier).state = true;
          } else {
            Logger.error(
              'Cannot handle global permission error: ref is not a WidgetRef or Ref',
            );
          }
        } catch (e) {
          Logger.error('Error setting global permission error state', e);
        }
      }
    } catch (e) {
      // If there's an error in the error handler itself, log it but don't crash
      Logger.error('Error in handleGlobalPermissionError', e);
    }
  }
}
