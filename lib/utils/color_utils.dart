import 'package:flutter/material.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/utils/logger.dart';

/// Utility functions for working with colors
class ColorUtils {
  /// Convert a color string to an IdeabookColor enum
  ///
  /// If the color string is not recognized, returns IdeabookColor.none
  static IdeabookColor stringToIdeabookColor(
    String? colorString, {
    String logPrefix = '',
  }) {
    if (colorString == null) {
      Logger.debug('${logPrefix}No color provided, using default');
      return IdeabookColor.none;
    }

    // Map the color string to the enum
    IdeabookColor result;
    switch (colorString.toLowerCase()) {
      case 'red':
        result = IdeabookColor.red;
        break;
      case 'green':
        result = IdeabookColor.green;
        break;
      case 'blue':
        result = IdeabookColor.blue;
        break;
      case 'yellow':
        result = IdeabookColor.yellow;
        break;
      case 'orange':
        result = IdeabookColor.orange;
        break;
      case 'purple':
        result = IdeabookColor.purple;
        break;
      default:
        Logger.debug(
          '${logPrefix}Unrecognized color: $colorString, using default',
        );
        result = IdeabookColor.none;
        break;
    }

    Logger.debug(
      '${logPrefix}Converted color string "$colorString" to ${result.name} (index: ${result.index})',
    );
    return result;
  }

  /// Get the optimal text color (black or white) for maximum contrast against a background color
  ///
  /// This function uses Flutter's built-in brightness estimation which is based on
  /// relative luminance calculations following WCAG guidelines for accessibility.
  ///
  /// [backgroundColor] The background color to calculate contrast against
  /// [darkTextColor] The dark text color to use (defaults to black-ish #333333)
  /// [lightTextColor] The light text color to use (defaults to white)
  ///
  /// Returns [darkTextColor] for light backgrounds and [lightTextColor] for dark backgrounds
  static Color getContrastingTextColor(
    Color backgroundColor, {
    Color darkTextColor = const Color(0xFF333333), // Black-ish
    Color lightTextColor = Colors.white,
  }) {
    // Use Flutter's built-in brightness estimation which calculates relative luminance
    // This follows WCAG guidelines and provides accurate contrast determination
    final backgroundBrightness = ThemeData.estimateBrightnessForColor(
      backgroundColor,
    );

    // If the background is dark, use light text
    // If the background is light, use dark text
    final textColor =
        backgroundBrightness == Brightness.dark
            ? lightTextColor
            : darkTextColor;

    Logger.debug(
      'Background color: ${backgroundColor.toString()}, '
      'brightness: ${backgroundBrightness.name}, '
      'text color: ${textColor.toString()}',
    );

    return textColor;
  }
}
