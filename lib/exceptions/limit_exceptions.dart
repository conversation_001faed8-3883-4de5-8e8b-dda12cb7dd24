import 'package:noeji/services/paywall/paywall_trigger_service.dart';

/// Base class for limit-related exceptions
abstract class LimitException implements Exception {
  final String message;
  final LimitType limitType;

  const LimitException(this.message, this.limitType);

  @override
  String toString() => message;
}

/// Exception thrown when ideabook limit is reached
class IdeabookLimitException extends LimitException {
  const IdeabookLimitException(String message)
    : super(message, LimitType.ideabooks);
}

/// Exception thrown when ideas limit is reached
class IdeasLimitException extends LimitException {
  const IdeasLimitException(String message) : super(message, LimitType.ideas);
}

/// Exception thrown when notes limit is reached
class NotesLimitException extends LimitException {
  const NotesLimitException(String message) : super(message, LimitType.notes);
}

/// Exception thrown when chat limit is reached
class ChatLimitException extends LimitException {
  const ChatLimitException(String message) : super(message, LimitType.chat);
}
