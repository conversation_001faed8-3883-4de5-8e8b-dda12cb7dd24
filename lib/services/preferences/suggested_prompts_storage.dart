import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving previous suggested prompts
class SuggestedPromptsStorage {
  static const String _suggestedPromptsKey = 'noeji_suggested_prompts';

  /// Save the current suggested prompts to persistent storage
  /// Only stores the 5 most recent prompts
  static Future<bool> saveSuggestedPrompts(
    String ideabookId,
    List<String> prompts,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Create a map with ideabook ID as key and prompts as value
      final Map<String, List<String>> allPrompts =
          await _loadAllSuggestedPrompts();

      // Update the prompts for this ideabook
      // Only keep the 5 most recent prompts
      allPrompts[ideabookId] = prompts.take(5).toList();

      // Convert to JSON string
      final jsonString = json.encode(allPrompts);

      // Save to SharedPreferences
      final result = await prefs.setString(_suggestedPromptsKey, jsonString);

      if (result) {
        Logger.debug(
          'Suggested prompts saved successfully for ideabook: $ideabookId',
        );
      } else {
        Logger.error(
          'Failed to save suggested prompts for ideabook: $ideabookId',
        );
      }

      return result;
    } catch (e) {
      Logger.error('Error saving suggested prompts', e);
      return false;
    }
  }

  /// Load the saved suggested prompts for a specific ideabook from persistent storage
  /// Returns an empty list if no prompts are saved
  static Future<List<String>> loadSuggestedPrompts(String ideabookId) async {
    try {
      final allPrompts = await _loadAllSuggestedPrompts();

      // Get prompts for this ideabook
      final prompts = allPrompts[ideabookId] ?? [];

      Logger.debug(
        'Loaded ${prompts.length} suggested prompts for ideabook: $ideabookId',
      );
      return prompts;
    } catch (e) {
      Logger.error('Error loading suggested prompts', e);
      return [];
    }
  }

  /// Load all suggested prompts for all ideabooks
  static Future<Map<String, List<String>>> _loadAllSuggestedPrompts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_suggestedPromptsKey);

      if (jsonString == null) {
        return {};
      }

      // Parse JSON string
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Convert to Map<String, List<String>>
      final Map<String, List<String>> result = {};

      jsonMap.forEach((key, value) {
        if (value is List) {
          result[key] = value.cast<String>();
        }
      });

      return result;
    } catch (e) {
      Logger.error('Error loading all suggested prompts', e);
      return {};
    }
  }
}
