import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/services/preferences/app_behavior_storage.dart';
import 'package:noeji/services/preferences/color_palette_preference_provider.dart';
import 'package:noeji/services/preferences/color_palette_storage.dart';
import 'package:noeji/services/remote_config/remote_config_service.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';

/// State notifier for app behavior preferences
class AppBehaviorNotifier extends StateNotifier<AppBehaviorState> {
  final RemoteConfigService _remoteConfigService;
  final Ref _ref;

  /// Constructor
  AppBehaviorNotifier(this._remoteConfigService, this._ref)
    : super(const AppBehaviorState()) {
    // Load saved preferences when initialized
    _loadSavedPreferences();
  }

  /// Load the saved preferences from storage
  Future<void> _loadSavedPreferences() async {
    try {
      // Get the default values from Remote Config
      final defaultSendVoiceChatOnFinish = _remoteConfigService.getBool(
        'default_send_voice_chat_on_finish',
      );
      final defaultCopyAsMarkdown = _remoteConfigService.getBool(
        'default_enable_chat_copy_markdown',
      );
      final defaultGenerativeSuggestedPrompts = _remoteConfigService.getBool(
        'default_enable_generative_suggested_prompts',
      );
      final defaultAutoLockIdeabooks = _remoteConfigService.getBool(
        'default_auto_lock_ideabooks',
      );
      final defaultLongPressToRecordNewIdea = _remoteConfigService.getBool(
        'default_enable_long_press_to_record_new_idea',
      );

      // Get default chat style values from Remote Config
      final llmPrompts = LlmPrompts(_remoteConfigService);
      final defaultChatStyles = llmPrompts.getDefaultChatResponseStyle();

      // Load the saved preferences or use the defaults
      final sendVoiceChatOnFinish =
          await AppBehaviorStorage.loadSendVoiceChatOnFinish(
            defaultValue: defaultSendVoiceChatOnFinish,
          );
      final copyAsMarkdown = await AppBehaviorStorage.loadCopyAsMarkdown(
        defaultValue: defaultCopyAsMarkdown,
      );
      final generativeSuggestedPrompts =
          await AppBehaviorStorage.loadGenerativeSuggestedPrompts(
            defaultValue: defaultGenerativeSuggestedPrompts,
          );
      final autoLockIdeabooks = await AppBehaviorStorage.loadAutoLockIdeabooks(
        defaultValue: defaultAutoLockIdeabooks,
      );
      final longPressToRecordNewIdea =
          await AppBehaviorStorage.loadLongPressToRecordNewIdea(
            defaultValue: defaultLongPressToRecordNewIdea,
          );
      final chatStyleVerbosity =
          await AppBehaviorStorage.loadChatStyleVerbosity(
            defaultValue: defaultChatStyles['verbosity'] ?? 'balanced',
          );
      final chatStyleTone = await AppBehaviorStorage.loadChatStyleTone(
        defaultValue: defaultChatStyles['tone'] ?? 'casual',
      );
      final chatStyleOutputFormat =
          await AppBehaviorStorage.loadChatStyleOutputFormat(
            defaultValue: defaultChatStyles['output_format'] ?? 'free_form',
          );

      // Update the state
      state = state.copyWith(
        sendVoiceChatOnFinish: sendVoiceChatOnFinish,
        copyAsMarkdown: copyAsMarkdown,
        generativeSuggestedPrompts: generativeSuggestedPrompts,
        autoLockIdeabooks: autoLockIdeabooks,
        longPressToRecordNewIdea: longPressToRecordNewIdea,
        chatStyleVerbosity: chatStyleVerbosity,
        chatStyleTone: chatStyleTone,
        chatStyleOutputFormat: chatStyleOutputFormat,
        isLoaded: true,
      );

      Logger.debug(
        'App behavior preferences loaded: sendVoiceChatOnFinish=$sendVoiceChatOnFinish, chatStyleVerbosity=$chatStyleVerbosity, chatStyleTone=$chatStyleTone, chatStyleOutputFormat=$chatStyleOutputFormat',
      );
    } catch (e) {
      Logger.error('Error loading app behavior preferences', e);
      // Set loaded to true even on error so UI doesn't wait indefinitely
      state = state.copyWith(isLoaded: true);
    }
  }

  /// Set the send voice chat on finish preference
  Future<void> setSendVoiceChatOnFinish(bool enabled) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveSendVoiceChatOnFinish(
        enabled,
      );

      if (success) {
        // Update the state
        state = state.copyWith(sendVoiceChatOnFinish: enabled);
        Logger.debug('Send voice chat on finish preference updated: $enabled');
      } else {
        Logger.error('Failed to save send voice chat on finish preference');
      }
    } catch (e) {
      Logger.error('Error setting send voice chat on finish preference', e);
    }
  }

  /// Set the copy as markdown preference
  Future<void> setCopyAsMarkdown(bool enabled) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveCopyAsMarkdown(enabled);

      if (success) {
        // Update the state
        state = state.copyWith(copyAsMarkdown: enabled);
        Logger.debug('Copy as markdown preference updated: $enabled');
      } else {
        Logger.error('Failed to save copy as markdown preference');
      }
    } catch (e) {
      Logger.error('Error setting copy as markdown preference', e);
    }
  }

  /// Set the generative suggested prompts preference
  Future<void> setGenerativeSuggestedPrompts(bool enabled) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveGenerativeSuggestedPrompts(
        enabled,
      );

      if (success) {
        // Update the state
        state = state.copyWith(generativeSuggestedPrompts: enabled);
        Logger.debug(
          'Generative suggested prompts preference updated: $enabled',
        );
      } else {
        Logger.error('Failed to save generative suggested prompts preference');
      }
    } catch (e) {
      Logger.error('Error setting generative suggested prompts preference', e);
    }
  }

  /// Set the auto lock ideabooks preference
  Future<void> setAutoLockIdeabooks(bool enabled) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveAutoLockIdeabooks(enabled);

      if (success) {
        // Update the state
        state = state.copyWith(autoLockIdeabooks: enabled);
        Logger.debug('Auto lock ideabooks preference updated: $enabled');
      } else {
        Logger.error('Failed to save auto lock ideabooks preference');
      }
    } catch (e) {
      Logger.error('Error setting auto lock ideabooks preference', e);
    }
  }

  /// Set the long press to record new idea preference
  Future<void> setLongPressToRecordNewIdea(bool enabled) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveLongPressToRecordNewIdea(
        enabled,
      );

      if (success) {
        // Update the state
        state = state.copyWith(longPressToRecordNewIdea: enabled);
        Logger.debug(
          'Long press to record new idea preference updated: $enabled',
        );
      } else {
        Logger.error('Failed to save long press to record new idea preference');
      }
    } catch (e) {
      Logger.error('Error setting long press to record new idea preference', e);
    }
  }

  /// Set the chat style verbosity preference
  Future<void> setChatStyleVerbosity(String verbosity) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveChatStyleVerbosity(
        verbosity,
      );

      if (success) {
        // Update the state
        state = state.copyWith(chatStyleVerbosity: verbosity);
        Logger.debug('Chat style verbosity preference updated: $verbosity');
      } else {
        Logger.error('Failed to save chat style verbosity preference');
      }
    } catch (e) {
      Logger.error('Error setting chat style verbosity preference', e);
    }
  }

  /// Set the chat style tone preference
  Future<void> setChatStyleTone(String tone) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveChatStyleTone(tone);

      if (success) {
        // Update the state
        state = state.copyWith(chatStyleTone: tone);
        Logger.debug('Chat style tone preference updated: $tone');
      } else {
        Logger.error('Failed to save chat style tone preference');
      }
    } catch (e) {
      Logger.error('Error setting chat style tone preference', e);
    }
  }

  /// Set the chat style output format preference
  Future<void> setChatStyleOutputFormat(String outputFormat) async {
    try {
      // Save to storage
      final success = await AppBehaviorStorage.saveChatStyleOutputFormat(
        outputFormat,
      );

      if (success) {
        // Update the state
        state = state.copyWith(chatStyleOutputFormat: outputFormat);
        Logger.debug(
          'Chat style output format preference updated: $outputFormat',
        );
      } else {
        Logger.error('Failed to save chat style output format preference');
      }
    } catch (e) {
      Logger.error('Error setting chat style output format preference', e);
    }
  }

  /// Reset all preferences to defaults
  /// This is called when user wants to manually reset or when a user becomes a free user
  Future<void> resetToDefaults() async {
    try {
      // Reset the send voice chat on finish preference (this method is known to work)
      await AppBehaviorStorage.resetSendVoiceChatOnFinish();

      // Reset other preferences by directly removing them from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove('noeji_copy_as_markdown'),
        prefs.remove('noeji_generative_suggested_prompts'),
        prefs.remove('noeji_auto_lock_ideabooks'),
        prefs.remove('noeji_long_press_to_record_new_idea'),
        prefs.remove('noeji_chat_style_verbosity'),
        prefs.remove('noeji_chat_style_tone'),
        prefs.remove('noeji_chat_style_output_format'),
      ]);

      // Reset color palette preference for free users
      await ColorPaletteStorage.resetSelectedColorPalette();

      // Invalidate color palette preference provider to force reload
      _ref.invalidate(colorPalettePreferenceProvider);

      // Reload preferences
      await _loadSavedPreferences();

      Logger.debug('App behavior preferences reset to defaults');
    } catch (e) {
      Logger.error('Error resetting app behavior preferences', e);
    }
  }
}

/// State class for app behavior preferences
class AppBehaviorState {
  final bool sendVoiceChatOnFinish;
  final bool copyAsMarkdown;
  final bool generativeSuggestedPrompts;
  final bool autoLockIdeabooks;
  final bool longPressToRecordNewIdea;
  final String chatStyleVerbosity;
  final String chatStyleTone;
  final String chatStyleOutputFormat;
  final bool isLoaded;

  const AppBehaviorState({
    this.sendVoiceChatOnFinish = false,
    this.copyAsMarkdown = false,
    this.generativeSuggestedPrompts = false,
    this.autoLockIdeabooks = false,
    this.longPressToRecordNewIdea = false,
    this.chatStyleVerbosity = 'balanced',
    this.chatStyleTone = 'casual',
    this.chatStyleOutputFormat = 'free_form',
    this.isLoaded = false,
  });

  AppBehaviorState copyWith({
    bool? sendVoiceChatOnFinish,
    bool? copyAsMarkdown,
    bool? generativeSuggestedPrompts,
    bool? autoLockIdeabooks,
    bool? longPressToRecordNewIdea,
    String? chatStyleVerbosity,
    String? chatStyleTone,
    String? chatStyleOutputFormat,
    bool? isLoaded,
  }) {
    return AppBehaviorState(
      sendVoiceChatOnFinish:
          sendVoiceChatOnFinish ?? this.sendVoiceChatOnFinish,
      copyAsMarkdown: copyAsMarkdown ?? this.copyAsMarkdown,
      generativeSuggestedPrompts:
          generativeSuggestedPrompts ?? this.generativeSuggestedPrompts,
      autoLockIdeabooks: autoLockIdeabooks ?? this.autoLockIdeabooks,
      longPressToRecordNewIdea:
          longPressToRecordNewIdea ?? this.longPressToRecordNewIdea,
      chatStyleVerbosity: chatStyleVerbosity ?? this.chatStyleVerbosity,
      chatStyleTone: chatStyleTone ?? this.chatStyleTone,
      chatStyleOutputFormat:
          chatStyleOutputFormat ?? this.chatStyleOutputFormat,
      isLoaded: isLoaded ?? this.isLoaded,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppBehaviorState &&
        other.sendVoiceChatOnFinish == sendVoiceChatOnFinish &&
        other.copyAsMarkdown == copyAsMarkdown &&
        other.generativeSuggestedPrompts == generativeSuggestedPrompts &&
        other.autoLockIdeabooks == autoLockIdeabooks &&
        other.longPressToRecordNewIdea == longPressToRecordNewIdea &&
        other.chatStyleVerbosity == chatStyleVerbosity &&
        other.chatStyleTone == chatStyleTone &&
        other.chatStyleOutputFormat == chatStyleOutputFormat &&
        other.isLoaded == isLoaded;
  }

  @override
  int get hashCode =>
      sendVoiceChatOnFinish.hashCode ^
      copyAsMarkdown.hashCode ^
      generativeSuggestedPrompts.hashCode ^
      autoLockIdeabooks.hashCode ^
      longPressToRecordNewIdea.hashCode ^
      chatStyleVerbosity.hashCode ^
      chatStyleTone.hashCode ^
      chatStyleOutputFormat.hashCode ^
      isLoaded.hashCode;

  @override
  String toString() {
    return 'AppBehaviorState(sendVoiceChatOnFinish: $sendVoiceChatOnFinish, copyAsMarkdown: $copyAsMarkdown, generativeSuggestedPrompts: $generativeSuggestedPrompts, autoLockIdeabooks: $autoLockIdeabooks, longPressToRecordNewIdea: $longPressToRecordNewIdea, chatStyleVerbosity: $chatStyleVerbosity, chatStyleTone: $chatStyleTone, chatStyleOutputFormat: $chatStyleOutputFormat, isLoaded: $isLoaded)';
  }
}

/// Provider for app behavior preferences
final appBehaviorProvider =
    StateNotifierProvider<AppBehaviorNotifier, AppBehaviorState>((ref) {
      final remoteConfigService = ref.watch(remoteConfigServiceProvider);
      return AppBehaviorNotifier(remoteConfigService, ref);
    });

/// Convenience provider for just the send voice chat on finish setting
final sendVoiceChatOnFinishProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.sendVoiceChatOnFinish;
});

/// Convenience provider for just the copy as markdown setting
final copyAsMarkdownProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.copyAsMarkdown;
});

/// Convenience provider for just the generative suggested prompts setting
final generativeSuggestedPromptsProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.generativeSuggestedPrompts;
});

/// Convenience provider for just the auto lock ideabooks setting
final autoLockIdeabooksProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.autoLockIdeabooks;
});

/// Convenience provider for just the long press to record new idea setting
final longPressToRecordNewIdeaProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.longPressToRecordNewIdea;
});

/// Convenience provider for just the chat style verbosity setting
final chatStyleVerbosityProvider = Provider<String>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.chatStyleVerbosity;
});

/// Convenience provider for just the chat style tone setting
final chatStyleToneProvider = Provider<String>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.chatStyleTone;
});

/// Convenience provider for just the chat style output format setting
final chatStyleOutputFormatProvider = Provider<String>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.chatStyleOutputFormat;
});

/// Provider to reset all pro-only preferences for free users
final resetProPreferencesProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    // Reset app behavior preferences
    await ref.read(appBehaviorProvider.notifier).resetToDefaults();

    // Reset color palette preference
    await ColorPaletteStorage.resetSelectedColorPalette();

    // Refresh color palette preference provider
    ref.invalidate(colorPalettePreferenceProvider);

    Logger.debug('All pro-only preferences reset for free user');
  };
});

/// Provider to check if app behavior preferences are loaded
final appBehaviorLoadedProvider = Provider<bool>((ref) {
  final appBehavior = ref.watch(appBehaviorProvider);
  return appBehavior.isLoaded;
});
