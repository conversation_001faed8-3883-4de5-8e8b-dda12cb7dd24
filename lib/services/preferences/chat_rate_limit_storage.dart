import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/models/chat_rate_limit.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving chat message logs for rate limiting
class ChatRateLimitStorage {
  static const String _chatMessageLogKey = 'noeji_chat_message_log';

  /// TTL for message logs (45 days)
  static const Duration messageTtl = Duration(days: 45);

  /// Save the chat message log to persistent storage
  static Future<bool> saveMessageLog(ChatMessageLog log) async {
    try {
      Logger.debug('===== SAVING CHAT MESSAGE LOG =====');
      Logger.debug('Original log: ${log.messageTimes.length} messages');

      // First clean up old messages
      final cleanedLog = log.removeOldMessages(messageTtl);

      Logger.debug('After cleanup: ${cleanedLog.messageTimes.length} messages');

      // Log the timestamps for debugging
      if (cleanedLog.messageTimes.isNotEmpty) {
        Logger.debug('Message timestamps (newest first):');
        final sortedTimes = List<DateTime>.from(cleanedLog.messageTimes)
          ..sort((a, b) => b.compareTo(a));

        for (int i = 0; i < sortedTimes.length; i++) {
          final time = sortedTimes[i];
          final timeAgo = DateTime.now().difference(time);
          Logger.debug(
            '  $i: ${time.toIso8601String()} (${_formatDuration(timeAgo)} ago)',
          );
        }
      }

      final prefs = await SharedPreferences.getInstance();

      // Convert to JSON string
      final jsonString = json.encode(cleanedLog.toJson());

      // Save to SharedPreferences
      final result = await prefs.setString(_chatMessageLogKey, jsonString);

      if (result) {
        Logger.debug(
          'Chat message log saved successfully (${cleanedLog.messageTimes.length} messages)',
        );
      } else {
        Logger.error('Failed to save chat message log');
      }

      Logger.debug('===================================');
      return result;
    } catch (e) {
      Logger.error('Error saving chat message log', e);
      return false;
    }
  }

  /// Format a duration for display in logs
  static String _formatDuration(Duration duration) {
    if (duration.inSeconds < 60) {
      return '${duration.inSeconds}s';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    }
  }

  /// Load the chat message log from persistent storage
  static Future<ChatMessageLog> loadMessageLog() async {
    try {
      Logger.debug('===== LOADING CHAT MESSAGE LOG =====');
      final prefs = await SharedPreferences.getInstance();

      // Get the JSON string from SharedPreferences
      final jsonString = prefs.getString(_chatMessageLogKey);

      if (jsonString == null) {
        Logger.debug('No chat message log found, creating empty log');
        Logger.debug('===================================');
        return ChatMessageLog.empty();
      }

      Logger.debug('Found existing chat message log in SharedPreferences');

      // Parse the JSON string
      final json = jsonDecode(jsonString) as Map<String, dynamic>;

      // Create a ChatMessageLog from the JSON
      final log = ChatMessageLog.fromJson(json);
      Logger.debug('Loaded log: ${log.messageTimes.length} messages');

      // Clean up old messages
      final cleanedLog = log.removeOldMessages(messageTtl);
      Logger.debug('After cleanup: ${cleanedLog.messageTimes.length} messages');

      // Log the timestamps for debugging
      if (cleanedLog.messageTimes.isNotEmpty) {
        Logger.debug('Message timestamps (newest first):');
        final sortedTimes = List<DateTime>.from(cleanedLog.messageTimes)
          ..sort((a, b) => b.compareTo(a));

        for (int i = 0; i < sortedTimes.length; i++) {
          final time = sortedTimes[i];
          final timeAgo = DateTime.now().difference(time);
          Logger.debug(
            '  $i: ${time.toIso8601String()} (${_formatDuration(timeAgo)} ago)',
          );
        }
      }

      // If messages were removed during cleanup, save the cleaned log
      if (cleanedLog.messageTimes.length < log.messageTimes.length) {
        Logger.debug(
          'Cleaned up ${log.messageTimes.length - cleanedLog.messageTimes.length} old messages',
        );
        await saveMessageLog(cleanedLog);
      }

      Logger.debug(
        'Chat message log loaded successfully (${cleanedLog.messageTimes.length} messages)',
      );
      Logger.debug('===================================');
      return cleanedLog;
    } catch (e) {
      Logger.error('Error loading chat message log', e);
      return ChatMessageLog.empty();
    }
  }

  /// Add a new message to the log
  static Future<bool> addMessage(DateTime timestamp) async {
    try {
      Logger.debug('===== ADDING NEW MESSAGE TO LOG =====');
      Logger.debug('New message timestamp: ${timestamp.toIso8601String()}');

      // Load the current log
      final log = await loadMessageLog();
      Logger.debug('Current log has ${log.messageTimes.length} messages');

      // Add the new message
      final updatedLog = log.addMessage(timestamp);
      Logger.debug(
        'Updated log has ${updatedLog.messageTimes.length} messages',
      );

      // Save the updated log
      final result = await saveMessageLog(updatedLog);
      Logger.debug('Save result: $result');
      Logger.debug('====================================');
      return result;
    } catch (e) {
      Logger.error('Error adding message to chat log', e);
      return false;
    }
  }

  /// Clear all message logs
  static Future<bool> clearMessageLog() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove the key from SharedPreferences
      final result = await prefs.remove(_chatMessageLogKey);

      if (result) {
        Logger.debug('Chat message log cleared successfully');
      } else {
        Logger.error('Failed to clear chat message log');
      }

      return result;
    } catch (e) {
      Logger.error('Error clearing chat message log', e);
      return false;
    }
  }
}
