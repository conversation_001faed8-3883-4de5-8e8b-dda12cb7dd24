import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/ui/providers/sort_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving sort order preferences
class SortOrderStorage {
  static const String _sortOrderKey = 'noeji_ideabook_sort_order';

  /// Save the current sort order to persistent storage
  static Future<bool> saveSortOrder(SortOrder order) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = _sortOrderToString(order);
      final result = await prefs.setString(_sortOrderKey, value);

      if (result) {
        Logger.debug('Sort order saved successfully: ${order.name}');
      } else {
        Logger.error('Failed to save sort order');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving sort order', e);
      return false;
    }
  }

  /// Load the saved sort order from persistent storage
  /// Returns SortOrder.ascending if no sort order is saved (default)
  static Future<SortOrder> loadSortOrder() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_sortOrderKey);

      if (value == null) {
        Logger.debug(
          'No sort order found, using default: ${SortOrder.ascending.name}',
        );
        return SortOrder.ascending; // Default to ascending order
      }

      final order = _stringToSortOrder(value);
      Logger.debug('Sort order loaded successfully: ${order.name}');
      return order;
    } catch (e) {
      Logger.error('Error loading sort order', e);
      return SortOrder.ascending; // Default to ascending order on error
    }
  }

  // Helper method to convert SortOrder to String
  static String _sortOrderToString(SortOrder order) {
    return order.name;
  }

  // Helper method to convert String to SortOrder
  static SortOrder _stringToSortOrder(String value) {
    return SortOrder.values.firstWhere(
      (order) => order.name == value,
      orElse: () => SortOrder.ascending, // Default to ascending order
    );
  }
}
