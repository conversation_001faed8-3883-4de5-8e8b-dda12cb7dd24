import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/color_palette_storage.dart';
import 'package:noeji/services/remote_config/remote_config_service.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';

/// State notifier for color palette preferences
class ColorPalettePreferenceNotifier
    extends StateNotifier<ColorPalettePreferenceState> {
  final RemoteConfigService _remoteConfigService;

  /// Constructor
  ColorPalettePreferenceNotifier(this._remoteConfigService)
    : super(const ColorPalettePreferenceState()) {
    // Load saved preferences when initialized
    _loadSavedPreference();
  }

  /// Load the saved preference from storage
  Future<void> _loadSavedPreference() async {
    try {
      // Get the default value from Remote Config
      final defaultPaletteName = _remoteConfigService.getString(
        'default_ideabook_color_palette_name',
      );

      // Load the saved preference or use the default
      final selectedPaletteName =
          await ColorPaletteStorage.loadSelectedColorPalette();

      // Update the state
      state = state.copyWith(
        selectedPaletteName: selectedPaletteName ?? defaultPaletteName,
        isLoaded: true,
      );

      Logger.debug(
        'Color palette preference loaded: ${selectedPaletteName ?? defaultPaletteName}',
      );
    } catch (e) {
      Logger.error('Error loading color palette preference', e);
      // Set loaded to true even on error so UI doesn't wait indefinitely
      state = state.copyWith(isLoaded: true);
    }
  }

  /// Set the selected color palette preference
  Future<void> setSelectedColorPalette(String paletteName) async {
    try {
      // Save to storage
      final success = await ColorPaletteStorage.saveSelectedColorPalette(
        paletteName,
      );

      if (success) {
        // Update the state
        state = state.copyWith(selectedPaletteName: paletteName);
        Logger.debug('Selected color palette preference updated: $paletteName');
      } else {
        Logger.error('Failed to save selected color palette preference');
      }
    } catch (e) {
      Logger.error('Error setting selected color palette preference', e);
    }
  }

  /// Reset preference to default
  /// This is called when user wants to manually reset or when a user becomes a free user
  Future<void> resetToDefault() async {
    try {
      // Reset the preference
      await ColorPaletteStorage.resetSelectedColorPalette();

      // Reload preference
      await _loadSavedPreference();

      Logger.debug('Color palette preference reset to default');
    } catch (e) {
      Logger.error('Error resetting color palette preference', e);
    }
  }
}

/// State class for color palette preferences
class ColorPalettePreferenceState {
  final String selectedPaletteName;
  final bool isLoaded;

  const ColorPalettePreferenceState({
    this.selectedPaletteName = 'default',
    this.isLoaded = false,
  });

  ColorPalettePreferenceState copyWith({
    String? selectedPaletteName,
    bool? isLoaded,
  }) {
    return ColorPalettePreferenceState(
      selectedPaletteName: selectedPaletteName ?? this.selectedPaletteName,
      isLoaded: isLoaded ?? this.isLoaded,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ColorPalettePreferenceState &&
        other.selectedPaletteName == selectedPaletteName &&
        other.isLoaded == isLoaded;
  }

  @override
  int get hashCode => selectedPaletteName.hashCode ^ isLoaded.hashCode;

  @override
  String toString() {
    return 'ColorPalettePreferenceState(selectedPaletteName: $selectedPaletteName, isLoaded: $isLoaded)';
  }
}

/// Provider for color palette preferences
final colorPalettePreferenceProvider = StateNotifierProvider<
  ColorPalettePreferenceNotifier,
  ColorPalettePreferenceState
>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return ColorPalettePreferenceNotifier(remoteConfigService);
});

/// Convenience provider for just the selected palette name
final selectedPaletteNameProvider = Provider<String>((ref) {
  final colorPalettePreference = ref.watch(colorPalettePreferenceProvider);
  return colorPalettePreference.selectedPaletteName;
});
