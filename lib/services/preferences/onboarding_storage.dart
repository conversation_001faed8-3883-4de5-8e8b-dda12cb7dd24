import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving onboarding tour preferences
class OnboardingStorage {
  /// Key for storing whether the ideabooks list tour has been shown
  static const String _ideabooksListTourShownKey =
      'noeji_ideabooks_list_tour_shown';

  /// Key for storing whether the first ideabook tour has been shown
  static const String _firstIdeabookTourShownKey =
      'noeji_first_ideabook_tour_shown';

  /// Key for storing whether the ideas reordering tour has been shown
  static const String _ideasReorderingTourShownKey =
      'noeji_ideas_reordering_tour_shown';

  /// Key for storing whether the save as note tour has been shown
  static const String _saveAsNoteTourShownKey = 'noeji_save_as_note_tour_shown';

  /// Key for storing whether the note detail tour has been shown
  static const String _noteDetailTourShownKey = 'noeji_note_detail_tour_shown';

  /// Key for storing whether the ideabook color system tour has been shown
  static const String _ideabookColorSystemTourShownKey =
      'noeji_ideabook_color_system_tour_shown';

  /// Save that the ideabooks list tour has been shown
  static Future<bool> saveIdeabooksListTourShown() async {
    try {
      Logger.debug('Attempting to save ideabooks list tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before saving
      final currentValue = prefs.getBool(_ideabooksListTourShownKey);
      Logger.debug(
        'Current tour status before saving: $currentValue (key: $_ideabooksListTourShownKey)',
      );

      final result = await prefs.setBool(_ideabooksListTourShownKey, true);

      if (result) {
        Logger.debug('Ideabooks list tour shown status saved successfully');

        // Verify the save worked
        final newValue = prefs.getBool(_ideabooksListTourShownKey);
        Logger.debug('Tour status after saving: $newValue (should be true)');
      } else {
        Logger.error('Failed to save ideabooks list tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving ideabooks list tour shown status', e);
      return false;
    }
  }

  /// Check if the ideabooks list tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowIdeabooksListTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown = prefs.getBool(_ideabooksListTourShownKey) ?? false;

      Logger.debug(
        'Checking tour status: hasBeenShown = $hasBeenShown (key: $_ideabooksListTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show ideabooks list tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error('Error checking if ideabooks list tour should be shown', e);
      return false;
    }
  }

  /// Reset the ideabooks list tour shown status
  /// This is useful for testing
  static Future<bool> resetIdeabooksListTourShown() async {
    try {
      Logger.debug('Attempting to reset ideabooks list tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_ideabooksListTourShownKey);
      Logger.debug(
        'Current tour status before reset: $currentValue (key: $_ideabooksListTourShownKey)',
      );

      final result = await prefs.remove(_ideabooksListTourShownKey);

      if (result) {
        Logger.debug('Ideabooks list tour shown status reset successfully');

        // Verify the reset worked
        final newValue = prefs.getBool(_ideabooksListTourShownKey);
        Logger.debug('Tour status after reset: $newValue (should be null)');
      } else {
        Logger.error('Failed to reset ideabooks list tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting ideabooks list tour shown status', e);
      return false;
    }
  }

  /// Save that the first ideabook tour has been shown
  static Future<bool> saveFirstIdeabookTourShown() async {
    try {
      Logger.debug('Attempting to save first ideabook tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check if the key exists before saving
      final keyExists = prefs.containsKey(_firstIdeabookTourShownKey);
      Logger.debug(
        'First ideabook tour key exists before saving: $keyExists (key: $_firstIdeabookTourShownKey)',
      );

      // Check current value before saving
      final currentValue = prefs.getBool(_firstIdeabookTourShownKey);
      Logger.debug(
        'Current first ideabook tour status before saving: $currentValue (key: $_firstIdeabookTourShownKey)',
      );

      // Try to set the value
      Logger.debug('Setting $_firstIdeabookTourShownKey to true');
      final result = await prefs.setBool(_firstIdeabookTourShownKey, true);

      if (result) {
        Logger.debug('First ideabook tour shown status saved successfully');

        // Verify the save worked
        final newValue = prefs.getBool(_firstIdeabookTourShownKey);
        Logger.debug(
          'First ideabook tour status after saving: $newValue (should be true)',
        );

        // Check if the key exists after saving
        final keyExistsAfter = prefs.containsKey(_firstIdeabookTourShownKey);
        Logger.debug(
          'First ideabook tour key exists after saving: $keyExistsAfter (key: $_firstIdeabookTourShownKey)',
        );
      } else {
        Logger.error('Failed to save first ideabook tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving first ideabook tour shown status', e);
      return false;
    }
  }

  /// Check if the first ideabook tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowFirstIdeabookTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if the key exists
      final keyExists = prefs.containsKey(_firstIdeabookTourShownKey);
      Logger.debug(
        'First ideabook tour key exists: $keyExists (key: $_firstIdeabookTourShownKey)',
      );

      final hasBeenShown = prefs.getBool(_firstIdeabookTourShownKey) ?? false;

      Logger.debug(
        'Checking first ideabook tour status: hasBeenShown = $hasBeenShown (key: $_firstIdeabookTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show first ideabook tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error('Error checking if first ideabook tour should be shown', e);
      return false;
    }
  }

  /// Reset the first ideabook tour shown status
  /// This is useful for testing
  static Future<bool> resetFirstIdeabookTourShown() async {
    try {
      Logger.debug('Attempting to reset first ideabook tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check if the key exists before resetting
      final keyExists = prefs.containsKey(_firstIdeabookTourShownKey);
      Logger.debug(
        'First ideabook tour key exists before reset: $keyExists (key: $_firstIdeabookTourShownKey)',
      );

      // Check current value before resetting
      final currentValue = prefs.getBool(_firstIdeabookTourShownKey);
      Logger.debug(
        'Current first ideabook tour status before reset: $currentValue (key: $_firstIdeabookTourShownKey)',
      );

      final result = await prefs.remove(_firstIdeabookTourShownKey);

      if (result) {
        Logger.debug('First ideabook tour shown status reset successfully');

        // Verify the reset worked
        final newValue = prefs.getBool(_firstIdeabookTourShownKey);
        Logger.debug(
          'First ideabook tour status after reset: $newValue (should be null)',
        );

        // Check if the key exists after resetting
        final keyExistsAfter = prefs.containsKey(_firstIdeabookTourShownKey);
        Logger.debug(
          'First ideabook tour key exists after reset: $keyExistsAfter (key: $_firstIdeabookTourShownKey)',
        );
      } else {
        Logger.error('Failed to reset first ideabook tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting first ideabook tour shown status', e);
      return false;
    }
  }

  /// Save that the ideas reordering tour has been shown
  static Future<bool> saveIdeasReorderingTourShown() async {
    try {
      Logger.debug('Attempting to save ideas reordering tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before saving
      final currentValue = prefs.getBool(_ideasReorderingTourShownKey);
      Logger.debug(
        'Current ideas reordering tour status before saving: $currentValue (key: $_ideasReorderingTourShownKey)',
      );

      final result = await prefs.setBool(_ideasReorderingTourShownKey, true);

      if (result) {
        Logger.debug('Ideas reordering tour shown status saved successfully');

        // Verify the save worked
        final newValue = prefs.getBool(_ideasReorderingTourShownKey);
        Logger.debug(
          'Ideas reordering tour status after saving: $newValue (should be true)',
        );
      } else {
        Logger.error('Failed to save ideas reordering tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving ideas reordering tour shown status', e);
      return false;
    }
  }

  /// Check if the ideas reordering tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowIdeasReorderingTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown = prefs.getBool(_ideasReorderingTourShownKey) ?? false;

      Logger.debug(
        'Checking ideas reordering tour status: hasBeenShown = $hasBeenShown (key: $_ideasReorderingTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show ideas reordering tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error(
        'Error checking if ideas reordering tour should be shown',
        e,
      );
      return false;
    }
  }

  /// Reset the ideas reordering tour shown status
  /// This is useful for testing
  static Future<bool> resetIdeasReorderingTourShown() async {
    try {
      Logger.debug('Attempting to reset ideas reordering tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_ideasReorderingTourShownKey);
      Logger.debug(
        'Current ideas reordering tour status before reset: $currentValue (key: $_ideasReorderingTourShownKey)',
      );

      final result = await prefs.remove(_ideasReorderingTourShownKey);

      if (result) {
        Logger.debug('Ideas reordering tour shown status reset successfully');

        // Verify the reset worked
        final newValue = prefs.getBool(_ideasReorderingTourShownKey);
        Logger.debug(
          'Ideas reordering tour status after reset: $newValue (should be null)',
        );
      } else {
        Logger.error('Failed to reset ideas reordering tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting ideas reordering tour shown status', e);
      return false;
    }
  }

  /// Save that the save as note tour has been shown
  static Future<bool> saveSaveAsNoteTourShown() async {
    try {
      Logger.debug('Attempting to save save as note tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before saving
      final currentValue = prefs.getBool(_saveAsNoteTourShownKey);
      Logger.debug(
        'Current save as note tour status before saving: $currentValue (key: $_saveAsNoteTourShownKey)',
      );

      final result = await prefs.setBool(_saveAsNoteTourShownKey, true);

      if (result) {
        Logger.debug('Save as note tour shown status saved successfully');

        // Verify the save worked
        final newValue = prefs.getBool(_saveAsNoteTourShownKey);
        Logger.debug(
          'Save as note tour status after saving: $newValue (should be true)',
        );
      } else {
        Logger.error('Failed to save save as note tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving save as note tour shown status', e);
      return false;
    }
  }

  /// Check if the save as note tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowSaveAsNoteTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown = prefs.getBool(_saveAsNoteTourShownKey) ?? false;

      Logger.debug(
        'Checking save as note tour status: hasBeenShown = $hasBeenShown (key: $_saveAsNoteTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show save as note tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error('Error checking if save as note tour should be shown', e);
      return false;
    }
  }

  /// Reset the save as note tour shown status
  /// This is useful for testing
  static Future<bool> resetSaveAsNoteTourShown() async {
    try {
      Logger.debug('Attempting to reset save as note tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_saveAsNoteTourShownKey);
      Logger.debug(
        'Current save as note tour status before reset: $currentValue (key: $_saveAsNoteTourShownKey)',
      );

      final result = await prefs.remove(_saveAsNoteTourShownKey);

      if (result) {
        Logger.debug('Save as note tour shown status reset successfully');

        // Verify the reset worked
        final newValue = prefs.getBool(_saveAsNoteTourShownKey);
        Logger.debug(
          'Save as note tour status after reset: $newValue (should be null)',
        );
      } else {
        Logger.error('Failed to reset save as note tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting save as note tour shown status', e);
      return false;
    }
  }

  /// Save that the note detail tour has been shown
  static Future<bool> saveNoteDetailTourShown() async {
    try {
      Logger.debug('Attempting to save note detail tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before saving
      final currentValue = prefs.getBool(_noteDetailTourShownKey);
      Logger.debug(
        'Current note detail tour status before saving: $currentValue (key: $_noteDetailTourShownKey)',
      );

      final result = await prefs.setBool(_noteDetailTourShownKey, true);

      if (result) {
        Logger.debug('Note detail tour shown status saved successfully');

        // Verify the save worked
        final newValue = prefs.getBool(_noteDetailTourShownKey);
        Logger.debug(
          'Note detail tour status after saving: $newValue (should be true)',
        );
      } else {
        Logger.error('Failed to save note detail tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving note detail tour shown status', e);
      return false;
    }
  }

  /// Check if the note detail tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowNoteDetailTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown = prefs.getBool(_noteDetailTourShownKey) ?? false;

      Logger.debug(
        'Checking note detail tour status: hasBeenShown = $hasBeenShown (key: $_noteDetailTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show note detail tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error('Error checking if note detail tour should be shown', e);
      return false;
    }
  }

  /// Reset the note detail tour shown status
  /// This is useful for testing
  static Future<bool> resetNoteDetailTourShown() async {
    try {
      Logger.debug('Attempting to reset note detail tour shown status');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_noteDetailTourShownKey);
      Logger.debug(
        'Current note detail tour status before reset: $currentValue (key: $_noteDetailTourShownKey)',
      );

      final result = await prefs.remove(_noteDetailTourShownKey);

      if (result) {
        Logger.debug('Note detail tour shown status reset successfully');

        // Verify the reset worked
        final newValue = prefs.getBool(_noteDetailTourShownKey);
        Logger.debug(
          'Note detail tour status after reset: $newValue (should be null)',
        );
      } else {
        Logger.error('Failed to reset note detail tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting note detail tour shown status', e);
      return false;
    }
  }

  /// Save that the ideabook color system tour has been shown
  static Future<bool> saveIdeabookColorSystemTourShown() async {
    try {
      Logger.debug(
        'Attempting to save ideabook color system tour shown status',
      );
      final prefs = await SharedPreferences.getInstance();

      // Check current value before saving
      final currentValue = prefs.getBool(_ideabookColorSystemTourShownKey);
      Logger.debug(
        'Current ideabook color system tour status before saving: $currentValue (key: $_ideabookColorSystemTourShownKey)',
      );

      final result = await prefs.setBool(
        _ideabookColorSystemTourShownKey,
        true,
      );

      if (result) {
        Logger.debug(
          'Ideabook color system tour shown status saved successfully',
        );

        // Verify the save worked
        final newValue = prefs.getBool(_ideabookColorSystemTourShownKey);
        Logger.debug(
          'Ideabook color system tour status after saving: $newValue (should be true)',
        );
      } else {
        Logger.error('Failed to save ideabook color system tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving ideabook color system tour shown status', e);
      return false;
    }
  }

  /// Check if the ideabook color system tour should be shown
  /// Returns true if the tour should be shown (has not been shown before)
  /// Returns false if the tour should not be shown (has been shown before)
  static Future<bool> shouldShowIdeabookColorSystemTour() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasBeenShown =
          prefs.getBool(_ideabookColorSystemTourShownKey) ?? false;

      Logger.debug(
        'Checking ideabook color system tour status: hasBeenShown = $hasBeenShown (key: $_ideabookColorSystemTourShownKey)',
      );

      // If the tour has been shown, we should not show it again
      final shouldShow = !hasBeenShown;

      Logger.debug('Should show ideabook color system tour: $shouldShow');
      return shouldShow;
    } catch (e) {
      Logger.error(
        'Error checking if ideabook color system tour should be shown',
        e,
      );
      return false;
    }
  }

  /// Reset the ideabook color system tour shown status
  /// This is useful for testing
  static Future<bool> resetIdeabookColorSystemTourShown() async {
    try {
      Logger.debug(
        'Attempting to reset ideabook color system tour shown status',
      );
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_ideabookColorSystemTourShownKey);
      Logger.debug(
        'Current ideabook color system tour status before reset: $currentValue (key: $_ideabookColorSystemTourShownKey)',
      );

      final result = await prefs.remove(_ideabookColorSystemTourShownKey);

      if (result) {
        Logger.debug(
          'Ideabook color system tour shown status reset successfully',
        );

        // Verify the reset worked
        final newValue = prefs.getBool(_ideabookColorSystemTourShownKey);
        Logger.debug(
          'Ideabook color system tour status after reset: $newValue (should be null)',
        );
      } else {
        Logger.error('Failed to reset ideabook color system tour shown status');
      }

      return result;
    } catch (e) {
      Logger.error(
        'Error resetting ideabook color system tour shown status',
        e,
      );
      return false;
    }
  }

  /// Reset all tour shown statuses
  /// This is useful for testing
  static Future<bool> resetAllTours() async {
    try {
      Logger.debug('Attempting to reset all tour shown statuses');

      // Reset ideabooks list tour
      final ideabooksListResult = await resetIdeabooksListTourShown();
      Logger.debug('Ideabooks list tour reset result: $ideabooksListResult');

      // Reset first ideabook tour
      final firstIdeabookResult = await resetFirstIdeabookTourShown();
      Logger.debug('First ideabook tour reset result: $firstIdeabookResult');

      // Reset ideas reordering tour
      final ideasReorderingResult = await resetIdeasReorderingTourShown();
      Logger.debug(
        'Ideas reordering tour reset result: $ideasReorderingResult',
      );

      // Reset save as note tour
      final saveAsNoteResult = await resetSaveAsNoteTourShown();
      Logger.debug('Save as note tour reset result: $saveAsNoteResult');

      // Reset note detail tour
      final noteDetailResult = await resetNoteDetailTourShown();
      Logger.debug('Note detail tour reset result: $noteDetailResult');

      // Reset ideabook color system tour
      final ideabookColorSystemResult =
          await resetIdeabookColorSystemTourShown();
      Logger.debug(
        'Ideabook color system tour reset result: $ideabookColorSystemResult',
      );

      return ideabooksListResult &&
          firstIdeabookResult &&
          ideasReorderingResult &&
          saveAsNoteResult &&
          noteDetailResult &&
          ideabookColorSystemResult;
    } catch (e) {
      Logger.error('Error resetting all tour shown statuses', e);
      return false;
    }
  }
}
