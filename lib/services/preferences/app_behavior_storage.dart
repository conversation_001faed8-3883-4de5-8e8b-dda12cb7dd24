import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving app behavior preferences
class AppBehaviorStorage {
  static const String _sendVoiceChatOnFinishKey =
      'noeji_send_voice_chat_on_finish';
  static const String _copyAsMarkdownKey = 'noeji_copy_as_markdown';
  static const String _generativeSuggestedPromptsKey =
      'noeji_generative_suggested_prompts';
  static const String _autoLockIdeabooksKey = 'noeji_auto_lock_ideabooks';
  static const String _longPressToRecordNewIdeaKey =
      'noeji_long_press_to_record_new_idea';
  static const String _chatStyleVerbosityKey = 'noeji_chat_style_verbosity';
  static const String _chatStyleToneKey = 'noeji_chat_style_tone';
  static const String _chatStyleOutputFormatKey =
      'noeji_chat_style_output_format';

  /// Save the send voice chat on finish preference to persistent storage
  static Future<bool> saveSendVoiceChatOnFinish(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_sendVoiceChatOnFinishKey, enabled);

      if (result) {
        Logger.debug(
          'Send voice chat on finish preference saved successfully: $enabled',
        );
      } else {
        Logger.error('Failed to save send voice chat on finish preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving send voice chat on finish preference', e);
      return false;
    }
  }

  /// Save the copy as markdown preference to persistent storage
  static Future<bool> saveCopyAsMarkdown(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_copyAsMarkdownKey, enabled);

      if (result) {
        Logger.debug(
          'Copy as markdown preference saved successfully: $enabled',
        );
      } else {
        Logger.error('Failed to save copy as markdown preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving copy as markdown preference', e);
      return false;
    }
  }

  /// Save the generative suggested prompts preference to persistent storage
  static Future<bool> saveGenerativeSuggestedPrompts(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(
        _generativeSuggestedPromptsKey,
        enabled,
      );

      if (result) {
        Logger.debug(
          'Generative suggested prompts preference saved successfully: $enabled',
        );
      } else {
        Logger.error('Failed to save generative suggested prompts preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving generative suggested prompts preference', e);
      return false;
    }
  }

  /// Load the send voice chat on finish preference from persistent storage
  /// Returns the default value from Remote Config if no preference is saved
  static Future<bool> loadSendVoiceChatOnFinish({
    bool defaultValue = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_sendVoiceChatOnFinishKey);

      if (value == null) {
        Logger.debug(
          'No send voice chat on finish preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Send voice chat on finish preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading send voice chat on finish preference', e);
      return defaultValue;
    }
  }

  /// Load the copy as markdown preference from persistent storage
  /// Returns the default value from Remote Config if no preference is saved
  static Future<bool> loadCopyAsMarkdown({bool defaultValue = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_copyAsMarkdownKey);

      if (value == null) {
        Logger.debug(
          'No copy as markdown preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug('Copy as markdown preference loaded successfully: $value');
      return value;
    } catch (e) {
      Logger.error('Error loading copy as markdown preference', e);
      return defaultValue;
    }
  }

  /// Load the generative suggested prompts preference from persistent storage
  /// Returns the default value from Remote Config if no preference is saved
  static Future<bool> loadGenerativeSuggestedPrompts({
    bool defaultValue = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_generativeSuggestedPromptsKey);

      if (value == null) {
        Logger.debug(
          'No generative suggested prompts preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Generative suggested prompts preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading generative suggested prompts preference', e);
      return defaultValue;
    }
  }

  /// Check if the send voice chat on finish preference has been set
  static Future<bool> hasSendVoiceChatOnFinishPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_sendVoiceChatOnFinishKey);
    } catch (e) {
      Logger.error(
        'Error checking if send voice chat on finish preference exists',
        e,
      );
      return false;
    }
  }

  /// Reset the send voice chat on finish preference
  /// This is useful for testing or when user wants to reset to default
  static Future<bool> resetSendVoiceChatOnFinish() async {
    try {
      Logger.debug('Attempting to reset send voice chat on finish preference');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_sendVoiceChatOnFinishKey);
      Logger.debug(
        'Current send voice chat on finish preference before reset: $currentValue',
      );

      final result = await prefs.remove(_sendVoiceChatOnFinishKey);

      if (result) {
        Logger.debug('Send voice chat on finish preference reset successfully');
      } else {
        Logger.error('Failed to reset send voice chat on finish preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting send voice chat on finish preference', e);
      return false;
    }
  }

  /// Reset the copy as markdown preference
  /// This is useful for testing or when user wants to reset to default
  static Future<bool> resetCopyAsMarkdown() async {
    try {
      Logger.debug('Attempting to reset copy as markdown preference');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_copyAsMarkdownKey);
      Logger.debug(
        'Current copy as markdown preference before reset: $currentValue',
      );

      final result = await prefs.remove(_copyAsMarkdownKey);

      if (result) {
        Logger.debug('Copy as markdown preference reset successfully');
      } else {
        Logger.error('Failed to reset copy as markdown preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting copy as markdown preference', e);
      return false;
    }
  }

  /// Reset the generative suggested prompts preference
  /// This is useful for testing or when user wants to reset to default
  static Future<bool> resetGenerativeSuggestedPrompts() async {
    try {
      Logger.debug(
        'Attempting to reset generative suggested prompts preference',
      );
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_generativeSuggestedPromptsKey);
      Logger.debug(
        'Current generative suggested prompts preference before reset: $currentValue',
      );

      final result = await prefs.remove(_generativeSuggestedPromptsKey);

      if (result) {
        Logger.debug(
          'Generative suggested prompts preference reset successfully',
        );
      } else {
        Logger.error('Failed to reset generative suggested prompts preference');
      }

      return result;
    } catch (e) {
      Logger.error(
        'Error resetting generative suggested prompts preference',
        e,
      );
      return false;
    }
  }

  /// Save the auto lock ideabooks preference to persistent storage
  static Future<bool> saveAutoLockIdeabooks(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_autoLockIdeabooksKey, enabled);

      if (result) {
        Logger.debug(
          'Auto lock ideabooks preference saved successfully: $enabled',
        );
      } else {
        Logger.error('Failed to save auto lock ideabooks preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving auto lock ideabooks preference', e);
      return false;
    }
  }

  /// Load the auto lock ideabooks preference from persistent storage
  /// Returns the default value if no preference is saved
  static Future<bool> loadAutoLockIdeabooks({bool defaultValue = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_autoLockIdeabooksKey);

      if (value == null) {
        Logger.debug(
          'No auto lock ideabooks preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Auto lock ideabooks preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading auto lock ideabooks preference', e);
      return defaultValue;
    }
  }

  /// Reset the auto lock ideabooks preference
  /// This is useful for testing or when user wants to reset to default
  static Future<bool> resetAutoLockIdeabooks() async {
    try {
      Logger.debug('Attempting to reset auto lock ideabooks preference');
      final prefs = await SharedPreferences.getInstance();

      // Check current value before resetting
      final currentValue = prefs.getBool(_autoLockIdeabooksKey);
      Logger.debug(
        'Current auto lock ideabooks preference before reset: $currentValue',
      );

      final result = await prefs.remove(_autoLockIdeabooksKey);

      if (result) {
        Logger.debug('Auto lock ideabooks preference reset successfully');
      } else {
        Logger.error('Failed to reset auto lock ideabooks preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting auto lock ideabooks preference', e);
      return false;
    }
  }

  /// Save the long press to record new idea preference to persistent storage
  static Future<bool> saveLongPressToRecordNewIdea(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_longPressToRecordNewIdeaKey, enabled);

      if (result) {
        Logger.debug(
          'Long press to record new idea preference saved successfully: $enabled',
        );
      } else {
        Logger.error('Failed to save long press to record new idea preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving long press to record new idea preference', e);
      return false;
    }
  }

  /// Load the long press to record new idea preference from persistent storage
  static Future<bool> loadLongPressToRecordNewIdea({
    bool defaultValue = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result =
          prefs.getBool(_longPressToRecordNewIdeaKey) ?? defaultValue;

      Logger.debug('Long press to record new idea preference loaded: $result');
      return result;
    } catch (e) {
      Logger.error('Error loading long press to record new idea preference', e);
      return defaultValue;
    }
  }

  /// Reset the long press to record new idea preference to default value
  static Future<bool> resetLongPressToRecordNewIdea() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_longPressToRecordNewIdeaKey);

      if (result) {
        Logger.debug(
          'Long press to record new idea preference reset successfully',
        );
      } else {
        Logger.error(
          'Failed to reset long press to record new idea preference',
        );
      }

      return result;
    } catch (e) {
      Logger.error(
        'Error resetting long press to record new idea preference',
        e,
      );
      return false;
    }
  }

  /// Save the chat style verbosity preference to persistent storage
  static Future<bool> saveChatStyleVerbosity(String verbosity) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(_chatStyleVerbosityKey, verbosity);

      if (result) {
        Logger.debug(
          'Chat style verbosity preference saved successfully: $verbosity',
        );
      } else {
        Logger.error('Failed to save chat style verbosity preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving chat style verbosity preference', e);
      return false;
    }
  }

  /// Save the chat style tone preference to persistent storage
  static Future<bool> saveChatStyleTone(String tone) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(_chatStyleToneKey, tone);

      if (result) {
        Logger.debug('Chat style tone preference saved successfully: $tone');
      } else {
        Logger.error('Failed to save chat style tone preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving chat style tone preference', e);
      return false;
    }
  }

  /// Save the chat style output format preference to persistent storage
  static Future<bool> saveChatStyleOutputFormat(String outputFormat) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(
        _chatStyleOutputFormatKey,
        outputFormat,
      );

      if (result) {
        Logger.debug(
          'Chat style output format preference saved successfully: $outputFormat',
        );
      } else {
        Logger.error('Failed to save chat style output format preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving chat style output format preference', e);
      return false;
    }
  }

  /// Load the chat style verbosity preference from persistent storage
  /// Returns the default value if no preference is saved
  static Future<String> loadChatStyleVerbosity({
    String defaultValue = 'balanced',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_chatStyleVerbosityKey);

      if (value == null) {
        Logger.debug(
          'No chat style verbosity preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Chat style verbosity preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading chat style verbosity preference', e);
      return defaultValue;
    }
  }

  /// Load the chat style tone preference from persistent storage
  /// Returns the default value if no preference is saved
  static Future<String> loadChatStyleTone({
    String defaultValue = 'casual',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_chatStyleToneKey);

      if (value == null) {
        Logger.debug(
          'No chat style tone preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug('Chat style tone preference loaded successfully: $value');
      return value;
    } catch (e) {
      Logger.error('Error loading chat style tone preference', e);
      return defaultValue;
    }
  }

  /// Load the chat style output format preference from persistent storage
  /// Returns the default value if no preference is saved
  static Future<String> loadChatStyleOutputFormat({
    String defaultValue = 'free_form',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_chatStyleOutputFormatKey);

      if (value == null) {
        Logger.debug(
          'No chat style output format preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Chat style output format preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading chat style output format preference', e);
      return defaultValue;
    }
  }

  /// Reset the chat style verbosity preference to default
  static Future<bool> resetChatStyleVerbosity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_chatStyleVerbosityKey);

      if (result) {
        Logger.debug('Chat style verbosity preference reset successfully');
      } else {
        Logger.error('Failed to reset chat style verbosity preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting chat style verbosity preference', e);
      return false;
    }
  }

  /// Reset the chat style tone preference to default
  static Future<bool> resetChatStyleTone() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_chatStyleToneKey);

      if (result) {
        Logger.debug('Chat style tone preference reset successfully');
      } else {
        Logger.error('Failed to reset chat style tone preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting chat style tone preference', e);
      return false;
    }
  }

  /// Reset the chat style output format preference to default
  static Future<bool> resetChatStyleOutputFormat() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_chatStyleOutputFormatKey);

      if (result) {
        Logger.debug('Chat style output format preference reset successfully');
      } else {
        Logger.error('Failed to reset chat style output format preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting chat style output format preference', e);
      return false;
    }
  }

  /// Reset all app behavior preferences to defaults
  /// This is useful when a user downgrades from pro to free
  static Future<bool> resetAllPreferences() async {
    try {
      Logger.debug('Attempting to reset all app behavior preferences');

      final results = await Future.wait([
        resetSendVoiceChatOnFinish(),
        resetCopyAsMarkdown(),
        resetGenerativeSuggestedPrompts(),
        resetAutoLockIdeabooks(),
        resetLongPressToRecordNewIdea(),
        resetChatStyleVerbosity(),
        resetChatStyleTone(),
        resetChatStyleOutputFormat(),
      ]);

      final allSuccessful = results.every((result) => result);

      if (allSuccessful) {
        Logger.debug('All app behavior preferences reset successfully');
      } else {
        Logger.error('Some app behavior preferences failed to reset');
      }

      return allSuccessful;
    } catch (e) {
      Logger.error('Error resetting all app behavior preferences', e);
      return false;
    }
  }
}
