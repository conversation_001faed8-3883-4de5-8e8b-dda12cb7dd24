import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving color palette preferences
class ColorPaletteStorage {
  static const String _selectedColorPaletteKey = 'noeji_selected_color_palette';

  /// Save the selected color palette name to persistent storage
  static Future<bool> saveSelectedColorPalette(String paletteName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setString(
        _selectedColorPaletteKey,
        paletteName,
      );

      if (result) {
        Logger.debug('Selected color palette saved successfully: $paletteName');
      } else {
        Logger.error('Failed to save selected color palette');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving selected color palette', e);
      return false;
    }
  }

  /// Load the selected color palette name from persistent storage
  /// Returns the default value from Remote Config if no preference is saved
  static Future<String?> loadSelectedColorPalette() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getString(_selectedColorPaletteKey);

      if (value == null) {
        Logger.debug(
          'No selected color palette preference found, will use remote config default',
        );
        return null;
      }

      Logger.debug(
        'Selected color palette preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading selected color palette preference', e);
      return null;
    }
  }

  /// Reset the selected color palette preference
  static Future<bool> resetSelectedColorPalette() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_selectedColorPaletteKey);

      if (result) {
        Logger.debug('Selected color palette preference reset successfully');
      } else {
        Logger.error('Failed to reset selected color palette preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting selected color palette preference', e);
      return false;
    }
  }
}
