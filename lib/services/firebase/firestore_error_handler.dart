import 'package:noeji/utils/error_utils.dart';
import 'package:noeji/utils/logger.dart';

/// A utility class for handling Firestore errors consistently across the app
class FirestoreErrorHandler {
  /// Handle a Firestore error
  ///
  /// This method checks if the error is a permission error and handles it appropriately:
  /// - For permission errors, it sets the global permission error state
  /// - For other errors, it logs the error and rethrows it
  ///
  /// Returns true if the error was handled (permission error), false otherwise
  static bool handleError(dynamic ref, Object error, {String? operation}) {
    try {
      // Check if this is a permission error
      final isPermissionError = ErrorUtils.isPermissionError(error);

      if (isPermissionError) {
        // Log the error with operation context if provided
        final logMessage =
            operation != null
                ? 'Permission error during $operation'
                : 'Permission error in Firestore operation';

        Logger.error(logMessage, error);

        // Handle the permission error globally
        if (ref != null) {
          ErrorUtils.handleGlobalPermissionError(ref, error);
        } else {
          Logger.error('Cannot handle global permission error: ref is null');
        }

        return true;
      } else {
        // For other errors, just log them
        final logMessage =
            operation != null
                ? 'Error during $operation'
                : 'Error in Firestore operation';

        Logger.error(logMessage, error);

        return false;
      }
    } catch (e) {
      // If there's an error in the error handler itself, log it but don't crash
      Logger.error('Error in FirestoreErrorHandler', e);
      return false;
    }
  }
}
