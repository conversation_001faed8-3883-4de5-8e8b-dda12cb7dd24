import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/firebase/firebase_providers.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/utils/logger.dart';

/// Global registry for all Firestore subscriptions
/// This ensures that ALL Firestore listeners can be canceled during sign out
class GlobalFirestoreSubscriptionRegistry {
  static final GlobalFirestoreSubscriptionRegistry _instance =
      GlobalFirestoreSubscriptionRegistry._internal();
  factory GlobalFirestoreSubscriptionRegistry() => _instance;
  GlobalFirestoreSubscriptionRegistry._internal();

  final Set<StreamSubscription> _allSubscriptions = {};

  /// Register a subscription for global tracking
  void register(StreamSubscription subscription) {
    _allSubscriptions.add(subscription);
    Logger.debug(
      'Registered Firestore subscription (total: ${_allSubscriptions.length})',
    );
  }

  /// Unregister a subscription
  void unregister(StreamSubscription subscription) {
    _allSubscriptions.remove(subscription);
    Logger.debug(
      'Unregistered Firestore subscription (remaining: ${_allSubscriptions.length})',
    );
  }

  /// Cancel all registered subscriptions
  void cancelAll() {
    final count = _allSubscriptions.length;
    Logger.debug('Canceling all $count registered Firestore subscriptions');

    for (final subscription in _allSubscriptions) {
      try {
        subscription.cancel();
      } catch (e) {
        Logger.error('Error canceling Firestore subscription', e);
      }
    }

    _allSubscriptions.clear();
    Logger.debug('All Firestore subscriptions canceled and cleared');
  }

  /// Get the number of active subscriptions
  int get activeCount => _allSubscriptions.length;
}

/// Provider for the Firestore listener pool service
final firestoreListenerPoolProvider = Provider<FirestoreListenerPool>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return FirestoreListenerPool(firestoreService);
});

/// Provider to ensure the auth listener clear provider is initialized early
/// This provider should be watched in the app's root widget
final ensureAuthListenerClearProvider = Provider<bool>((ref) {
  // Watch the auth listener clear provider to ensure it's initialized
  ref.watch(authListenerClearProvider);

  // Return true to indicate the provider has been initialized
  return true;
});

/// Provider that listens to authentication state changes and clears the listener pool
/// when the user changes to avoid permission issues with listeners that were created
/// for the previous user
final authListenerClearProvider = Provider<void>((ref) {
  Logger.debug(
    'Initializing authListenerClearProvider to monitor user account changes',
  );

  // Get the listener pool
  final listenerPool = ref.watch(firestoreListenerPoolProvider);

  // Keep track of the previous user ID
  String? previousUserId;

  // Get the current user ID immediately for initialization
  final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
  if (currentUser != null) {
    previousUserId = currentUser.uid;
    Logger.debug(
      'authListenerClearProvider initialized with user ID: $previousUserId',
    );
  } else {
    Logger.debug(
      'authListenerClearProvider initialized with no user signed in',
    );
  }

  // Watch the Firebase user provider to detect auth state changes
  ref.listen<AsyncValue<firebase_auth.User?>>(firebaseUserProvider, (
    previous,
    current,
  ) {
    Logger.debug('Auth state change detected in authListenerClearProvider');

    // Skip if we're still loading or there was an error
    if (current.isLoading || current.hasError) {
      Logger.debug(
        'Auth state is loading or has error, skipping listener cleanup',
      );
      return;
    }

    // Get the current user ID
    final currentUserId = current.value?.uid;
    Logger.debug(
      'Current user ID: ${currentUserId ?? 'null'}, Previous tracked ID: ${previousUserId ?? 'null'}',
    );

    // Handle the previous state
    if (previous != null && !previous.isLoading && !previous.hasError) {
      final previousUserValue = previous.value?.uid;
      Logger.debug(
        'Previous AsyncValue user ID: ${previousUserValue ?? 'null'}',
      );

      // If the user ID has changed (sign in, sign out, or switch accounts)
      if (previousUserValue != currentUserId) {
        Logger.debug(
          'User account changed from ${previousUserValue ?? 'null'} to ${currentUserId ?? 'null'}, clearing all Firestore listeners',
        );

        // Clear all listeners in the pool
        listenerPool.clearAllListeners();

        // Also cancel all globally registered subscriptions
        final globalRegistry = GlobalFirestoreSubscriptionRegistry();
        globalRegistry.cancelAll();
      } else {
        Logger.debug('User ID unchanged, not clearing listeners');
      }
    } else if (previousUserId != null && previousUserId != currentUserId) {
      // Handle case where previous AsyncValue wasn't available but we tracked the ID
      Logger.debug(
        'User account changed from $previousUserId to ${currentUserId ?? 'null'}, clearing all Firestore listeners',
      );

      // Clear all listeners in the pool
      listenerPool.clearAllListeners();

      // Also cancel all globally registered subscriptions
      final globalRegistry = GlobalFirestoreSubscriptionRegistry();
      globalRegistry.cancelAll();
    } else {
      Logger.debug(
        'No user ID change detected or no previous user ID available',
      );
    }

    // Update the previous user ID
    previousUserId = currentUserId;
    Logger.debug('Updated tracked user ID to: ${previousUserId ?? 'null'}');
  });
});

/// Class to manage a pool of Firestore listeners with TTL
class FirestoreListenerPool {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Default TTL for listeners in milliseconds (30 minutes)
  static const int _defaultTtlMs = 30 * 60 * 1000;

  /// Map of collection paths to listener entries
  final Map<String, _ListenerEntry> _listeners = {};

  /// Constructor
  FirestoreListenerPool(this._firestoreService);

  /// Get or create a stream for ideabooks
  Stream<List<Ideabook>> getIdeabooksStream() {
    const path = 'ideabooks';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<List<Ideabook>>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToIdeabooks();

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for ideas in an ideabook
  Stream<List<Idea>> getIdeasStream(String ideabookId) {
    final path = 'ideabooks/$ideabookId/ideas';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<List<Idea>>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToIdeas(ideabookId);

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for notes in an ideabook
  Stream<List<Note>> getNotesStream(String ideabookId) {
    final path = 'ideabooks/$ideabookId/notes';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<List<Note>>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToNotes(ideabookId);

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for a specific ideabook by ID
  Stream<Ideabook?> getIdeabookStream(String ideabookId) {
    final path = 'ideabooks/$ideabookId';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<Ideabook?>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToIdeabookById(ideabookId);

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for a specific idea by ID
  Stream<Idea?> getIdeaStream(String ideabookId, String ideaId) {
    final path = 'ideabooks/$ideabookId/ideas/$ideaId';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<Idea?>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToIdeaById(ideabookId, ideaId);

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for a specific note by ID
  Stream<Note?> getNoteStream(String ideabookId, String noteId) {
    final path = 'ideabooks/$ideabookId/notes/$noteId';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<Note?>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToNoteById(ideabookId, noteId);

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Get or create a stream for the user's passcode hash
  Stream<String?> getPasscodeHashStream() {
    const path = 'users/passcode';

    // Check if we already have a listener for this path
    if (_listeners.containsKey(path)) {
      // Reset the TTL timer
      _resetTtl(path);

      // Return the existing stream
      return _listeners[path]!.stream as Stream<String?>;
    }

    // Create a new listener
    final stream = _firestoreService.listenToPasscodeHash();

    // Store the listener with a TTL timer
    _addListener(path, stream, _defaultTtlMs);

    return stream;
  }

  /// Register a subscription with the listener pool for tracking
  /// This allows the pool to cancel the subscription when clearing listeners
  void registerSubscription(String path, StreamSubscription subscription) {
    final entry = _listeners[path];
    if (entry != null) {
      entry.addSubscription(subscription);
      Logger.debug(
        'Registered subscription for path: $path (total: ${entry.activeSubscriptions.length})',
      );
    }
  }

  /// Unregister a subscription from the listener pool
  /// This should be called when a provider stops listening
  void unregisterSubscription(String path, StreamSubscription subscription) {
    final entry = _listeners[path];
    if (entry != null) {
      entry.removeSubscription(subscription);
      Logger.debug(
        'Unregistered subscription for path: $path (remaining: ${entry.activeSubscriptions.length})',
      );
    }
  }

  /// Add a listener to the pool with a TTL timer
  void _addListener(String path, Stream stream, int ttlMs) {
    Logger.debug(
      'Adding Firestore listener for path: $path with TTL: ${ttlMs}ms',
    );

    // Create a TTL timer
    final timer = Timer(Duration(milliseconds: ttlMs), () {
      _removeListener(path);
    });

    // Store the listener entry
    _listeners[path] = _ListenerEntry(
      stream: stream,
      timer: timer,
      ttlMs: ttlMs,
      path: path, // Store the path for debugging
    );
  }

  /// Reset the TTL timer for a listener
  void _resetTtl(String path) {
    final entry = _listeners[path];
    if (entry != null) {
      Logger.debug('Resetting TTL for Firestore listener: $path');

      // Cancel the existing timer
      entry.timer.cancel();

      // Create a new timer
      final newTimer = Timer(Duration(milliseconds: entry.ttlMs), () {
        _removeListener(path);
      });

      // Update the entry with the new timer
      _listeners[path] = _ListenerEntry(
        stream: entry.stream,
        timer: newTimer,
        ttlMs: entry.ttlMs,
        path: path, // Preserve the path
      );
    }
  }

  /// Remove a listener from the pool
  void _removeListener(String path) {
    Logger.debug(
      'Removing Firestore listener for path: $path due to TTL expiration',
    );

    // Remove the listener from the pool
    _listeners.remove(path);
  }

  /// Dispose all listeners
  void dispose() {
    Logger.debug('Disposing all Firestore listeners');

    // Cancel all timers and underlying subscriptions
    for (final entry in _listeners.values) {
      try {
        // Cancel the timer
        entry.timer.cancel();

        // Cancel all active subscriptions to this stream
        Logger.debug(
          'Disposing ${entry.activeSubscriptions.length} active subscriptions for listener: ${entry.path}',
        );
        entry.cancelAllSubscriptions();
      } catch (e) {
        Logger.error('Error disposing listener ${entry.path}', e);
      }
    }

    // Clear the listeners map
    _listeners.clear();
  }

  /// Clear all listeners in the pool
  /// This should be called when the user account changes to avoid permission issues
  /// with listeners that were created for the previous user
  void clearAllListeners() {
    final listenerCount = _listeners.length;
    Logger.debug(
      'Clearing all Firestore listeners ($listenerCount active) due to user account change',
    );

    if (listenerCount > 0) {
      // Log the paths of all active listeners for debugging
      final paths = _listeners.keys.join(', ');
      Logger.debug('Active listener paths being cleared: $paths');

      try {
        // Cancel all timers and underlying subscriptions
        for (final entry in _listeners.values) {
          try {
            // Cancel the timer
            entry.timer.cancel();

            // Cancel all active subscriptions to this stream
            Logger.debug(
              'Canceling ${entry.activeSubscriptions.length} active subscriptions for listener: ${entry.path}',
            );
            entry.cancelAllSubscriptions();

            // For debugging, log each listener being cleared
            Logger.debug(
              'Cleared timer and subscriptions for listener: ${entry.path}',
            );
          } catch (e) {
            Logger.error(
              'Error canceling timer/subscriptions for listener ${entry.path}',
              e,
            );
          }
        }

        // Clear the listeners map
        _listeners.clear();

        Logger.debug(
          'Successfully cleared all Firestore listeners and their underlying subscriptions',
        );
      } catch (e) {
        Logger.error('Error clearing Firestore listeners', e);

        // Force clear the map even if there was an error
        _listeners.clear();
        Logger.debug('Forcibly cleared listeners map after error');
      }
    } else {
      Logger.debug('No active Firestore listeners to clear');
    }
  }
}

/// Class to represent a listener entry in the pool
class _ListenerEntry {
  /// The stream for this listener
  final Stream stream;

  /// The TTL timer for this listener
  final Timer timer;

  /// The TTL in milliseconds
  final int ttlMs;

  /// The path for this listener (for debugging)
  final String? path;

  /// List of active subscriptions to this stream
  /// This tracks all the providers that are listening to this stream
  final List<StreamSubscription> activeSubscriptions = [];

  /// Constructor
  _ListenerEntry({
    required this.stream,
    required this.timer,
    required this.ttlMs,
    this.path,
  });

  /// Add a subscription to track
  void addSubscription(StreamSubscription subscription) {
    activeSubscriptions.add(subscription);
  }

  /// Remove a subscription from tracking
  void removeSubscription(StreamSubscription subscription) {
    activeSubscriptions.remove(subscription);
  }

  /// Cancel all active subscriptions
  void cancelAllSubscriptions() {
    for (final subscription in activeSubscriptions) {
      try {
        subscription.cancel();
      } catch (e) {
        // Ignore errors when canceling subscriptions
      }
    }
    activeSubscriptions.clear();
  }
}
