import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/exceptions/limit_exceptions.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Helper service for repositories to check limits
/// This provides a bridge between the old repository pattern and the new limits system
class RepositoryLimitsHelper {
  final ProviderContainer _container;

  RepositoryLimitsHelper(this._container);

  /// Check if the user has reached the maximum number of ideabooks
  Future<bool> isUserAtIdeabookLimit() async {
    try {
      final maxIdeabooks = await _container.read(maxIdeabooksProvider.future);
      // This would need to be implemented by getting the actual count
      // For now, we'll return false as a placeholder
      Logger.debug(
        'RepositoryLimitsHelper: Max ideabooks for user: $maxIdeabooks',
      );
      return false; // TODO: Implement actual count check
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check ideabook limit', e);
      return false; // In case of error, assume not at limit to avoid blocking the user
    }
  }

  /// Check if an ideabook has reached the maximum number of ideas
  Future<bool> isIdeabookFullOfIdeas(String ideabookId) async {
    try {
      Logger.debug(
        'RepositoryLimitsHelper: Checking ideas limit for ideabook: $ideabookId',
      );

      final maxIdeas = await _container.read(
        maxIdeasPerIdeabookProvider.future,
      );
      Logger.debug(
        'RepositoryLimitsHelper: Max ideas per ideabook for user: $maxIdeas',
      );

      // Get the actual count of ideas in the ideabook
      // Note: This requires access to the idea repository
      // For now, we'll implement a basic check that can be extended
      final currentCount = await _getIdeaCount(ideabookId);
      Logger.debug('RepositoryLimitsHelper: Current idea count: $currentCount');

      final isAtLimit = currentCount >= maxIdeas;
      Logger.debug('RepositoryLimitsHelper: Is at ideas limit: $isAtLimit');

      return isAtLimit;
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check ideas limit', e);
      return false; // In case of error, assume not full to avoid blocking the user
    }
  }

  /// Check if an ideabook has reached the maximum number of notes
  Future<bool> isIdeabookFullOfNotes(String ideabookId) async {
    try {
      Logger.debug(
        'RepositoryLimitsHelper: Checking notes limit for ideabook: $ideabookId',
      );

      final maxNotes = await _container.read(
        maxNotesPerIdeabookProvider.future,
      );
      Logger.debug(
        'RepositoryLimitsHelper: Max notes per ideabook for user: $maxNotes',
      );

      // Get the actual count of notes in the ideabook
      final currentCount = await _getNoteCount(ideabookId);
      Logger.debug('RepositoryLimitsHelper: Current note count: $currentCount');

      final isAtLimit = currentCount >= maxNotes;
      Logger.debug('RepositoryLimitsHelper: Is at notes limit: $isAtLimit');

      return isAtLimit;
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to check notes limit', e);
      return false; // In case of error, assume not full to avoid blocking the user
    }
  }

  /// Get the current count of ideas in an ideabook
  ///
  /// This is a placeholder implementation that should be replaced
  /// with actual repository calls when the idea repository is available
  Future<int> _getIdeaCount(String ideabookId) async {
    try {
      // TODO: Replace with actual idea repository call
      // For now, return 0 to avoid blocking users
      Logger.debug(
        'RepositoryLimitsHelper: Getting idea count for ideabook: $ideabookId (placeholder implementation)',
      );
      return 0;
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Error getting idea count', e);
      return 0;
    }
  }

  /// Get the current count of notes in an ideabook
  ///
  /// This is a placeholder implementation that should be replaced
  /// with actual repository calls when the note repository is available
  Future<int> _getNoteCount(String ideabookId) async {
    try {
      // TODO: Replace with actual note repository call
      // For now, return 0 to avoid blocking users
      Logger.debug(
        'RepositoryLimitsHelper: Getting note count for ideabook: $ideabookId (placeholder implementation)',
      );
      return 0;
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Error getting note count', e);
      return 0;
    }
  }

  /// Get the maximum number of ideabooks for the current user
  Future<int> getMaxIdeabooks() async {
    try {
      // Use real-time provider for immediate updates when user tier changes
      final maxIdeabooks = _container.read(realtimeMaxIdeabooksProvider);
      final userTier =
          _container.read(realtimeUserTierProvider).value ?? 'free';
      Logger.debug(
        'RepositoryLimitsHelper: getMaxIdeabooks - User tier: $userTier, Max ideabooks: $maxIdeabooks',
      );
      return maxIdeabooks;
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get max ideabooks', e);
      return 3; // Fallback to free tier limit
    }
  }

  /// Get the maximum number of ideas per ideabook for the current user
  Future<int> getMaxIdeasPerIdeabook() async {
    try {
      // Use real-time provider for immediate updates when user tier changes
      return _container.read(realtimeMaxIdeasPerIdeabookProvider);
    } catch (e) {
      Logger.error(
        'RepositoryLimitsHelper: Failed to get max ideas per ideabook',
        e,
      );
      return 10; // Fallback to free tier limit
    }
  }

  /// Get the maximum number of notes per ideabook for the current user
  Future<int> getMaxNotesPerIdeabook() async {
    try {
      // Use real-time provider for immediate updates when user tier changes
      return _container.read(realtimeMaxNotesPerIdeabookProvider);
    } catch (e) {
      Logger.error(
        'RepositoryLimitsHelper: Failed to get max notes per ideabook',
        e,
      );
      return 10; // Fallback to free tier limit
    }
  }

  /// Get the user tier
  Future<String> getUserTier() async {
    try {
      // Use real-time provider for immediate updates when user tier changes
      final userTierAsync = _container.read(realtimeUserTierProvider);
      return userTierAsync.value ?? 'free';
    } catch (e) {
      Logger.error('RepositoryLimitsHelper: Failed to get user tier', e);
      return 'free'; // Fallback to free tier
    }
  }

  /// Create an exception message for when the ideabook limit is reached
  Future<String> getIdeabookLimitExceptionMessage() async {
    final maxIdeabooks = await getMaxIdeabooks();
    return 'Maximum number of ideabooks reached ($maxIdeabooks).';
  }

  /// Create an exception message for when the ideas limit is reached
  Future<String> getIdeasLimitExceptionMessage() async {
    final maxIdeas = await getMaxIdeasPerIdeabook();
    return 'Ideabook is full. Maximum of $maxIdeas ideas reached.';
  }

  /// Create an exception message for when the notes limit is reached
  Future<String> getNotesLimitExceptionMessage() async {
    final maxNotes = await getMaxNotesPerIdeabook();
    return 'Ideabook is full. Maximum of $maxNotes notes reached.';
  }

  /// Throw appropriate exception for ideabook limit reached
  Future<void> throwIdeabookLimitException() async {
    final message = await getIdeabookLimitExceptionMessage();
    throw IdeabookLimitException(message);
  }

  /// Throw appropriate exception for ideas limit reached
  Future<void> throwIdeasLimitException() async {
    final message = await getIdeasLimitExceptionMessage();
    throw IdeasLimitException(message);
  }

  /// Throw appropriate exception for notes limit reached
  Future<void> throwNotesLimitException() async {
    final message = await getNotesLimitExceptionMessage();
    throw NotesLimitException(message);
  }

  /// Throw appropriate exception for chat limit reached
  void throwChatLimitException(String message) {
    throw ChatLimitException(message);
  }
}
