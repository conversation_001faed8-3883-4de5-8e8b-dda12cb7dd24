import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/chat_rate_limit.dart';
import 'package:noeji/services/limits/app_limits_service.dart';
import 'package:noeji/services/limits/repository_limits_helper.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for the app limits service
final appLimitsServiceProvider = Provider<AppLimitsService>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return AppLimitsService(remoteConfigService);
});

/// Provider for the repository limits helper
final repositoryLimitsHelperProvider = Provider<RepositoryLimitsHelper>((ref) {
  return RepositoryLimitsHelper(ref.container);
});

/// Provider for the current user's maximum ideabooks
final maxIdeabooksProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxIdeabooks(userTier: userTier);
});

/// Real-time provider for the current user's maximum ideabooks
/// This updates immediately when user tier changes
final realtimeMaxIdeabooksProvider = Provider<int>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTierAsync = ref.watch(realtimeUserTierProvider);

  // Return default free tier limit if user tier is loading or error
  final userTier = userTierAsync.value ?? 'free';
  final maxIdeabooks = appLimitsService.getMaxIdeabooks(userTier: userTier);

  Logger.debug(
    'realtimeMaxIdeabooksProvider: User tier: $userTier, Max ideabooks: $maxIdeabooks',
  );
  return maxIdeabooks;
});

/// Provider for the current user's maximum ideas per ideabook
final maxIdeasPerIdeabookProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxIdeasPerIdeabook(userTier: userTier);
});

/// Real-time provider for the current user's maximum ideas per ideabook
/// This updates immediately when user tier changes
final realtimeMaxIdeasPerIdeabookProvider = Provider<int>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTierAsync = ref.watch(realtimeUserTierProvider);

  // Return default free tier limit if user tier is loading or error
  final userTier = userTierAsync.value ?? 'free';
  return appLimitsService.getMaxIdeasPerIdeabook(userTier: userTier);
});

/// Provider for the current user's maximum notes per ideabook
final maxNotesPerIdeabookProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getMaxNotesPerIdeabook(userTier: userTier);
});

/// Real-time provider for the current user's maximum notes per ideabook
/// This updates immediately when user tier changes
final realtimeMaxNotesPerIdeabookProvider = Provider<int>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTierAsync = ref.watch(realtimeUserTierProvider);

  // Return default free tier limit if user tier is loading or error
  final userTier = userTierAsync.value ?? 'free';
  return appLimitsService.getMaxNotesPerIdeabook(userTier: userTier);
});

/// Provider for the current user's daily chat limit
final chatLimitDailyProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatLimitDaily(userTier: userTier);
});

/// Provider for the current user's monthly chat limit
final chatLimitMonthlyProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatLimitMonthly(userTier: userTier);
});

/// Provider for the current user's audio recording length limit
final audioRecordingLengthSecondsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getAudioRecordingLengthSeconds(userTier: userTier);
});

/// Provider for the current user's ideabook name max words
final ideabookNameMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getIdeabookNameMaxWords(userTier: userTier);
});

/// Provider for the current user's idea max words
final ideaMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getIdeaMaxWords(userTier: userTier);
});

/// Provider for the current user's chat input max words
final chatInputMaxWordsProvider = FutureProvider<int>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);
  return appLimitsService.getChatInputMaxWords(userTier: userTier);
});

/// Provider for the current user's chat rate limits
final chatRateLimitsProvider = FutureProvider<List<ChatRateLimit>>((ref) async {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTier = await ref.watch(userTierProvider.future);

  final rateLimits = <ChatRateLimit>[];

  // Add rate limit per minute for all users
  final rateLimitMinute = appLimitsService.getChatRateLimitMinute(
    userTier: userTier,
  );
  if (rateLimitMinute != null) {
    rateLimits.add(
      ChatRateLimit(
        maxMessages: rateLimitMinute,
        periodSeconds: 60,
        description: 'minute',
      ),
    );
  }

  // Add daily limit
  final dailyLimit = appLimitsService.getChatLimitDaily(userTier: userTier);
  rateLimits.add(
    ChatRateLimit(
      maxMessages: dailyLimit,
      periodSeconds: 86400, // 24 hours in seconds
      description: 'day',
    ),
  );

  // Add monthly limit
  final monthlyLimit = appLimitsService.getChatLimitMonthly(userTier: userTier);
  rateLimits.add(
    ChatRateLimit(
      maxMessages: monthlyLimit,
      periodSeconds: 2592000, // 30 days in seconds
      description: 'month',
    ),
  );

  return rateLimits;
});

/// Real-time provider for the current user's chat rate limits
/// This updates immediately when user tier changes
final realtimeChatRateLimitsProvider = Provider<List<ChatRateLimit>>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final appLimitsService = ref.watch(appLimitsServiceProvider);
  final userTierAsync = ref.watch(realtimeUserTierProvider);

  // Return default free tier limits if user tier is loading or error
  final userTier = userTierAsync.value ?? 'free';

  final rateLimits = <ChatRateLimit>[];

  // Add rate limit per minute for all users
  final rateLimitMinute = appLimitsService.getChatRateLimitMinute(
    userTier: userTier,
  );
  if (rateLimitMinute != null) {
    rateLimits.add(
      ChatRateLimit(
        maxMessages: rateLimitMinute,
        periodSeconds: 60,
        description: 'minute',
      ),
    );
  }

  // Add daily limit
  final dailyLimit = appLimitsService.getChatLimitDaily(userTier: userTier);
  rateLimits.add(
    ChatRateLimit(
      maxMessages: dailyLimit,
      periodSeconds: 86400, // 24 hours in seconds
      description: 'day',
    ),
  );

  // Add monthly limit
  final monthlyLimit = appLimitsService.getChatLimitMonthly(userTier: userTier);
  rateLimits.add(
    ChatRateLimit(
      maxMessages: monthlyLimit,
      periodSeconds: 2592000, // 30 days in seconds
      description: 'month',
    ),
  );

  return rateLimits;
});
