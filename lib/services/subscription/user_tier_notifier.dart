import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/services/preferences/app_behavior_provider.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Real-time user tier state that updates automatically when CustomerInfo changes
class UserTierNotifier extends StateNotifier<AsyncValue<String>> {
  final Ref _ref;
  String? _previousTier;
  bool _isDisposed = false;
  StreamSubscription<CustomerInfo>? _customerInfoSubscription;

  UserTierNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initialize();
  }

  /// Constructor for non-signed-in users that immediately sets free tier
  UserTierNotifier.forFreeUser(this._ref)
    : super(const AsyncValue.data('free')) {
    // Don't initialize RevenueCat listeners for free users
    Logger.debug('UserTierNotifier: Created for free user (not signed in)');
    _previousTier = 'free';
  }

  /// Initialize the notifier and set up CustomerInfo listener
  Future<void> _initialize() async {
    try {
      // Get initial user tier
      await _updateUserTier();

      // Set up listener for CustomerInfo updates from the centralized stream
      _setupCustomerInfoStreamListener();

      Logger.debug('UserTierNotifier: Initialized successfully');
    } catch (e) {
      Logger.error('UserTierNotifier: Failed to initialize', e);
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Set up listener for CustomerInfo updates from RevenueCat service stream
  void _setupCustomerInfoStreamListener() {
    try {
      // Check if RevenueCat is configured before setting up listener
      final revenueCatService = _ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug(
          'UserTierNotifier: RevenueCat not configured yet, skipping CustomerInfo stream listener setup',
        );
        return;
      }

      // Listen to the centralized CustomerInfo stream from RevenueCatService
      _customerInfoSubscription = revenueCatService.customerInfoStream.listen(
        (customerInfo) {
          // Check if this notifier has been disposed to avoid "Bad state" errors
          if (_isDisposed) {
            Logger.debug(
              'UserTierNotifier: Ignoring CustomerInfo update - notifier is disposed',
            );
            return;
          }

          Logger.debug(
            'UserTierNotifier: Received CustomerInfo update from stream',
          );
          _updateUserTierFromCustomerInfo(customerInfo);
        },
        onError: (error) {
          if (_isDisposed) return;
          Logger.error('UserTierNotifier: Error in CustomerInfo stream', error);
          // Default to free tier on error to be safe
          if (!_isDisposed) {
            state = const AsyncValue.data('free');
            _previousTier = 'free';
            _resetAppBehaviorForFreeUser();
          }
        },
      );

      Logger.debug('UserTierNotifier: Set up CustomerInfo stream listener');
    } catch (e) {
      Logger.error(
        'UserTierNotifier: Failed to set up CustomerInfo stream listener',
        e,
      );
    }
  }

  /// Update user tier by fetching latest CustomerInfo
  Future<void> _updateUserTier() async {
    // Check if this notifier has been disposed
    if (_isDisposed) {
      Logger.debug(
        'UserTierNotifier: Ignoring user tier update - notifier is disposed',
      );
      return;
    }

    try {
      // Check if RevenueCat is configured before calling getCustomerInfo
      final revenueCatService = _ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug(
          'UserTierNotifier: RevenueCat not configured yet, defaulting to free tier',
        );
        if (!_isDisposed) {
          state = const AsyncValue.data('free');
          _previousTier = 'free';
        }
        return;
      }

      final customerInfo = await revenueCatService.getCustomerInfo();
      if (customerInfo != null) {
        _updateUserTierFromCustomerInfo(customerInfo);
      } else {
        // If customerInfo is null, default to free tier
        if (!_isDisposed) {
          state = const AsyncValue.data('free');
          _previousTier = 'free';
        }
      }
    } catch (e) {
      Logger.error('UserTierNotifier: Failed to get CustomerInfo', e);
      // Only update state if not disposed
      if (!_isDisposed) {
        // Default to free tier on error to be safe
        state = const AsyncValue.data('free');
        _previousTier = 'free';
        _resetAppBehaviorForFreeUser();
      }
    }
  }

  /// Update user tier from CustomerInfo object
  void _updateUserTierFromCustomerInfo(CustomerInfo customerInfo) {
    // Check if this notifier has been disposed
    if (_isDisposed) {
      Logger.debug(
        'UserTierNotifier: Ignoring CustomerInfo update - notifier is disposed',
      );
      return;
    }

    try {
      // Check if the user has any active entitlements
      final hasActiveEntitlements = customerInfo.entitlements.active.isNotEmpty;
      final userTier = hasActiveEntitlements ? 'pro' : 'free';

      Logger.debug('UserTierNotifier: User tier determined as: $userTier');

      if (hasActiveEntitlements) {
        // Log the active entitlements for debugging
        final activeEntitlements = customerInfo.entitlements.active.keys.join(
          ', ',
        );
        Logger.debug(
          'UserTierNotifier: Active entitlements: $activeEntitlements',
        );
      }

      // Check if user tier changed from pro to free
      final isInitialLoad = _previousTier == null;
      final tierChanged = _previousTier != null && _previousTier != userTier;
      final becameFreeUser = tierChanged && userTier == 'free';
      final isFreeUserOnInitialLoad = isInitialLoad && userTier == 'free';

      // Update state with new tier (only if not disposed)
      if (!_isDisposed) {
        state = AsyncValue.data(userTier);
      }

      // Reset app behavior settings if user became a free user or is free on initial load
      if (becameFreeUser) {
        Logger.debug(
          'UserTierNotifier: User downgraded to free tier, resetting app behavior settings',
        );
        _resetAppBehaviorForFreeUser();
      } else if (isFreeUserOnInitialLoad) {
        // Also reset on initial load if user is free (handles app startup)
        Logger.debug(
          'UserTierNotifier: Initial load detected free user, resetting app behavior settings',
        );
        _resetAppBehaviorForFreeUser();
      }

      // Store the current tier for next comparison
      _previousTier = userTier;
    } catch (e) {
      Logger.error(
        'UserTierNotifier: Failed to determine user tier from CustomerInfo',
        e,
      );
      // Only update state if not disposed
      if (!_isDisposed) {
        // Default to free tier on error to be safe
        state = const AsyncValue.data('free');
        _previousTier = 'free';
        // Reset app behavior settings on error to be safe
        _resetAppBehaviorForFreeUser();
      }
    }
  }

  /// Reset app behavior settings for free users
  void _resetAppBehaviorForFreeUser() {
    // Check if this notifier has been disposed
    if (_isDisposed) {
      Logger.debug(
        'UserTierNotifier: Ignoring app behavior reset - notifier is disposed',
      );
      return;
    }

    try {
      // Use the ref to access the app behavior provider and reset settings
      final appBehaviorNotifier = _ref.read(appBehaviorProvider.notifier);
      // Reset all app behavior settings to defaults for free users
      appBehaviorNotifier.resetToDefaults();
      Logger.debug(
        'UserTierNotifier: App behavior settings reset for free user',
      );
    } catch (e) {
      Logger.error(
        'UserTierNotifier: Failed to reset app behavior for free user',
        e,
      );
    }
  }

  /// Force refresh the user tier (useful after purchases or app resume)
  Future<void> refresh() async {
    // Check if this notifier has been disposed
    if (_isDisposed) {
      Logger.debug('UserTierNotifier: Ignoring refresh - notifier is disposed');
      return;
    }

    Logger.debug('UserTierNotifier: Force refreshing user tier');
    if (!_isDisposed) {
      state = const AsyncValue.loading();
    }

    // Ensure CustomerInfo stream listener is set up (in case RevenueCat was initialized after this notifier was created)
    _ensureCustomerInfoStreamListener();

    await _updateUserTier();
  }

  /// Ensure CustomerInfo stream listener is set up
  /// This is called during refresh to handle cases where RevenueCat was initialized after this notifier was created
  void _ensureCustomerInfoStreamListener() {
    // Only try to set up if we don't already have a subscription
    if (_customerInfoSubscription != null) {
      Logger.debug(
        'UserTierNotifier: CustomerInfo stream listener already set up',
      );
      return;
    }

    // Try to set up the listener
    _setupCustomerInfoStreamListener();
  }

  /// Check if the user is a pro user (convenience method)
  bool get isProUser {
    return state.value == 'pro';
  }

  @override
  void dispose() {
    // Mark as disposed to prevent further operations
    _isDisposed = true;

    // Note: RevenueCat doesn't provide a way to remove individual CustomerInfo listeners
    // The listener will be automatically cleaned up when the app is terminated
    // However, we now check _isDisposed in the listener callback to prevent errors
    Logger.debug('UserTierNotifier: Disposed');
    _customerInfoSubscription?.cancel();
    super.dispose();
  }
}
