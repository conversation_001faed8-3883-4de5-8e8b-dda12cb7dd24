import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/utils/logger.dart';

/// Service for determining user tier (free vs pro)
class UserTierService {
  static UserTierService? _instance;
  static UserTierService get instance => _instance ??= UserTierService._();

  UserTierService._();

  /// Check if the user has an active pro subscription
  /// Returns true if the user is a pro user, false if free user
  Future<bool> isProUser() async {
    try {
      // Check if RevenueCat is configured before calling getCustomerInfo
      if (!(await Purchases.isConfigured)) {
        Logger.debug(
          'UserTierService: Purchases SDK not configured yet. Defaulting to free user.',
        );
        return false;
      }

      // Get customer info from RevenueCat
      final customerInfo = await Purchases.getCustomerInfo();

      // Check if the user has any active entitlements
      final hasActiveEntitlements = customerInfo.entitlements.active.isNotEmpty;

      Logger.debug(
        'UserTierService: User has active entitlements: $hasActiveEntitlements',
      );

      if (hasActiveEntitlements) {
        // Log the active entitlements for debugging
        final activeEntitlements = customerInfo.entitlements.active.keys.join(
          ', ',
        );
        Logger.debug(
          'UserTierService: Active entitlements: $activeEntitlements',
        );
      }

      return hasActiveEntitlements;
    } catch (e) {
      Logger.error('UserTierService: Failed to check user tier', e);
      // In case of error, assume free user to be safe
      return false;
    }
  }

  /// Get the user tier as a string
  /// Returns 'pro' for pro users, 'free' for free users
  Future<String> getUserTier() async {
    final isPro = await isProUser();
    return isPro ? 'pro' : 'free';
  }

  /// Get the user tier synchronously if possible
  /// This is a best-effort method that returns the cached tier if available
  /// Returns null if the tier cannot be determined synchronously
  String? getUserTierSync() {
    try {
      // Try to get cached customer info
      // Note: This might not always be available, so we return null if uncertain
      // The async method should be preferred for accurate results
      return null; // For now, always return null to force async usage
    } catch (e) {
      Logger.error('UserTierService: Failed to get user tier synchronously', e);
      return null;
    }
  }
}
