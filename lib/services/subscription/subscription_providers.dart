import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/revenue_cat_service.dart';
import 'package:noeji/services/subscription/user_tier_service.dart';
import 'package:noeji/services/subscription/user_tier_notifier.dart';
import 'package:noeji/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

/// Provider for the RevenueCat service
final revenueCatServiceProvider = Provider<RevenueCatService>((ref) {
  return RevenueCatService();
});

/// Provider for the user tier service
final userTierServiceProvider = Provider<UserTierService>((ref) {
  return UserTierService.instance;
});

/// Provider for the real-time user tier notifier
/// This provides real-time updates when user tier changes
/// The notifier lifecycle is tied to Firebase authentication state
final userTierNotifierProvider = StateNotifierProvider<
  UserTierNotifier,
  AsyncValue<String>
>((ref) {
  // Watch Firebase user to react to authentication changes
  final firebaseUserAsync = ref.watch(firebaseUserProvider);

  // Get the current user state
  final user = firebaseUserAsync.when(
    loading: () {
      // While loading, check if we can determine auth state synchronously
      final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
      Logger.debug(
        'userTierNotifierProvider: Firebase loading, current user: ${currentUser?.uid ?? 'null'}',
      );
      return currentUser;
    },
    error: (error, stackTrace) {
      // On error, default to no user
      Logger.error('Error in userTierNotifierProvider', error);
      return null;
    },
    data: (firebaseUser) {
      Logger.debug(
        'userTierNotifierProvider: Firebase user data: ${firebaseUser?.uid ?? 'null'}',
      );
      return firebaseUser;
    },
  );

  if (user == null) {
    // No Firebase user, create free user notifier
    Logger.debug(
      'userTierNotifierProvider: No user, creating free user notifier',
    );
    return UserTierNotifier.forFreeUser(ref);
  } else {
    // User is signed in, ensure RevenueCat is initialized before creating notifier
    // This will suspend until RevenueCat initialization is complete
    final initializationAsync = ref.watch(ensureRevenueCatInitializedProvider);

    return initializationAsync.when(
      loading: () {
        // RevenueCat is still initializing, return a temporary free user notifier
        Logger.debug(
          'userTierNotifierProvider: Waiting for RevenueCat initialization',
        );
        return UserTierNotifier.forFreeUser(
          ref,
        ); // Temporary while RC initializes
      },
      error: (error, stackTrace) {
        // RevenueCat initialization failed, default to free user
        Logger.error(
          'userTierNotifierProvider: RevenueCat initialization failed',
          error,
        );
        return UserTierNotifier.forFreeUser(ref);
      },
      data: (_) {
        // RevenueCat is properly initialized, create the full notifier
        Logger.debug(
          'userTierNotifierProvider: RevenueCat initialized, creating UserTierNotifier for user: ${user.uid}',
        );
        return UserTierNotifier(ref);
      },
    );
  }
});

/// Provider for real-time user tier as string
/// This replaces the old userTierProvider with real-time updates
final realtimeUserTierProvider = Provider<AsyncValue<String>>((ref) {
  return ref.watch(userTierNotifierProvider);
});

/// Provider for real-time pro user status
/// This replaces the old isProUserProvider with real-time updates
final realtimeIsProUserProvider = Provider<bool>((ref) {
  final userTierAsync = ref.watch(userTierNotifierProvider);
  return userTierAsync.value == 'pro';
});

/// Provider for refreshing user tier after purchases
/// This can be called to force refresh the user tier state
final refreshUserTierProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    try {
      Logger.debug('Refreshing user tier after purchase');

      // Check if user is signed in
      final userAsync = ref.read(firebaseUserProvider);
      final user = userAsync.value;
      if (user == null) {
        Logger.debug('User not signed in, skipping user tier refresh');
        return;
      }

      // Check if RevenueCat is configured
      final revenueCatService = ref.read(revenueCatServiceProvider);
      if (!revenueCatService.isConfigured) {
        Logger.debug('RevenueCat not configured, skipping user tier refresh');
        return;
      }

      // Force refresh CustomerInfo from RevenueCat
      // This will trigger the customerInfoStream that UserTierNotifier listens to
      await revenueCatService.refreshCustomerInfo();

      // Also directly refresh the UserTierNotifier to ensure immediate UI update
      try {
        final userTierNotifier = ref.read(userTierNotifierProvider.notifier);
        await userTierNotifier.refresh();
        Logger.debug('UserTierNotifier refreshed directly');
      } catch (e) {
        Logger.error('Failed to refresh UserTierNotifier directly', e);
        // Don't fail the overall refresh if this fails
      }

      Logger.debug('User tier refresh completed');
    } catch (e) {
      Logger.error('Failed to refresh user tier', e);
      rethrow;
    }
  };
});

/// Provider for checking if the user is a pro user
/// Note: This uses UserTierService which internally calls RevenueCatService methods
/// We ensure RevenueCat is initialized before calling UserTierService methods
final isProUserProvider = FutureProvider<bool>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are not a pro user
  if (user == null) {
    return false;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.isProUser();
  } catch (e) {
    Logger.error('Failed to check if user is pro', e);
    return false;
  }
});

/// Provider for getting the user tier as a string
/// Note: This uses UserTierService which internally calls RevenueCatService methods
/// We ensure RevenueCat is initialized before calling UserTierService methods
final userTierProvider = FutureProvider<String>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, they are a free user
  if (user == null) {
    return 'free';
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  try {
    final userTierService = ref.watch(userTierServiceProvider);
    return await userTierService.getUserTier();
  } catch (e) {
    Logger.error('Failed to get user tier', e);
    return 'free';
  }
});

/// Provider to ensure RevenueCat is initialized when the user is signed in
/// and logged out when the user signs out
/// Returns a Future that completes when RevenueCat is properly configured for the current user
/// This provider will re-run when firebaseUserProvider emits new values
final ensureRevenueCatInitializedProvider = FutureProvider<void>((ref) async {
  // Watch the current Firebase user
  final userAsync = ref.watch(firebaseUserProvider);
  final revenueCatService = ref.watch(revenueCatServiceProvider);

  Logger.debug(
    'ensureRevenueCatInitializedProvider: Starting initialization check',
  );

  // Handle the AsyncValue properly
  // Note: Treating loading as null allows the provider to complete while Firebase auth is loading
  // The provider will re-run when firebaseUserProvider resolves to a definite state
  final user = userAsync.when(
    loading: () {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: Firebase user loading, treating as no user',
      );
      return null; // Treat loading as no user for now
    },
    error: (error, stackTrace) {
      Logger.error(
        'Error getting user in ensureRevenueCatInitializedProvider',
        error,
      );
      return null;
    },
    data: (user) {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: Firebase user data received: ${user?.uid ?? 'null'}',
      );
      return user;
    },
  );

  if (user != null) {
    // User is signed in, ensure RevenueCat is initialized
    Logger.debug(
      'ensureRevenueCatInitializedProvider: User is signed in, ensuring RevenueCat initialization with user ID: ${user.uid}',
    );
    try {
      // RevenueCatService.initialize() has internal checks to prevent redundant calls
      // for the same user, so this is safe even if called multiple times
      await revenueCatService.initialize(userId: user.uid);
      Logger.debug(
        'ensureRevenueCatInitializedProvider: RevenueCat initialization completed for user: ${user.uid}',
      );
    } catch (e) {
      Logger.error(
        'ensureRevenueCatInitializedProvider: Failed to initialize RevenueCat for user ${user.uid}',
        e,
      );
      rethrow;
    }
  } else {
    // User signed out (or was never signed in, or is loading)
    if (revenueCatService.isConfigured) {
      // Only logout if it was configured for a previous user
      Logger.debug(
        'ensureRevenueCatInitializedProvider: User signed out, ensuring RevenueCat logout',
      );
      try {
        // RevenueCatService.logout() has internal checks to prevent redundant calls
        await revenueCatService.logout();
        Logger.debug(
          'ensureRevenueCatInitializedProvider: RevenueCat logout completed',
        );
      } catch (e) {
        Logger.error(
          'ensureRevenueCatInitializedProvider: Failed to logout from RevenueCat',
          e,
        );
        rethrow;
      }
    } else {
      Logger.debug(
        'ensureRevenueCatInitializedProvider: User signed out, but RevenueCat was not configured',
      );
    }
  }

  Logger.debug(
    'ensureRevenueCatInitializedProvider: Initialization check completed',
  );
});

/// Provider for customer info from RevenueCat
final customerInfoProvider = FutureProvider<CustomerInfo?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for customer info');
    return null;
  }

  try {
    return await revenueCatService.getCustomerInfo();
  } catch (e) {
    Logger.error('Failed to get customer info', e);
    return null;
  }
});

/// Provider for offerings from RevenueCat
final offeringsProvider = FutureProvider<Offerings?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for offerings');
    return null;
  }

  try {
    return await revenueCatService.getOfferings();
  } catch (e) {
    Logger.error('Failed to get offerings', e);
    return null;
  }
});

/// Provider for the app user ID from RevenueCat
final appUserIdProvider = FutureProvider<String?>((ref) async {
  // Get the current user
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.value;

  // If user is not signed in, return null
  if (user == null) {
    return null;
  }

  // Ensure RevenueCat is initialized for this user
  await ref.watch(ensureRevenueCatInitializedProvider.future);

  // Check if RevenueCat is configured
  final revenueCatService = ref.watch(revenueCatServiceProvider);
  if (!revenueCatService.isConfigured) {
    Logger.debug('RevenueCat not configured, returning null for app user ID');
    return null;
  }

  try {
    return await revenueCatService.getAppUserId();
  } catch (e) {
    Logger.error('Failed to get app user ID', e);
    return null;
  }
});
