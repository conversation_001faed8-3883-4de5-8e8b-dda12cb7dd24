import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/color_palette/color_palette_service.dart';
import 'package:noeji/services/preferences/color_palette_preference_provider.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';
import 'package:noeji/utils/logger.dart';

/// Provider for the color palette service
final colorPaletteServiceProvider = Provider<ColorPaletteService>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return ColorPaletteService(remoteConfigService);
});

/// Provider for all available color palettes
/// Automatically updates when remote config changes
final colorPalettesProvider = Provider<Map<String, ColorPalette>>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  final colorPaletteService = ref.watch(colorPaletteServiceProvider);
  return colorPaletteService.getColorPalettes();
});

/// Provider for the current active color palette name
/// Automatically updates when remote config or user preference changes
final currentPaletteNameProvider = Provider<String>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);

  // Watch for user preference changes
  final colorPalettePreference = ref.watch(colorPalettePreferenceProvider);

  final colorPaletteService = ref.watch(colorPaletteServiceProvider);
  return colorPaletteService.getCurrentPaletteNameWithPreference(
    colorPalettePreference.selectedPaletteName,
  );
});

/// Provider for the current active color palette
/// Automatically updates when remote config or user preference changes
final currentColorPaletteProvider = Provider<ColorPalette>((ref) {
  // Get the current palette name (which includes user preference)
  final currentPaletteName = ref.watch(currentPaletteNameProvider);

  // Get all available palettes
  final palettes = ref.watch(colorPalettesProvider);

  // Find the current palette
  final palette = palettes[currentPaletteName];
  if (palette != null) {
    Logger.debug('Current color palette: ${palette.name}');
    return palette;
  }

  // Fallback to default if current palette not found
  Logger.debug(
    'Current palette "$currentPaletteName" not found, using default',
  );
  final defaultPalette = palettes['default'];
  if (defaultPalette != null) {
    return defaultPalette;
  }

  // Final fallback - create a default palette
  Logger.error('No default palette found, creating fallback');
  return ColorPalette(
    name: 'default',
    colors: {
      'red': const Color(0xFFE76F51),
      'orange': const Color(0xFFF4A261),
      'yellow': const Color(0xFFE9C46A),
      'green': const Color(0xFF8AB17D),
      'blue': const Color(0xFF2A9D8F),
      'purple': const Color(0xFF264653),
    },
  );
});
