import 'dart:ui';
import 'package:noeji/services/remote_config/remote_config_service.dart';
import 'package:noeji/utils/logger.dart';

/// Service for managing ideabook color palettes from Remote Config
class ColorPaletteService {
  final RemoteConfigService _remoteConfigService;

  ColorPaletteService(this._remoteConfigService);

  /// Get all available color palettes
  Map<String, ColorPalette> getColorPalettes() {
    try {
      final palettesJson = _remoteConfigService.getJson(
        'ideabook_color_palettes',
      );
      final palettes = <String, ColorPalette>{};

      for (final entry in palettesJson.entries) {
        final paletteName = entry.key;
        final paletteString = entry.value as String;

        final palette = _parseColorPalette(paletteName, paletteString);
        if (palette != null) {
          palettes[paletteName] = palette;
        }
      }

      Logger.debug(
        'Loaded ${palettes.length} color palettes: ${palettes.keys.join(', ')}',
      );
      return palettes;
    } catch (e) {
      Logger.error('Error loading color palettes from Remote Config', e);
      // Return default palette as fallback
      return {'default': _getDefaultColorPalette()};
    }
  }

  /// Get the current active color palette name
  /// This method is now deprecated - use the provider-based approach instead
  String getCurrentPaletteName() {
    try {
      return _remoteConfigService.getString(
        'default_ideabook_color_palette_name',
      );
    } catch (e) {
      Logger.error('Error getting current palette name from Remote Config', e);
      return 'default';
    }
  }

  /// Get the current active color palette name with user preference override
  String getCurrentPaletteNameWithPreference(String? userPreference) {
    try {
      // Use user preference if available, otherwise fall back to remote config default
      if (userPreference != null && userPreference.isNotEmpty) {
        Logger.debug(
          'Using user preference for color palette: $userPreference',
        );
        return userPreference;
      }

      final defaultName = _remoteConfigService.getString(
        'default_ideabook_color_palette_name',
      );
      Logger.debug(
        'Using remote config default for color palette: $defaultName',
      );
      return defaultName;
    } catch (e) {
      Logger.error('Error getting current palette name', e);
      return 'default';
    }
  }

  /// Get the current active color palette
  ColorPalette getCurrentPalette() {
    final palettes = getColorPalettes();
    final currentName = getCurrentPaletteName();

    final palette = palettes[currentName];
    if (palette != null) {
      Logger.debug('Using color palette: $currentName');
      return palette;
    }

    Logger.debug('Current palette "$currentName" not found, using default');
    return palettes['default'] ?? _getDefaultColorPalette();
  }

  /// Parse a color palette string into a ColorPalette object
  ColorPalette? _parseColorPalette(String name, String paletteString) {
    try {
      final colorPairs = paletteString.split(',');
      final colors = <String, Color>{};

      for (final pair in colorPairs) {
        final parts = pair.trim().split(':');
        if (parts.length == 2) {
          final colorName = parts[0].trim().toLowerCase();
          final colorHex = parts[1].trim();

          final color = _parseHexColor(colorHex);
          if (color != null) {
            colors[colorName] = color;
          }
        }
      }

      // Validate that all required colors are present
      final requiredColors = [
        'red',
        'orange',
        'yellow',
        'green',
        'blue',
        'purple',
      ];
      for (final requiredColor in requiredColors) {
        if (!colors.containsKey(requiredColor)) {
          Logger.error(
            'Color palette "$name" missing required color: $requiredColor',
          );
          return null;
        }
      }

      Logger.debug('Parsed color palette "$name" with ${colors.length} colors');
      return ColorPalette(name: name, colors: colors);
    } catch (e) {
      Logger.error('Error parsing color palette "$name": $paletteString', e);
      return null;
    }
  }

  /// Parse a hex color string to a Color object
  Color? _parseHexColor(String hexString) {
    try {
      // Remove # if present
      String hex = hexString.replaceAll('#', '');

      // Add FF for alpha if not present (6 digit hex)
      if (hex.length == 6) {
        hex = 'FF$hex';
      }

      if (hex.length == 8) {
        return Color(int.parse(hex, radix: 16));
      }

      Logger.error('Invalid hex color format: $hexString');
      return null;
    } catch (e) {
      Logger.error('Error parsing hex color: $hexString', e);
      return null;
    }
  }

  /// Get the default color palette as fallback
  ColorPalette _getDefaultColorPalette() {
    return ColorPalette(
      name: 'default',
      colors: {
        'red': const Color(0xFFE76F51),
        'orange': const Color(0xFFF4A261),
        'yellow': const Color(0xFFE9C46A),
        'green': const Color(0xFF8AB17D),
        'blue': const Color(0xFF2A9D8F),
        'purple': const Color(0xFF264653),
      },
    );
  }
}

/// Represents a color palette with a name and color mappings
class ColorPalette {
  final String name;
  final Map<String, Color> colors;

  const ColorPalette({required this.name, required this.colors});

  /// Get a color by name, with fallback to a default color
  Color getColor(String colorName, {Color? fallback}) {
    final color = colors[colorName.toLowerCase()];
    if (color != null) {
      return color;
    }

    if (fallback != null) {
      return fallback;
    }

    // Return a default gray color if nothing else is available
    return const Color(0xFFEEEEEE);
  }

  /// Get the color for 'none' (always gray)
  Color get noneColor => const Color(0xFFEEEEEE);

  /// Get red color
  Color get redColor => getColor('red');

  /// Get orange color
  Color get orangeColor => getColor('orange');

  /// Get yellow color
  Color get yellowColor => getColor('yellow');

  /// Get green color
  Color get greenColor => getColor('green');

  /// Get blue color
  Color get blueColor => getColor('blue');

  /// Get purple color
  Color get purpleColor => getColor('purple');

  @override
  String toString() {
    return 'ColorPalette(name: $name, colors: ${colors.keys.join(', ')})';
  }
}
