import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/audio/audio_recording_controller.dart';
import 'package:noeji/services/llm/llm_providers.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/ui/providers/idea_edit_recording_provider.dart';
import 'package:noeji/ui/providers/idea_edit_transcription_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Controller for recording audio to append to an idea in edit mode
class IdeaEditRecordingController extends BaseAudioRecordingController {
  /// The idea ID for which the recording is being made
  final String ideaId;

  /// Callback to handle the transcribed text
  final Function(String transcribedText)? onTranscriptionComplete;

  /// Constructor
  IdeaEditRecordingController({
    required this.ideaId,
    this.onTranscriptionComplete,
  });

  @override
  String get controllerName => 'IdeaEditRecordingController';

  @override
  Future<void> handleRecordingCompleted(
    WidgetRef ref,
    String filePath,
    Duration duration,
  ) async {
    Logger.debug(
      '$controllerName: Recording completed for idea $ideaId: $filePath, duration: ${duration.inSeconds}s',
    );

    // Set the state to processing
    beginAudioProcessing(ref);

    try {
      // Transcribe the audio
      Logger.debug('$controllerName: Transcribing audio...');
      final llmService = safelyUseRef(ref, (r) => r.read(llmServiceProvider));
      if (llmService == null) {
        Logger.error(
          '$controllerName: Could not access LLM service - widget may be disposed',
        );
        return;
      }

      final transcriptionResult = await llmService.transcribeAudio(
        filePath,
        useCase: TranscriptionUseCase.newIdea,
      );

      if (transcriptionResult.isSuccess && transcriptionResult.idea != null) {
        // Get the transcribed text
        final transcribedText = transcriptionResult.idea!;
        Logger.debug(
          '$controllerName: Transcription successful: $transcribedText',
        );

        // Store the transcription result in the provider
        safelyUseRef(ref, (r) {
          r
              .read(ideaEditTranscriptionProvider.notifier)
              .state = IdeaEditTranscription(
            ideaId: ideaId,
            transcribedText: transcribedText,
          );
        });

        // Call the callback with the transcribed text (for backward compatibility)
        if (onTranscriptionComplete != null) {
          onTranscriptionComplete!(transcribedText);
        }
      } else {
        // Handle transcription failure
        Logger.error(
          '$controllerName: Transcription failed: ${transcriptionResult.errorMessage}',
        );
        showNotification(ref, 'Transcription failed');
      }
    } catch (e) {
      Logger.error('$controllerName: Error processing recording', e);
      showNotification(ref, 'Error processing recording');
    } finally {
      // Reset the processing state
      completeAudioProcessing(ref);

      // Exit recording mode
      safelyUseRef(ref, (r) {
        r.read(ideaEditRecordingProvider.notifier).state = null;
      });
    }
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    Logger.debug('$controllerName: Recording cancelled for idea $ideaId');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideaEditRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    Logger.error(
      '$controllerName: Recording failed for idea $ideaId: $errorMessage',
    );

    // Show error notification
    showNotification(ref, 'Recording failed');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideaEditRecordingProvider.notifier).state = null;
    });
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    Logger.error(
      '$controllerName: Microphone permission denied for idea $ideaId',
    );

    // Show permission error notification
    showNotification(ref, 'Microphone permission required');

    // Exit recording mode
    safelyUseRef(ref, (r) {
      r.read(ideaEditRecordingProvider.notifier).state = null;
    });
  }

  @override
  void showNotification(WidgetRef ref, String message) {
    // Show a snackbar notification
    safelyUseRef(ref, (r) {
      // Use a simple logger for now since we don't have a specific notification provider for ideas
      Logger.debug('$controllerName: $message');
    });
  }
}

/// Provider for the idea edit recording controller
final ideaEditRecordingControllerProvider =
    Provider.family<IdeaEditRecordingController, IdeaEditRecordingParams>((
      ref,
      params,
    ) {
      return IdeaEditRecordingController(
        ideaId: params.ideaId,
        onTranscriptionComplete: params.onTranscriptionComplete,
      );
    });

/// Parameters for the idea edit recording controller
class IdeaEditRecordingParams {
  /// The idea ID
  final String ideaId;

  /// Callback for when transcription is complete
  final Function(String transcribedText)? onTranscriptionComplete;

  /// Constructor
  const IdeaEditRecordingParams({
    required this.ideaId,
    this.onTranscriptionComplete,
  });
}
