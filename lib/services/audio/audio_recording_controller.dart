import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/audio_recording_state.dart';
import 'package:noeji/services/audio/audio_providers.dart';
import 'package:noeji/ui/providers/audio_processing_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Base class for audio recording controllers
/// This class provides common functionality for recording audio in different contexts
abstract class BaseAudioRecordingController {
  /// The name of the controller for logging purposes
  String get controllerName;

  /// Start recording
  Future<bool> startRecording(WidgetRef ref) async {
    Logger.debug('$controllerName: startRecording called');

    // Reset the global audio processing state to ensure it doesn't interfere with this recording
    try {
      ref.read(audioProcessingStateProvider.notifier).state =
          AudioProcessingState.recording;
    } catch (e) {
      // Ignore errors
      Logger.debug(
        '$controllerName: Could not reset global processing state: $e',
      );
    }

    // Explicitly reset the recording state before starting a new recording
    // This ensures that any previous recording state doesn't persist
    final recordingStateNotifier = ref.read(
      audioRecordingStateProvider.notifier,
    );
    recordingStateNotifier.resetState();
    Logger.debug(
      '$controllerName: Reset recording state before starting new recording',
    );

    // Start the new recording
    final result = await recordingStateNotifier.startRecording();

    if (!result) {
      // Check if permission was denied
      final state = ref.read(audioRecordingStateProvider);
      if (state.errorMessage?.contains('permission') ?? false) {
        await handlePermissionDenied(ref);
      } else {
        await handleRecordingFailed(ref, state.errorMessage ?? 'Unknown error');
      }
    }

    return result;
  }

  /// Stop recording
  Future<bool> stopRecording(WidgetRef ref) async {
    Logger.debug('$controllerName: stopRecording called');

    final recordingStateNotifier = ref.read(
      audioRecordingStateProvider.notifier,
    );
    final result = await recordingStateNotifier.stopRecording();

    if (result) {
      // Get the recording state
      final state = ref.read(audioRecordingStateProvider);
      if (state.state == RecordingState.completed && state.filePath != null) {
        // Store the file path and duration before resetting the state
        final filePath = state.filePath!;
        final duration = state.duration;

        // Handle successful recording
        await handleRecordingCompleted(ref, filePath, duration);

        // Reset the state after handling the recording to ensure it's clean for next time
        // We do this after handling the recording to ensure the file path is still available
        recordingStateNotifier.resetState();
        Logger.debug(
          '$controllerName: Reset recording state after successful completion',
        );
      }
    } else {
      // Handle recording failure
      final state = ref.read(audioRecordingStateProvider);
      await handleRecordingFailed(ref, state.errorMessage ?? 'Unknown error');

      // Reset the state after handling the failure
      recordingStateNotifier.resetState();
      Logger.debug('$controllerName: Reset recording state after failure');
    }

    return result;
  }

  /// Cancel recording
  Future<bool> cancelRecording(WidgetRef ref) async {
    Logger.debug('$controllerName: cancelRecording called');

    final recordingStateNotifier = ref.read(
      audioRecordingStateProvider.notifier,
    );
    final result = await recordingStateNotifier.cancelRecording();

    if (result) {
      await handleRecordingCancelled(ref);
    }

    // Explicitly reset the state after cancellation to ensure it's clean
    recordingStateNotifier.resetState();
    Logger.debug('$controllerName: Reset recording state after cancellation');

    return result;
  }

  /// Check if recording is in progress
  bool isRecording(WidgetRef ref) {
    final recordingState = ref.read(audioRecordingStateProvider);
    return recordingState.state == RecordingState.recording;
  }

  /// Get the current amplitude
  double getAmplitude(WidgetRef ref) {
    final recordingState = ref.read(audioRecordingStateProvider);
    return recordingState.amplitude;
  }

  /// Get the current duration
  Duration getDuration(WidgetRef ref) {
    final recordingState = ref.read(audioRecordingStateProvider);
    return recordingState.duration;
  }

  /// Get the current file path
  String? getFilePath(WidgetRef ref) {
    final recordingState = ref.read(audioRecordingStateProvider);
    return recordingState.filePath;
  }

  /// Handle recording completion
  /// This method should be implemented by subclasses to handle the specific
  /// behavior when a recording is completed successfully
  Future<void> handleRecordingCompleted(
    WidgetRef ref,
    String filePath,
    Duration duration,
  );

  /// Handle recording cancellation
  /// This method should be implemented by subclasses to handle the specific
  /// behavior when a recording is cancelled
  Future<void> handleRecordingCancelled(WidgetRef ref);

  /// Handle recording failure
  /// This method should be implemented by subclasses to handle the specific
  /// behavior when a recording fails
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage);

  /// Handle permission denial
  /// This method should be implemented by subclasses to handle the specific
  /// behavior when microphone permission is denied
  Future<void> handlePermissionDenied(WidgetRef ref);

  /// Begin audio processing
  /// This is a utility method to set the audio processing state to processing
  /// Note: This method is kept for backward compatibility but should be avoided
  /// in favor of using local state in the recording panel
  void beginAudioProcessing(WidgetRef ref) {
    try {
      // Reset the global state to recording first to ensure it doesn't interfere with other recording instances
      ref.read(audioProcessingStateProvider.notifier).state =
          AudioProcessingState.recording;
      // Then set it to processing for backward compatibility
      ref.read(audioProcessingStateProvider.notifier).state =
          AudioProcessingState.processing;
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug(
        '$controllerName: Could not begin audio processing - widget may be disposed',
      );
    }
  }

  /// Complete audio processing
  /// This is a utility method to set the audio processing state to completed
  /// Note: This method is kept for backward compatibility but should be avoided
  /// in favor of using local state in the recording panel
  void completeAudioProcessing(WidgetRef ref) {
    try {
      // Reset the global state to recording to ensure it doesn't interfere with other recording instances
      ref.read(audioProcessingStateProvider.notifier).state =
          AudioProcessingState.recording;
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug(
        '$controllerName: Could not complete audio processing - widget may be disposed',
      );
    }
  }

  /// Show notification
  /// This is a utility method to show a notification using the appropriate provider
  /// Subclasses should override this to use their specific notification mechanism
  void showNotification(WidgetRef ref, String message) {
    try {
      Logger.debug('$controllerName: $message');
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug(
        '$controllerName: Could not show notification - widget may be disposed',
      );
    }
  }

  /// Safely use ref
  /// This is a utility method to safely use a ref, catching any exceptions if the widget is disposed
  T? safelyUseRef<T>(
    WidgetRef ref,
    T Function(WidgetRef) action, {
    T? defaultValue,
  }) {
    try {
      return action(ref);
    } catch (e) {
      // Handle the case where the ref is no longer valid (widget disposed)
      Logger.debug(
        '$controllerName: Could not use ref - widget may be disposed',
      );
      return defaultValue;
    }
  }
}

/// Common implementation of audio recording controller
/// This class provides a default implementation of the abstract methods
/// in BaseAudioRecordingController
class CommonAudioRecordingController extends BaseAudioRecordingController {
  @override
  String get controllerName => 'CommonAudioRecordingController';

  @override
  Future<void> handleRecordingCompleted(
    WidgetRef ref,
    String filePath,
    Duration duration,
  ) async {
    // This is a base implementation that can be overridden by subclasses
    Logger.debug(
      '$controllerName: Recording completed: $filePath, duration: ${duration.inSeconds}s',
    );
  }

  @override
  Future<void> handleRecordingCancelled(WidgetRef ref) async {
    // This is a base implementation that can be overridden by subclasses
    Logger.debug('$controllerName: Recording cancelled');
  }

  @override
  Future<void> handleRecordingFailed(WidgetRef ref, String errorMessage) async {
    // This is a base implementation that can be overridden by subclasses
    Logger.error('$controllerName: Recording failed: $errorMessage');
  }

  @override
  Future<void> handlePermissionDenied(WidgetRef ref) async {
    // This is a base implementation that can be overridden by subclasses
    Logger.error('$controllerName: Microphone permission denied');
  }
}

/// Provider for the common audio recording controller
final commonAudioRecordingControllerProvider =
    Provider<CommonAudioRecordingController>((ref) {
      return CommonAudioRecordingController();
    });
