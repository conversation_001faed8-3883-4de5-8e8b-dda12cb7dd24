import 'dart:async';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/id_utils.dart';

/// Result of an audio recording operation
class AudioRecordingResult {
  /// Path to the recorded audio file
  final String filePath;

  /// Duration of the recording in milliseconds
  final Duration duration;

  /// Whether the recording was successful
  final bool isSuccess;

  /// Error message if the recording failed
  final String? errorMessage;

  /// Creates a new AudioRecordingResult
  const AudioRecordingResult({
    required this.filePath,
    required this.duration,
    required this.isSuccess,
    this.errorMessage,
  });

  /// Creates a successful result
  factory AudioRecordingResult.success({
    required String filePath,
    required Duration duration,
  }) {
    return AudioRecordingResult(
      filePath: filePath,
      duration: duration,
      isSuccess: true,
    );
  }

  /// Creates a failed result
  factory AudioRecordingResult.failure({required String errorMessage}) {
    return AudioRecordingResult(
      filePath: '',
      duration: Duration.zero,
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}

/// Service for recording audio
class AudioRecordingService {
  final _record = AudioRecorder();

  bool _isRecording = false;
  String? _currentFilePath;
  DateTime? _recordingStartTime;

  /// Stream controller for amplitude updates
  final StreamController<double> _amplitudeStreamController =
      StreamController<double>.broadcast();

  /// Stream of amplitude updates during recording
  Stream<double> get amplitudeStream => _amplitudeStreamController.stream;

  /// Whether the service is currently recording
  bool get isRecording => _isRecording;

  /// Current recording file path
  String? get currentFilePath => _currentFilePath;

  /// Dispose the service
  void dispose() {
    _amplitudeStreamController.close();
    _record.dispose();
  }

  /// Request microphone permission
  Future<PermissionStatus> requestPermission() async {
    Logger.debug('Requesting microphone permission...');

    // First check the current status
    final currentStatus = await Permission.microphone.status;
    Logger.debug('Current microphone permission status: ${currentStatus.name}');

    // On iOS, we need to make sure the permission is requested properly
    if (Platform.isIOS) {
      Logger.debug(
        'iOS platform detected, using special permission request approach',
      );

      // If the permission is not determined yet, request it
      if (currentStatus.isDenied || currentStatus.isPermanentlyDenied) {
        // Force a direct request to ensure the system dialog appears
        final status = await Permission.microphone.request();
        Logger.debug(
          'iOS microphone permission request result: ${status.name}',
        );
        return status;
      } else {
        // Permission is already granted or restricted
        return currentStatus;
      }
    } else {
      // For Android and other platforms, use the standard approach
      final status = await Permission.microphone.request();
      Logger.debug('Microphone permission status: ${status.name}');
      return status;
    }
  }

  /// Check if microphone permission is granted
  Future<bool> checkPermission() async {
    final isGranted = await Permission.microphone.isGranted;
    Logger.debug(
      'Checking microphone permission: ${isGranted ? 'GRANTED' : 'NOT GRANTED'}',
    );
    return isGranted;
  }

  /// Check if microphone permission is permanently denied
  /// On iOS, this might incorrectly return true on first request
  Future<bool> isPermanentlyDenied() async {
    final status = await Permission.microphone.status;
    final isPermanentlyDenied = status.isPermanentlyDenied;

    // On iOS, the permission might incorrectly report as permanently denied on first request
    // We'll check the platform to handle this special case
    final isIOS = Platform.isIOS;

    Logger.debug(
      'Checking if microphone permission is permanently denied: $isPermanentlyDenied (iOS: $isIOS)',
    );

    // On iOS, we'll attempt to request the permission regardless of the status
    // This ensures the system dialog is shown at least once
    if (isIOS && isPermanentlyDenied) {
      // Check if this is the first time we're requesting the permission
      // We can do this by attempting to request it and seeing if the system dialog appears
      Logger.debug(
        'iOS detected, will attempt to request permission despite permanentlyDenied status',
      );
      return false; // Return false to allow the permission request to proceed
    }

    return isPermanentlyDenied;
  }

  /// Start recording audio
  /// Returns the file path where the audio will be saved
  Future<String?> startRecording() async {
    if (_isRecording) {
      Logger.debug('Already recording');
      return _currentFilePath;
    }

    // Check permission
    final hasPermission = await checkPermission();
    if (!hasPermission) {
      final permissionStatus = await requestPermission();
      if (!permissionStatus.isGranted) {
        Logger.error('Microphone permission denied');
        return null;
      }
    }

    try {
      // Generate a unique file path
      final filePath = await _generateFilePath();
      _currentFilePath = filePath;

      // Configure the recorder
      await _record.start(
        RecordConfig(
          encoder: AudioEncoder.aacLc, // AAC is widely supported
          bitRate: 128000, // 128 kbps
          sampleRate: 44100, // 44.1 kHz
        ),
        path: filePath,
      );

      _isRecording = true;
      _recordingStartTime = DateTime.now();

      // Start amplitude monitoring
      _startAmplitudeMonitoring();

      Logger.debug('Recording started at $filePath');
      return filePath;
    } catch (e) {
      Logger.error('Failed to start recording', e);
      return null;
    }
  }

  /// Stop recording and return the result
  Future<AudioRecordingResult> stopRecording() async {
    if (!_isRecording) {
      return AudioRecordingResult.failure(errorMessage: 'Not recording');
    }

    try {
      // Stop the recording
      final path = await _record.stop();
      _isRecording = false;

      if (path == null) {
        return AudioRecordingResult.failure(
          errorMessage: 'Recording failed to save',
        );
      }

      // Calculate duration
      final duration =
          _recordingStartTime != null
              ? DateTime.now().difference(_recordingStartTime!)
              : Duration.zero;

      Logger.debug('Recording stopped. Duration: ${duration.inSeconds}s');

      return AudioRecordingResult.success(filePath: path, duration: duration);
    } catch (e) {
      Logger.error('Failed to stop recording', e);
      return AudioRecordingResult.failure(
        errorMessage: 'Failed to stop recording: $e',
      );
    } finally {
      _currentFilePath = null;
      _recordingStartTime = null;
    }
  }

  /// Cancel the current recording
  Future<bool> cancelRecording() async {
    if (!_isRecording) {
      return false;
    }

    try {
      await _record.stop();
      _isRecording = false;

      // Delete the file if it exists
      if (_currentFilePath != null) {
        final file = File(_currentFilePath!);
        if (await file.exists()) {
          await file.delete();
          Logger.debug('Cancelled recording deleted: $_currentFilePath');
        }
      }

      return true;
    } catch (e) {
      Logger.error('Failed to cancel recording', e);
      return false;
    } finally {
      _currentFilePath = null;
      _recordingStartTime = null;
    }
  }

  /// Generate a unique file path for the recording
  Future<String> _generateFilePath() async {
    final directory = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory('${directory.path}/recordings');

    // Create the recordings directory if it doesn't exist
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uniqueId = IdUtils.generateId();
    return '${recordingsDir.path}/recording_${timestamp}_$uniqueId.aac';
  }

  /// Start monitoring the amplitude of the recording
  void _startAmplitudeMonitoring() {
    Logger.debug('Starting amplitude monitoring...');

    // Counter for logging (to avoid excessive logs)
    int counter = 0;

    // Monitor amplitude every 100ms
    Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (!_isRecording) {
        Logger.debug('Stopping amplitude monitoring (not recording anymore)');
        timer.cancel();
        return;
      }

      try {
        final amplitude = await _record.getAmplitude();
        final normalized = _normalizeAmplitude(amplitude.current);

        // Log every 10th value (once per second) to avoid flooding the logs
        if (counter % 10 == 0) {
          Logger.debug(
            'Amplitude: ${amplitude.current} dB, Normalized: $normalized',
          );
        }
        counter++;

        _amplitudeStreamController.add(normalized);
      } catch (e) {
        Logger.error('Error getting amplitude', e);
      }
    });
  }

  /// Normalize the amplitude value to a range of 0.0 to 1.0
  /// Higher amplitude (louder sound) should result in higher values
  double _normalizeAmplitude(double amplitude) {
    // The amplitude is typically in decibels (negative values)
    // Convert to a 0.0 to 1.0 range for easier visualization

    // Typical values range from -160 (silence) to 0 (loudest)
    // Adjust the range to make the visualization more dynamic
    const double minDb = -60.0; // Adjusted from -160.0 to be more sensitive
    const double maxDb = 0.0;

    // Clamp the value to the expected range
    final clampedAmplitude = amplitude.clamp(minDb, maxDb);

    // Calculate the normalized value (0.0 to 1.0)
    // For audio in dB scale, higher values (closer to 0) mean louder sounds
    // We need to invert the normalized value to make higher amplitude
    // correspond to higher normalized values
    double normalizedValue = (clampedAmplitude - minDb) / (maxDb - minDb);

    // Invert the value so louder sounds (higher dB) have higher normalized values
    normalizedValue = 1.0 - normalizedValue;

    // Apply a curve to make the visualization more dynamic
    // This reduces the sensitivity for very quiet sounds
    normalizedValue = normalizedValue * normalizedValue;

    return normalizedValue;
  }
}
