import 'package:intl/intl.dart';
import 'package:noeji/models/chat_rate_limit.dart';
import 'package:noeji/services/preferences/chat_rate_limit_storage.dart';
import 'package:noeji/utils/logger.dart';

/// Service for enforcing rate limits on chat messages
class ChatRateLimiter {
  /// The rate limits to enforce
  final List<ChatRateLimit> rateLimits;

  /// In-memory cache of message timestamps for immediate rate limiting
  /// This helps prevent race conditions between checking and adding messages
  List<DateTime> _cachedMessageTimes = [];

  /// Lock to prevent concurrent modifications to the cache
  final _cacheLock = Object();

  /// Last time the cache was refreshed from storage
  DateTime _lastCacheRefresh = DateTime.now();

  /// How often to refresh the cache from storage (in milliseconds)
  static const int _cacheRefreshInterval = 500; // 500ms

  /// Creates a new ChatRateLimiter with the given rate limits
  /// Rate limits must be provided and cannot be null
  ChatRateLimiter({required this.rateLimits}) {
    // Initialize the cache when the rate limiter is created
    _initializeCache();
  }

  /// Initialize the in-memory cache from storage
  Future<void> _initializeCache() async {
    Logger.debug('Initializing rate limit cache');
    try {
      final log = await ChatRateLimitStorage.loadMessageLog();

      synchronized(_cacheLock, () {
        _cachedMessageTimes = List<DateTime>.from(log.messageTimes);
        _lastCacheRefresh = DateTime.now();
        Logger.debug(
          'Cache initialized with ${_cachedMessageTimes.length} messages',
        );
      });
    } catch (e) {
      Logger.error('Error initializing rate limit cache', e);
    }
  }

  /// Refresh the in-memory cache from storage if needed
  Future<void> _refreshCacheIfNeeded() async {
    final now = DateTime.now();
    final timeSinceLastRefresh =
        now.difference(_lastCacheRefresh).inMilliseconds;

    // Only refresh if it's been long enough since the last refresh
    if (timeSinceLastRefresh >= _cacheRefreshInterval) {
      Logger.debug(
        'Refreshing rate limit cache (last refresh: ${_formatDuration(now.difference(_lastCacheRefresh))} ago)',
      );

      try {
        final log = await ChatRateLimitStorage.loadMessageLog();

        synchronized(_cacheLock, () {
          _cachedMessageTimes = List<DateTime>.from(log.messageTimes);
          _lastCacheRefresh = now;
          Logger.debug(
            'Cache refreshed with ${_cachedMessageTimes.length} messages',
          );
        });
      } catch (e) {
        Logger.error('Error refreshing rate limit cache', e);
      }
    }
  }

  /// Add a message to the in-memory cache
  void _addMessageToCache(DateTime timestamp) {
    synchronized(_cacheLock, () {
      _cachedMessageTimes.add(timestamp);
      Logger.debug(
        'Added message to cache (now ${_cachedMessageTimes.length} messages)',
      );
    });
  }

  /// Synchronized execution of a function with a lock
  T synchronized<T>(Object lock, T Function() fn) {
    // Simple synchronization using a lock object
    // In a real implementation, this would use a proper mutex
    // but for our purposes, this is sufficient
    return fn();
  }

  /// Log a new message
  /// Returns true if the message was logged successfully
  Future<bool> logMessage() async {
    Logger.debug('===== LOGGING NEW CHAT MESSAGE =====');
    final timestamp = DateTime.now();
    Logger.debug('Message timestamp: ${timestamp.toIso8601String()}');

    // Log the current rate limits for reference
    _logCurrentRateLimits();

    // First check if we would exceed rate limits with this new message
    await _refreshCacheIfNeeded();

    // Add the message to the in-memory cache immediately
    // This ensures that subsequent rate limit checks will include this message
    // even before it's saved to persistent storage
    _addMessageToCache(timestamp);

    // Save to persistent storage
    final result = await ChatRateLimitStorage.addMessage(timestamp);
    Logger.debug('Message logged successfully: $result');
    Logger.debug('====================================');
    return result;
  }

  /// Check if any rate limit has been reached
  /// Returns a RateLimitResult with information about the limit status
  Future<RateLimitResult> checkRateLimit() async {
    try {
      Logger.debug('===== CHECKING RATE LIMITS =====');

      // First refresh the cache if needed
      await _refreshCacheIfNeeded();

      // Get message times from the in-memory cache
      final messageTimes = synchronized(_cacheLock, () {
        return List<DateTime>.from(_cachedMessageTimes);
      });

      Logger.debug('Found ${messageTimes.length} messages in cache');

      // If there are no messages, no limit has been reached
      if (messageTimes.isEmpty) {
        Logger.debug('No messages in cache, no rate limit reached');
        Logger.debug('================================');
        return RateLimitResult(isLimitReached: false);
      }

      // Check each rate limit
      DateTime? earliestNextAvailable;
      String? limitDescription;

      Logger.debug('Checking against ${rateLimits.length} rate limits:');

      for (final limit in rateLimits) {
        Logger.debug(
          '- Checking limit: max ${limit.maxMessages} per ${limit.description}',
        );

        // Count messages within the time window for this limit
        final windowStart = DateTime.now().subtract(
          Duration(seconds: limit.periodSeconds),
        );
        final messagesInWindow =
            messageTimes.where((time) => time.isAfter(windowStart)).length;

        Logger.debug('  Window start: ${windowStart.toIso8601String()}');
        Logger.debug(
          '  Messages in window: $messagesInWindow/${limit.maxMessages}',
        );

        final isReached = limit.isLimitReached(messageTimes);
        Logger.debug('  Limit reached: $isReached');

        if (isReached) {
          final nextAvailable = limit.getNextAvailableTime(messageTimes);

          if (nextAvailable != null) {
            final timeUntilAvailable = nextAvailable.difference(DateTime.now());
            Logger.debug(
              '  Next available: ${nextAvailable.toIso8601String()} (in ${_formatDuration(timeUntilAvailable)})',
            );

            // If this is the first limit reached or it's earlier than the current earliest
            if (earliestNextAvailable == null ||
                nextAvailable.isBefore(earliestNextAvailable)) {
              earliestNextAvailable = nextAvailable;
              limitDescription = limit.description;
              Logger.debug('  This is now the earliest next available time');
            }
          } else {
            Logger.debug('  Could not determine next available time');
          }
        }
      }

      // If any limit was reached, return the result
      if (earliestNextAvailable != null) {
        final formattedTime = _formatNextAvailableTime(earliestNextAvailable);
        final message =
            'You have reached the chat limit. Please wait until $formattedTime to chat again.';

        Logger.debug(
          'Rate limit reached. Next available: $formattedTime ($limitDescription limit)',
        );
        Logger.debug('================================');

        return RateLimitResult(
          isLimitReached: true,
          nextAvailableTime: earliestNextAvailable,
          message: message,
          limitDescription: limitDescription,
        );
      }

      // No limit was reached
      Logger.debug('No rate limit reached');
      Logger.debug('================================');
      return RateLimitResult(isLimitReached: false);
    } catch (e) {
      Logger.error('Error checking rate limit', e);
      // In case of error, don't block the user
      return RateLimitResult(isLimitReached: false);
    }
  }

  /// Format a duration for display in logs
  String _formatDuration(Duration duration) {
    if (duration.inSeconds < 60) {
      return '${duration.inSeconds}s';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    }
  }

  /// Format the next available time for display
  String _formatNextAvailableTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);

    // If it's less than a minute away
    if (difference.inMinutes < 1) {
      return 'a few seconds';
    }

    // If it's less than an hour away
    if (difference.inHours < 1) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'}';
    }

    // If it's today
    if (time.day == now.day &&
        time.month == now.month &&
        time.year == now.year) {
      return DateFormat('h:mm a').format(time);
    }

    // If it's tomorrow
    final tomorrow = now.add(const Duration(days: 1));
    if (time.day == tomorrow.day &&
        time.month == tomorrow.month &&
        time.year == tomorrow.year) {
      return 'tomorrow at ${DateFormat('h:mm a').format(time)}';
    }

    // Otherwise, show the full date and time
    return DateFormat('MMM d, h:mm a').format(time);
  }

  /// Clear all message logs
  /// This is useful for testing or resetting rate limits
  Future<bool> clearMessageLog() async {
    Logger.debug('===== CLEARING RATE LIMIT LOGS =====');

    // Clear the in-memory cache
    synchronized(_cacheLock, () {
      _cachedMessageTimes.clear();
      Logger.debug('In-memory cache cleared');
    });

    // Clear the persistent storage
    final result = await ChatRateLimitStorage.clearMessageLog();
    Logger.debug('Persistent storage cleared: $result');
    Logger.debug('===================================');

    return result;
  }

  /// Debug method to log the current rate limit status
  Future<void> debugRateLimitStatus() async {
    Logger.debug('===== DEBUG RATE LIMIT STATUS =====');

    // Log the current rate limits
    Logger.debug('Current rate limits:');
    for (final limit in rateLimits) {
      Logger.debug(
        '- ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds}s)',
      );
    }

    // Log the in-memory cache status
    final cachedTimes = synchronized(_cacheLock, () {
      return List<DateTime>.from(_cachedMessageTimes);
    });
    Logger.debug('In-memory cache: ${cachedTimes.length} messages');
    Logger.debug(
      'Last cache refresh: ${_formatDuration(DateTime.now().difference(_lastCacheRefresh))} ago',
    );

    // Load the message log from persistent storage
    final log = await ChatRateLimitStorage.loadMessageLog();
    final messageTimes = log.messageTimes;

    Logger.debug('Persistent storage: ${messageTimes.length} messages');

    // Check if cache and storage are in sync
    final cacheIds = cachedTimes.map((t) => t.toIso8601String()).toSet();
    final storageIds = messageTimes.map((t) => t.toIso8601String()).toSet();

    final onlyInCache = cacheIds.difference(storageIds);
    final onlyInStorage = storageIds.difference(cacheIds);

    if (onlyInCache.isNotEmpty) {
      Logger.debug(
        'WARNING: ${onlyInCache.length} messages in cache but not in storage!',
      );
    }

    if (onlyInStorage.isNotEmpty) {
      Logger.debug(
        'WARNING: ${onlyInStorage.length} messages in storage but not in cache!',
      );
    }

    // Use the in-memory cache for the rest of the debug info
    final combinedTimes =
        {...cacheIds, ...storageIds}.map((id) => DateTime.parse(id)).toList();
    Logger.debug('Total unique messages: ${combinedTimes.length}');

    if (combinedTimes.isNotEmpty) {
      // Sort message times (newest first)
      final sortedTimes = List<DateTime>.from(combinedTimes)
        ..sort((a, b) => b.compareTo(a));

      Logger.debug('Newest message: ${sortedTimes.first.toIso8601String()}');
      Logger.debug('Oldest message: ${sortedTimes.last.toIso8601String()}');

      // Check each rate limit
      for (final limit in rateLimits) {
        final windowStart = DateTime.now().subtract(
          Duration(seconds: limit.periodSeconds),
        );
        final messagesInWindow =
            combinedTimes.where((time) => time.isAfter(windowStart)).length;

        Logger.debug(
          '${limit.description} limit: $messagesInWindow/${limit.maxMessages} messages in the last ${limit.periodSeconds}s',
        );

        if (messagesInWindow >= limit.maxMessages) {
          final nextAvailable = limit.getNextAvailableTime(combinedTimes);
          if (nextAvailable != null) {
            final timeUntilAvailable = nextAvailable.difference(DateTime.now());
            Logger.debug(
              '  Limit reached! Next available: ${nextAvailable.toIso8601String()} (in ${_formatDuration(timeUntilAvailable)})',
            );
          } else {
            Logger.debug(
              '  Limit reached but could not determine next available time',
            );
          }
        } else {
          Logger.debug('  Limit not reached');
        }
      }
    } else {
      Logger.debug('No messages found');
    }

    Logger.debug('==================================');
  }

  /// Log the current rate limits for debugging
  void _logCurrentRateLimits() {
    Logger.debug('===== RATE LIMITS CONFIGURATION =====');
    for (final limit in rateLimits) {
      Logger.debug(
        'Rate limit: max ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds} seconds)',
      );
    }
    Logger.debug('====================================');
  }
}

/// Result of a rate limit check
class RateLimitResult {
  /// Whether any rate limit has been reached
  final bool isLimitReached;

  /// The next time a message can be sent (null if no limit reached)
  final DateTime? nextAvailableTime;

  /// Message to display to the user (null if no limit reached)
  final String? message;

  /// Description of the limit that was reached (null if no limit reached)
  final String? limitDescription;

  /// Creates a new RateLimitResult
  const RateLimitResult({
    required this.isLimitReached,
    this.nextAvailableTime,
    this.message,
    this.limitDescription,
  });
}
