# Chat Rate Limiting System

This directory contains the implementation of the chat rate limiting system for the Noeji app.

## Overview

The chat rate limiting system enforces limits on how many messages a user can send within specific time periods. The system is designed to:

1. Enforce multiple rate limits simultaneously (e.g., per minute, per day, per month)
2. Provide real-time feedback to users when they reach a limit
3. Automatically update the UI when a rate limit expires
4. Persist rate limit data across app restarts

## Components

### ChatRateLimitCache

A write-through cache that ensures both in-memory and persistent storage are always in sync. This prevents race conditions and ensures accurate rate limiting.

- Maintains an in-memory cache of message timestamps
- Automatically syncs with persistent storage
- Implements TTL cleanup to remove old messages
- Provides thread-safe access to the cache

### ChatRateLimitService

The main service that enforces rate limits and provides global state for rate limit enforcement.

- Checks if any rate limits have been reached
- Logs new messages and updates the cache
- Maintains a countdown timer for active rate limits
- Provides real-time updates to the UI when rate limits change

### ChatRateLimitServiceProvider

Provides access to the rate limit service throughout the app.

- Initializes the service at app startup
- Provides the current rate limit status to the UI
- Handles cleanup when the app is closed

## Rate Limit Flow

1. When the app starts, the rate limit service is initialized and the cache is loaded from persistent storage.
2. When the user opens the chat page, the rate limit status is checked.
3. If a rate limit is reached, the chat input is disabled and a warning message is shown.
4. When the user sends a message, it's logged in both the in-memory cache and persistent storage.
5. After each message, the rate limit status is recalculated.
6. If a rate limit is reached, a timer is started to track when the limit expires.
7. The warning message is dynamically updated with the remaining time.
8. When the timer expires, the rate limit status is checked again and the UI is updated.

## Migration

The `RateLimitMigration` utility handles migrating from the old rate limit system to the new one. This is called once at app startup to ensure a smooth transition.

## Usage

To check if a rate limit has been reached:

```dart
final rateLimitStatus = ref.watch(chatRateLimitStatusProvider);
if (rateLimitStatus != null && rateLimitStatus.isLimitReached) {
  // Rate limit reached, disable chat input
}
```

To log a new message:

```dart
final success = await ref.read(chatRateLimitServiceProvider).logMessage();
```

To force a rate limit check:

```dart
final result = await ref.read(checkChatRateLimitProvider.future);
```
