import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/chat/chat_rate_limit_cache.dart';
import 'package:noeji/services/chat/chat_rate_limit_service.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';

/// Provider for the chat rate limit cache
final chatRateLimitCacheProvider = Provider<ChatRateLimitCache>((ref) {
  final cache = ChatRateLimitCache();
  ref.onDispose(() {
    // No need to dispose the cache as it doesn't have any resources to clean up
  });
  return cache;
});

/// Provider for the chat rate limit service
final chatRateLimitServiceProvider = ChangeNotifierProvider<
  ChatRateLimitService
>((ref) {
  // Get the cache from the provider
  final cache = ref.watch(chatRateLimitCacheProvider);

  // Get the rate limits from the real-time provider for immediate updates when user tier changes
  final rateLimits = ref.watch(realtimeChatRateLimitsProvider);

  // Create the service with the cache and rate limits
  final service = ChatRateLimitService(rateLimits: rateLimits, cache: cache);

  // Clean up when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the current rate limit status
final chatRateLimitStatusProvider = Provider<RateLimitResult?>((ref) {
  final service = ref.watch(chatRateLimitServiceProvider);
  return service.currentResult;
});

/// Provider for checking the rate limit status
/// This is a convenience provider that triggers a rate limit check
/// and returns the result
final checkChatRateLimitProvider = FutureProvider<RateLimitResult>((ref) async {
  final service = ref.watch(chatRateLimitServiceProvider);
  return await service.checkRateLimit();
});
