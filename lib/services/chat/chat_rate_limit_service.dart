import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:noeji/models/chat_rate_limit.dart';
import 'package:noeji/services/chat/chat_rate_limit_cache.dart';
import 'package:noeji/utils/logger.dart';

/// Result of a rate limit check
class RateLimitResult {
  /// Whether the rate limit has been reached
  final bool isLimitReached;

  /// The next time when the user can send a message
  /// Only set if isLimitReached is true
  final DateTime? nextAvailableTime;

  /// A user-friendly message explaining the rate limit
  final String? message;

  /// Description of the limit that was reached (e.g. "minute", "day")
  final String? limitDescription;

  /// Seconds remaining until the rate limit expires
  final int? secondsRemaining;

  /// Creates a new RateLimitResult
  const RateLimitResult({
    required this.isLimitReached,
    this.nextAvailableTime,
    this.message,
    this.limitDescription,
    this.secondsRemaining,
  });

  /// Create a copy of this result with updated seconds remaining
  RateLimitResult copyWithSecondsRemaining(int seconds) {
    return RateLimitResult(
      isLimitReached: isLimitReached,
      nextAvailableTime: nextAvailableTime,
      message: message,
      limitDescription: limitDescription,
      secondsRemaining: seconds,
    );
  }

  /// Create a copy of this result with an updated message
  RateLimitResult copyWithMessage(String newMessage) {
    return RateLimitResult(
      isLimitReached: isLimitReached,
      nextAvailableTime: nextAvailableTime,
      message: newMessage,
      limitDescription: limitDescription,
      secondsRemaining: secondsRemaining,
    );
  }
}

/// Service for enforcing rate limits on chat messages
/// This service maintains a global state for rate limit enforcement
/// and provides methods for checking and updating rate limits
class ChatRateLimitService extends ChangeNotifier {
  /// The rate limits to enforce
  final List<ChatRateLimit> rateLimits;

  /// The cache for message timestamps
  final ChatRateLimitCache _cache;

  /// The current rate limit result
  RateLimitResult? _currentResult;

  /// Timer for updating the seconds remaining
  Timer? _countdownTimer;

  /// Creates a new ChatRateLimitService
  ChatRateLimitService({required this.rateLimits, ChatRateLimitCache? cache})
    : _cache = cache ?? ChatRateLimitCache() {
    // Initialize the service
    _initialize();
  }

  /// Initialize the service
  Future<void> _initialize() async {
    // Wait for the cache to be initialized
    await _cache.initialized;

    // Check rate limits immediately
    await checkRateLimit();
  }

  /// Get the current rate limit result
  RateLimitResult? get currentResult => _currentResult;

  /// Check if any rate limit has been reached
  /// Returns a RateLimitResult with information about the limit status
  Future<RateLimitResult> checkRateLimit() async {
    try {
      Logger.debug('===== CHECKING RATE LIMITS =====');

      // Get message times from the cache
      final messageTimes = _cache.messageTimes;
      Logger.debug('Found ${messageTimes.length} messages in cache');

      // If there are no messages, no limit has been reached
      if (messageTimes.isEmpty) {
        Logger.debug('No messages in cache, no rate limit reached');
        Logger.debug('================================');
        _updateResult(RateLimitResult(isLimitReached: false));
        return RateLimitResult(isLimitReached: false);
      }

      // Check each rate limit
      DateTime? earliestNextAvailable;
      String? limitDescription;

      Logger.debug('Checking against ${rateLimits.length} rate limits:');

      for (final limit in rateLimits) {
        Logger.debug(
          '- Checking limit: max ${limit.maxMessages} per ${limit.description}',
        );

        // Count messages within the time window for this limit
        final windowStart = DateTime.now().subtract(
          Duration(seconds: limit.periodSeconds),
        );
        final messagesInWindow =
            messageTimes.where((time) => time.isAfter(windowStart)).length;

        Logger.debug('  Window start: ${windowStart.toIso8601String()}');
        Logger.debug(
          '  Messages in window: $messagesInWindow/${limit.maxMessages}',
        );

        final isReached = limit.isLimitReached(messageTimes);
        Logger.debug('  Limit reached: $isReached');

        if (isReached) {
          final nextAvailable = limit.getNextAvailableTime(messageTimes);

          if (nextAvailable != null) {
            final timeUntilAvailable = nextAvailable.difference(DateTime.now());
            Logger.debug(
              '  Next available: ${nextAvailable.toIso8601String()} (in ${_formatDuration(timeUntilAvailable)})',
            );

            // If this is the first limit reached or it's earlier than the current earliest
            if (earliestNextAvailable == null ||
                nextAvailable.isBefore(earliestNextAvailable)) {
              earliestNextAvailable = nextAvailable;
              limitDescription = limit.description;
              Logger.debug('  This is now the earliest next available time');
            }
          } else {
            Logger.debug('  Could not determine next available time');
          }
        }
      }

      // If any limit was reached, return the result
      if (earliestNextAvailable != null) {
        final secondsRemaining =
            earliestNextAvailable.difference(DateTime.now()).inSeconds;
        final formattedTime = _formatNextAvailableTime(earliestNextAvailable);
        final message =
            'Running low on AI fuel! 🔋 You can start chatting again at $formattedTime. Hang tight! ⏳';

        Logger.debug(
          'Rate limit reached. Next available: $formattedTime ($limitDescription limit)',
        );
        Logger.debug('Seconds remaining: $secondsRemaining');
        Logger.debug('================================');

        final result = RateLimitResult(
          isLimitReached: true,
          nextAvailableTime: earliestNextAvailable,
          message: message,
          limitDescription: limitDescription,
          secondsRemaining: secondsRemaining,
        );

        _updateResult(result);
        return result;
      }

      // No limit was reached
      Logger.debug('No rate limit reached');
      Logger.debug('================================');

      _updateResult(RateLimitResult(isLimitReached: false));
      return RateLimitResult(isLimitReached: false);
    } catch (e) {
      Logger.error('Error checking rate limit', e);
      // In case of error, don't block the user
      _updateResult(RateLimitResult(isLimitReached: false));
      return RateLimitResult(isLimitReached: false);
    }
  }

  /// Log a new message
  /// Returns true if the message was logged successfully
  Future<bool> logMessage() async {
    Logger.debug('===== LOGGING NEW CHAT MESSAGE =====');
    final timestamp = DateTime.now();
    Logger.debug('Message timestamp: ${timestamp.toIso8601String()}');

    // Log the current rate limits for reference
    _logCurrentRateLimits();

    // Add the message to the cache
    final result = await _cache.addMessage(timestamp);

    // Check rate limits after adding the message
    if (result) {
      await checkRateLimit();
    }

    Logger.debug('Message logged successfully: $result');
    Logger.debug('====================================');
    return result;
  }

  /// Update the current rate limit result and start/stop the countdown timer as needed
  void _updateResult(RateLimitResult result) {
    // Cancel existing timer if there is one
    _countdownTimer?.cancel();

    // Update the result
    _currentResult = result;

    // If a limit is reached and we have a next available time, start a countdown timer
    if (result.isLimitReached &&
        result.nextAvailableTime != null &&
        result.secondsRemaining != null) {
      _startCountdownTimer(result);
    }

    // Notify listeners of the change
    notifyListeners();
  }

  /// Start a countdown timer to update the seconds remaining
  void _startCountdownTimer(RateLimitResult initialResult) {
    // Calculate initial seconds remaining
    int secondsRemaining =
        initialResult.secondsRemaining ??
        initialResult.nextAvailableTime?.difference(DateTime.now()).inSeconds ??
        0;

    // Start a timer that fires every second
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Decrement seconds remaining
      secondsRemaining--;

      if (secondsRemaining <= 0) {
        // Time's up, check rate limits again
        timer.cancel();
        checkRateLimit();
      } else {
        // Update the message with the new time
        // Calculate the next available time based on seconds remaining
        final nextAvailableTime = DateTime.now().add(
          Duration(seconds: secondsRemaining),
        );
        final formattedTime = _formatNextAvailableTime(nextAvailableTime);
        final message =
            'Running low on AI fuel! 🔋 You can start chatting again at $formattedTime. Hang tight! ⏳';

        // Update the result with the new seconds remaining and message
        _currentResult = _currentResult
            ?.copyWithSecondsRemaining(secondsRemaining)
            .copyWithMessage(message);

        // Notify listeners of the change
        notifyListeners();
      }
    });
  }

  /// Format a duration for display in logs
  String _formatDuration(Duration duration) {
    if (duration.inSeconds < 60) {
      return '${duration.inSeconds}s';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    }
  }

  /// Format the next available time for display to the user
  String _formatNextAvailableTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);

    // If it's less than a minute away
    if (difference.inMinutes < 1) {
      return 'a few seconds';
    }

    // If it's less than an hour away
    if (difference.inHours < 1) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'}';
    }

    // If it's today
    if (time.day == now.day &&
        time.month == now.month &&
        time.year == now.year) {
      return DateFormat('h:mm a').format(time);
    }

    // If it's tomorrow
    final tomorrow = now.add(const Duration(days: 1));
    if (time.day == tomorrow.day &&
        time.month == tomorrow.month &&
        time.year == tomorrow.year) {
      return 'tomorrow at ${DateFormat('h:mm a').format(time)}';
    }

    // If it's later this week (within 7 days)
    if (difference.inDays < 7) {
      final dayName = DateFormat('EEEE').format(time); // e.g., "Monday"
      final timeStr = DateFormat('h:mm a').format(time);
      return '$dayName at $timeStr';
    }

    // Otherwise, show the full date and time
    return DateFormat('MMM d, h:mm a').format(time);
  }

  /// Debug method to log the current rate limit status
  Future<void> debugRateLimitStatus() async {
    Logger.debug('===== DEBUG RATE LIMIT STATUS =====');

    // Log the current rate limits
    Logger.debug('Current rate limits:');
    for (final limit in rateLimits) {
      Logger.debug(
        '- ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds}s)',
      );
    }

    // Get message times from the cache
    final messageTimes = _cache.messageTimes;
    Logger.debug('Message cache: ${messageTimes.length} messages');

    // Check if there are any messages
    if (messageTimes.isNotEmpty) {
      // Sort message times (newest first)
      final sortedTimes = List<DateTime>.from(messageTimes)
        ..sort((a, b) => b.compareTo(a));

      Logger.debug('Newest message: ${sortedTimes.first.toIso8601String()}');
      Logger.debug('Oldest message: ${sortedTimes.last.toIso8601String()}');

      // Check each rate limit
      for (final limit in rateLimits) {
        final windowStart = DateTime.now().subtract(
          Duration(seconds: limit.periodSeconds),
        );
        final messagesInWindow =
            messageTimes.where((time) => time.isAfter(windowStart)).length;

        Logger.debug(
          '${limit.description} limit: $messagesInWindow/${limit.maxMessages} messages in the last ${limit.periodSeconds}s',
        );

        if (messagesInWindow >= limit.maxMessages) {
          final nextAvailable = limit.getNextAvailableTime(messageTimes);
          if (nextAvailable != null) {
            final timeUntilAvailable = nextAvailable.difference(DateTime.now());
            Logger.debug(
              '  Limit reached! Next available: ${nextAvailable.toIso8601String()} (in ${_formatDuration(timeUntilAvailable)})',
            );
          } else {
            Logger.debug(
              '  Limit reached but could not determine next available time',
            );
          }
        } else {
          Logger.debug('  Limit not reached');
        }
      }
    } else {
      Logger.debug('No messages found');
    }

    Logger.debug('==================================');
  }

  /// Clear all message logs
  /// This is useful for testing or resetting rate limits
  Future<bool> clearMessageLog() async {
    return await _cache.clearAll();
  }

  /// Log the current rate limits for debugging
  void _logCurrentRateLimits() {
    Logger.debug('===== RATE LIMITS CONFIGURATION =====');
    for (final limit in rateLimits) {
      Logger.debug(
        'Rate limit: max ${limit.maxMessages} messages per ${limit.description} (${limit.periodSeconds} seconds)',
      );
    }
    Logger.debug('====================================');
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }
}
