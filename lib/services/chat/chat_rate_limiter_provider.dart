import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/chat/chat_rate_limiter.dart';
import 'package:noeji/services/limits/app_limits_providers.dart';

/// Provider for the chat rate limiter
final chatRateLimiterProvider = Provider<ChatRateLimiter>((ref) {
  // Use real-time chat rate limits for immediate updates when user tier changes
  final rateLimits = ref.watch(realtimeChatRateLimitsProvider);
  return ChatRateLimiter(rateLimits: rateLimits);
});

/// Provider for the current rate limit status
final chatRateLimitStatusProvider = StateProvider<RateLimitResult?>(
  (ref) => null,
);

/// Provider for checking the rate limit status
final checkChatRateLimitProvider = FutureProvider<RateLimitResult>((ref) async {
  final rateLimiter = ref.watch(chatRateLimiterProvider);
  final result = await rateLimiter.checkRateLimit();

  // Update the status provider
  ref.read(chatRateLimitStatusProvider.notifier).state = result;

  return result;
});
