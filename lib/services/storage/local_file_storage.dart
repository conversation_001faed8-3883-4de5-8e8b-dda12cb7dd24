import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving data from local files
class LocalFileStorage {
  /// Get the application documents directory
  Future<Directory> get _appDocumentsDirectory async {
    return await getApplicationDocumentsDirectory();
  }

  /// Get the path for a file in the app documents directory
  Future<String> _getFilePath(String fileName) async {
    final directory = await _appDocumentsDirectory;
    return '${directory.path}/$fileName';
  }

  /// Check if a file exists
  Future<bool> fileExists(String fileName) async {
    try {
      final filePath = await _getFilePath(fileName);
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      Logger.error('Error checking if file exists: $fileName', e);
      return false;
    }
  }

  /// Read data from a file as a string
  Future<String?> readFile(String fileName) async {
    try {
      final filePath = await _getFilePath(fileName);
      final file = File(filePath);

      if (await file.exists()) {
        return await file.readAsString();
      } else {
        Logger.debug('File does not exist: $fileName');
        return null;
      }
    } catch (e) {
      Logger.error('Error reading file: $fileName', e);
      return null;
    }
  }

  /// Write data to a file
  Future<bool> writeFile(String fileName, String data) async {
    try {
      final filePath = await _getFilePath(fileName);
      final file = File(filePath);
      await file.writeAsString(data);
      return true;
    } catch (e) {
      Logger.error('Error writing to file: $fileName', e);
      return false;
    }
  }

  /// Delete a file
  Future<bool> deleteFile(String fileName) async {
    try {
      final filePath = await _getFilePath(fileName);
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();
        return true;
      } else {
        Logger.debug('File does not exist, nothing to delete: $fileName');
        return true;
      }
    } catch (e) {
      Logger.error('Error deleting file: $fileName', e);
      return false;
    }
  }

  /// Read JSON data from a file
  Future<Map<String, dynamic>?> readJson(String fileName) async {
    try {
      final jsonString = await readFile(fileName);
      if (jsonString == null) {
        return null;
      }
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      Logger.error('Error reading JSON from file: $fileName', e);
      return null;
    }
  }

  /// Write JSON data to a file
  Future<bool> writeJson(String fileName, Map<String, dynamic> data) async {
    try {
      final jsonString = jsonEncode(data);
      return await writeFile(fileName, jsonString);
    } catch (e) {
      Logger.error('Error writing JSON to file: $fileName', e);
      return false;
    }
  }
}

/// Service specifically for chat storage
class ChatFileStorage {
  final LocalFileStorage _storage;

  /// Constructor
  ChatFileStorage(this._storage);

  /// Get the file name for a chat
  String _getChatFileName(String ideabookId) {
    return 'chat_$ideabookId.json';
  }

  /// Check if a chat file exists
  Future<bool> chatExists(String ideabookId) async {
    final fileName = _getChatFileName(ideabookId);
    return await _storage.fileExists(fileName);
  }

  /// Read chat data from a file
  Future<Map<String, dynamic>?> readChat(String ideabookId) async {
    final fileName = _getChatFileName(ideabookId);
    return await _storage.readJson(fileName);
  }

  /// Write chat data to a file
  Future<bool> writeChat(String ideabookId, Map<String, dynamic> data) async {
    final fileName = _getChatFileName(ideabookId);
    return await _storage.writeJson(fileName, data);
  }

  /// Delete a chat file
  Future<bool> deleteChat(String ideabookId) async {
    final fileName = _getChatFileName(ideabookId);
    return await _storage.deleteFile(fileName);
  }
}
