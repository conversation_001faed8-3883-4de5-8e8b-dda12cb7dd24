import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/llm/firebase_ai_provider.dart';
import 'package:noeji/services/llm/gemini_chat_service.dart';
import 'package:noeji/services/llm/llm_service.dart';
import 'package:noeji/services/remote_config/remote_config_providers.dart';

/// Provider for the Firebase AI provider
final firebaseAiProvider = Provider<FirebaseAiProvider>((ref) {
  return FirebaseAiProvider();
});

/// Provider for the Gemini LLM service
final geminiServiceProvider = Provider<GeminiService>((ref) {
  // Get the Firebase AI provider
  final firebaseAi = ref.watch(firebaseAiProvider);

  // Get the Remote Config providers
  final llmPrompts = ref.watch(llmPromptsProvider);
  final llmModelConfig = ref.watch(llmModelConfigProvider);

  // Create the Gemini service with the Firebase AI provider and Remote Config
  final service = GeminiService(
    firebaseAiProvider: firebaseAi,
    llmPrompts: llmPrompts,
    llmModelConfig: llmModelConfig,
    // TODO: Add user tier detection here when subscription system is integrated
    userTier: null,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the Gemini Chat service (for chat, prompt suggestions, and note refresh)
final geminiChatServiceProvider = Provider<GeminiChatService>((ref) {
  // Get the Firebase AI provider
  final firebaseAi = ref.watch(firebaseAiProvider);

  // Get the Remote Config providers
  final llmModelConfig = ref.watch(llmModelConfigProvider);

  // Create the Gemini Chat service with the Firebase AI provider and Remote Config
  final service = GeminiChatService(
    firebaseAiProvider: firebaseAi,
    llmModelConfig: llmModelConfig,
    // TODO: Add user tier detection here when subscription system is integrated
    userTier: null,
  );

  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the LLM service (currently using Gemini)
final llmServiceProvider = Provider<LlmService>((ref) {
  return ref.watch(geminiServiceProvider);
});
