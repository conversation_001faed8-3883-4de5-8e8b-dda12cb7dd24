import 'package:firebase_ai/firebase_ai.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:noeji/services/llm/llm_service.dart';

/// Provider for Firebase AI Logic SDK models with different configurations
class FirebaseAiProvider {
  /// Get a basic generative model with custom configuration
  GenerativeModel _getGeminiModel({
    required String modelName,
    GenerationConfig? generationConfig,
    Content? systemInstruction,
  }) {
    return FirebaseAI.googleAI(
      appCheck: FirebaseAppCheck.instance,
    ).generativeModel(
      model: modelName,
      generationConfig: generationConfig,
      systemInstruction: systemInstruction,
    );
  }

  /// Get a model configured for audio transcription with structured JSON output
  GenerativeModel getAudioTranscriptionModel({
    required String modelName,
    required Schema responseSchema,
    GenerationConfig? baseGenerationConfig,
  }) {
    final config = GenerationConfig(
      responseMimeType: 'application/json',
      responseSchema: responseSchema,
      candidateCount: baseGenerationConfig?.candidateCount,
      maxOutputTokens: baseGenerationConfig?.maxOutputTokens,
      temperature: baseGenerationConfig?.temperature,
      topP: baseGenerationConfig?.topP,
      topK: baseGenerationConfig?.topK,
    );
    return _getGeminiModel(modelName: modelName, generationConfig: config);
  }

  /// Get a model configured for chat with optional structured JSON output
  GenerativeModel getChatModel({
    required String modelName,
    required Schema responseSchema,
    String? systemInstructionText,
    GenerationConfig? baseGenerationConfig,
  }) {
    Content? systemInstruction;
    if (systemInstructionText != null && systemInstructionText.isNotEmpty) {
      systemInstruction = Content.system(systemInstructionText);
    }

    final config = GenerationConfig(
      responseMimeType: 'application/json',
      responseSchema: responseSchema,
      candidateCount: baseGenerationConfig?.candidateCount,
      maxOutputTokens: baseGenerationConfig?.maxOutputTokens,
      temperature: baseGenerationConfig?.temperature,
      topP: baseGenerationConfig?.topP,
      topK: baseGenerationConfig?.topK,
    );

    return _getGeminiModel(
      modelName: modelName,
      generationConfig: config,
      systemInstruction: systemInstruction,
    );
  }

  /// Get response schema for different transcription use cases
  Schema getSchemaForTranscriptionUseCase(TranscriptionUseCase useCase) {
    switch (useCase) {
      case TranscriptionUseCase.newIdeabook:
        return Schema.object(
          properties: {
            'short_name': Schema.string(
              description: "Short name for the ideabook",
            ),
            'color': Schema.enumString(
              enumValues: [
                'red',
                'green',
                'blue',
                'yellow',
                'orange',
                'purple',
              ],
              description: "Suggested color for the ideabook",
            ),
          },
        );
      case TranscriptionUseCase.newIdea:
        return Schema.object(
          properties: {
            'idea': Schema.string(description: "The detailed idea content"),
            'short_title': Schema.string(
              description: "A short title for the idea",
            ),
          },
        );
      case TranscriptionUseCase.chatInput:
        return Schema.object(
          properties: {
            'transcript': Schema.string(
              description: "The transcribed audio content",
            ),
          },
        );
    }
  }

  /// Get response schema for chat responses
  Schema getChatResponseSchema() {
    return Schema.object(
      properties: {
        'response': Schema.string(
          description: "The chat bot's textual response",
        ),
        'user_prompt': Schema.string(description: "Summary of user's prompts"),
      },
    );
  }

  /// Get response schema for suggested prompts
  Schema getSuggestedPromptsResponseSchema() {
    return Schema.object(
      properties: {
        'prompts': Schema.array(
          items: Schema.object(
            properties: {
              'prompt': Schema.string(
                description: "A suggested prompt for the user",
              ),
            },
          ),
          description: "Array of suggested prompts",
        ),
      },
    );
  }
}
