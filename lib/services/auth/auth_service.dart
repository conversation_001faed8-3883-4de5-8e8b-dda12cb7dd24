import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/services/firebase/firebase_init.dart';
import 'package:noeji/services/auth/auth_provider_service.dart';

/// Service for handling authentication
class AuthService {
  /// Firebase Auth instance
  final firebase_auth.FirebaseAuth _firebaseAuth;

  /// Auth provider service for handling Google/Apple authentication
  final AuthProviderService _authProviderService;

  /// Constructor
  AuthService({
    firebase_auth.FirebaseAuth? firebaseAuth,
    AuthProviderService? authProviderService,
  }) : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
       _authProviderService = authProviderService ?? AuthProviderService();

  /// Stream of auth state changes
  /// Using idTokenChanges() instead of authStateChanges() to ensure we get updates
  /// when the user's ID token is refreshed, which happens periodically and on app restart
  Stream<firebase_auth.User?> get authStateChanges {
    Logger.debug('Initializing auth state changes listener');
    return _firebaseAuth.idTokenChanges();
  }

  /// Get the current user
  /// This is a synchronous method that returns the current user immediately
  firebase_auth.User? get currentUser {
    final user = _firebaseAuth.currentUser;
    Logger.debug(
      'Current user check: ${user != null ? 'Signed in as ${user.email}' : 'Not signed in'}',
    );
    return user;
  }

  /// Sign in with Google
  Future<firebase_auth.UserCredential?> signInWithGoogle() async {
    try {
      Logger.debug('Starting Google sign-in flow');

      // Get Google credential using shared service
      final authResult = await _authProviderService.getGoogleCredential();

      // If user canceled the sign-in flow
      if (authResult == null) {
        Logger.debug('Google sign-in was canceled by user');
        return null;
      }

      // Sign in to Firebase with the Google credential with App Check error handling
      return await _signInWithCredentialWithRetry(authResult.credential);
    } catch (e) {
      Logger.error('Error during Google sign-in', e);
      rethrow;
    }
  }

  /// Sign in with Apple
  Future<firebase_auth.UserCredential?> signInWithApple() async {
    try {
      Logger.debug('Starting Apple sign-in flow');

      // Get Apple credential using shared service
      final authResult = await _authProviderService.getAppleCredential();

      // If user canceled the sign-in flow
      if (authResult == null) {
        Logger.debug('Apple sign-in was canceled by user');
        return null;
      }

      // Sign in to Firebase with the Apple credential with App Check error handling
      final userCredential = await _signInWithCredentialWithRetry(authResult.credential);

      // Handle full name capture on first sign-in
      if (authResult.appleCredential != null) {
        await _updateUserProfileFromApple(userCredential.user, authResult.appleCredential!);
      }

      return userCredential;
    } catch (e) {
      Logger.error('Error during Apple sign-in', e);
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      final currentUserEmail = _firebaseAuth.currentUser?.email;
      Logger.debug('Signing out user: $currentUserEmail');

      // Sign out from Google using shared service
      await _authProviderService.signOutGoogle();

      // Note: Apple sign-in doesn't require explicit sign-out like Google
      // Signing out from Firebase is sufficient for Apple users

      // Sign out from Firebase
      try {
        await _firebaseAuth.signOut();
        Logger.debug('Firebase sign out successful');
      } catch (firebaseError) {
        Logger.error('Error during Firebase sign out', firebaseError);
        rethrow;
      }

      Logger.debug('Sign out completed successfully');

      // Refresh App Check token after sign out to prevent stale token issues
      // This helps ensure a fresh token is available for the next sign-in
      try {
        await refreshAppCheckToken();
        Logger.debug('App Check token refreshed after sign out');
      } catch (tokenError) {
        Logger.debug(
          'Could not refresh App Check token after sign out: $tokenError',
        );
        // Don't fail the sign out process if token refresh fails
      }
    } catch (e) {
      Logger.error('Error during sign out process', e);
      rethrow;
    }
  }

  /// Check if the user is signed in
  bool isSignedIn() {
    return currentUser != null;
  }

  /// Update user profile with information from Apple sign-in
  /// Apple provides name details ONLY on the first authorization
  Future<void> _updateUserProfileFromApple(
    firebase_auth.User? user,
    AuthorizationCredentialAppleID appleCredential,
  ) async {
    if (user == null) return;

    try {
      bool userProfileUpdated = false;

      // Update display name if it's not set and Apple provided name parts
      if ((user.displayName == null || user.displayName!.isEmpty) &&
          (appleCredential.givenName != null || appleCredential.familyName != null)) {
        final newDisplayName =
            '${appleCredential.givenName ?? ''} ${appleCredential.familyName ?? ''}'.trim();

        if (newDisplayName.isNotEmpty) {
          await user.updateDisplayName(newDisplayName);
          userProfileUpdated = true;
          Logger.debug('Updated Firebase display name: $newDisplayName');
        }
      }

      if (userProfileUpdated) {
        // Reload the user to ensure local User object reflects the changes
        await user.reload();
        Logger.debug('User profile updated and reloaded');
      }
    } catch (e) {
      Logger.error('Error updating user profile from Apple credentials', e);
      // Don't rethrow as this is not critical for the sign-in process
    }
  }

  /// Sign in to Firebase with credential and retry on App Check token errors
  Future<firebase_auth.UserCredential> _signInWithCredentialWithRetry(
    firebase_auth.AuthCredential credential,
  ) async {
    try {
      // Attempt the initial sign-in
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );

      Logger.debug('Firebase sign-in successful: ${userCredential.user?.uid}');
      Logger.debug('User email: ${userCredential.user?.email}');
      Logger.debug('User display name: ${userCredential.user?.displayName}');
      Logger.debug('User photo URL: ${userCredential.user?.photoURL}');

      return userCredential;
    } catch (authError) {
      // Check if this is an App Check token error
      if (isAppCheckTokenError(authError)) {
        Logger.debug(
          'Detected App Check token error during sign-in, attempting token refresh',
        );

        try {
          // Refresh the App Check token
          await refreshAppCheckToken();

          // Retry the Firebase sign-in with the refreshed token
          Logger.debug(
            'Retrying Firebase sign-in after App Check token refresh',
          );
          final userCredential = await _firebaseAuth.signInWithCredential(
            credential,
          );

          Logger.debug(
            'Firebase sign-in successful after token refresh: ${userCredential.user?.uid}',
          );
          return userCredential;
        } catch (retryError) {
          Logger.error(
            'Firebase sign-in failed even after App Check token refresh',
            retryError,
          );
          throw Exception(
            'Failed to authenticate after token refresh: ${retryError.toString()}',
          );
        }
      }

      // Re-throw the original error if it's not an App Check token error
      rethrow;
    }
  }
}
