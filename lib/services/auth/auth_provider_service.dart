import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/nonce_utils.dart';
import 'package:noeji/utils/crypto_utils.dart';

/// Result of authentication provider operations
class AuthProviderResult {
  final firebase_auth.AuthCredential credential;
  final AuthorizationCredentialAppleID? appleCredential;

  AuthProviderResult({required this.credential, this.appleCredential});
}

/// Shared service for handling authentication providers (Google, Apple)
/// Used by both AuthService for sign-in and AccountDeletionService for reauthentication
class AuthProviderService {
  final GoogleSignIn _googleSignIn;

  AuthProviderService({GoogleSignIn? googleSignIn})
    : _googleSignIn = googleSignIn ?? GoogleSignIn();

  /// Get authentication credential from Google
  /// Returns null if user cancels the flow
  Future<AuthProviderResult?> getGoogleCredential() async {
    try {
      Logger.debug('Starting Google authentication flow');

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        Logger.debug('Google authentication was cancelled by user');
        return null;
      }

      Logger.debug('Google sign-in successful: ${googleUser.email}');

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if we have valid tokens
      if (googleAuth.accessToken == null) {
        Logger.error('Google authentication failed: No access token received');
        throw Exception('No access token received from Google');
      }

      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      Logger.debug('Created Firebase credential from Google authentication');

      return AuthProviderResult(credential: credential);
    } catch (e) {
      Logger.error('Google authentication failed', e);
      throw Exception('Google authentication failed: $e');
    }
  }

  /// Get authentication credential from Apple
  /// Returns null if user cancels the flow
  Future<AuthProviderResult?> getAppleCredential() async {
    try {
      Logger.debug('Starting Apple authentication flow');

      // Generate raw nonce and its SHA-256 hash
      final rawNonce = NonceUtils.generateNonce();
      final hashedNonce = CryptoUtils.hashPasscode(rawNonce);

      Logger.debug('Generated nonce for Apple authentication');

      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: hashedNonce,
      );

      Logger.debug('Apple credential received');

      final credential = firebase_auth.OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        rawNonce: rawNonce,
        accessToken: appleCredential.authorizationCode,
      );

      Logger.debug('Created Firebase credential from Apple authentication');

      return AuthProviderResult(
        credential: credential,
        appleCredential: appleCredential,
      );
    } on SignInWithAppleAuthorizationException catch (e) {
      Logger.error('Apple Auth Exception: ${e.code} - ${e.message}');
      throw Exception('Apple Sign-In Error: ${e.message}');
    } catch (e) {
      Logger.error('Apple authentication failed', e);
      throw Exception('Apple authentication failed: $e');
    }
  }

  /// Get authentication credential based on provider ID
  /// Used for reauthentication when we know the user's provider
  Future<AuthProviderResult?> getCredentialForProvider(
    String providerId,
  ) async {
    switch (providerId) {
      case 'google.com':
        return await getGoogleCredential();
      case 'apple.com':
        return await getAppleCredential();
      default:
        throw Exception(
          'Unsupported provider: $providerId. Only Google and Apple are supported.',
        );
    }
  }

  /// Get friendly provider name for UI display
  static String getProviderFriendlyName(String providerId) {
    switch (providerId) {
      case 'google.com':
        return 'Google';
      case 'apple.com':
        return 'Apple';
      default:
        return 'your sign-in provider';
    }
  }

  /// Sign out from Google (Apple doesn't require explicit sign-out)
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
      Logger.debug('Google sign out successful');
    } catch (e) {
      Logger.error('Error during Google sign out', e);
      // Don't rethrow as this is not critical
    }
  }
}
