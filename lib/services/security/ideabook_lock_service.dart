import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/security/lock_operation.dart';
import 'package:noeji/models/security/lock_result.dart';
import 'package:noeji/models/security/lock_state.dart';
import 'package:noeji/repositories/ideabook_repository.dart';
import 'package:noeji/repositories/repository_providers.dart';
import 'package:noeji/services/security/passcode_storage.dart';
import 'package:noeji/utils/logger.dart';

/// Centralized service for managing ideabook lock operations
///
/// This service provides a clean interface for all lock-related operations
/// including validation, state management, and error handling.
///
/// Features:
/// - Centralized lock state management
/// - Comprehensive error handling with retry logic
/// - Extensible architecture for future lock features
/// - Proper separation of concerns
class IdeabookLockService {
  final IdeabookRepository _repository;

  /// Creates a new instance of the lock service
  IdeabookLockService({required IdeabookRepository repository})
    : _repository = repository;

  /// Validates if a lock operation can be performed
  ///
  /// Returns [LockResult] indicating success or failure with details
  Future<LockResult> validateLockOperation(LockOperation operation) async {
    try {
      Logger.debug(
        'IdeabookLockService: Validating lock operation: ${operation.type} for ideabook: ${operation.ideabookId}',
      );

      // Check if ideabook exists
      final ideabook = await _repository.getIdeabookById(operation.ideabookId);
      if (ideabook == null) {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'Ideabook not found',
          errorCode: LockErrorCode.ideabookNotFound,
        );
      }

      // Validate operation based on current state
      final currentState = LockState.fromIdeabook(ideabook);
      final validationResult = _validateOperationForState(
        operation,
        currentState,
      );

      if (!validationResult.isSuccess) {
        return validationResult;
      }

      // Check passcode requirements
      final passcodeResult = await _validatePasscodeRequirements(operation);
      if (!passcodeResult.isSuccess) {
        return passcodeResult;
      }

      Logger.debug('IdeabookLockService: Lock operation validation successful');
      return LockResult.success(
        operation: operation,
        message: 'Operation validated successfully',
      );
    } catch (e) {
      Logger.error('IdeabookLockService: Error validating lock operation', e);
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Validation failed: $e',
        errorCode: LockErrorCode.validationError,
      );
    }
  }

  /// Executes a lock operation with proper error handling and retry logic
  ///
  /// Returns [LockResult] with operation outcome
  Future<LockResult> executeLockOperation(LockOperation operation) async {
    try {
      Logger.debug(
        'IdeabookLockService: Executing lock operation: ${operation.type} for ideabook: ${operation.ideabookId}',
      );

      // First validate the operation
      final validationResult = await validateLockOperation(operation);
      if (!validationResult.isSuccess) {
        return validationResult;
      }

      // Execute the operation based on type
      switch (operation.type) {
        case LockOperationType.lock:
          return await _executeLockOperation(operation);
        case LockOperationType.unlock:
          return await _executeUnlockOperation(operation);
        case LockOperationType.toggle:
          return await _executeToggleOperation(operation);
      }
    } catch (e) {
      Logger.error('IdeabookLockService: Error executing lock operation', e);
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Operation failed: $e',
        errorCode: LockErrorCode.executionError,
      );
    }
  }

  /// Gets the current lock state for an ideabook
  ///
  /// Returns [LockState] or null if ideabook not found
  Future<LockState?> getLockState(String ideabookId) async {
    try {
      Logger.debug(
        'IdeabookLockService: Getting lock state for ideabook: $ideabookId',
      );

      final ideabook = await _repository.getIdeabookById(ideabookId);
      if (ideabook == null) {
        Logger.debug('IdeabookLockService: Ideabook not found: $ideabookId');
        return null;
      }

      return LockState.fromIdeabook(ideabook);
    } catch (e) {
      Logger.error('IdeabookLockService: Error getting lock state', e);
      return null;
    }
  }

  /// Checks if a passcode is required for the given operation
  ///
  /// Returns true if passcode validation is needed
  Future<bool> isPasscodeRequired(LockOperation operation) async {
    try {
      // For locking operations, passcode is always required
      if (operation.type == LockOperationType.lock) {
        return true;
      }

      // For unlocking, check if ideabook is currently locked
      if (operation.type == LockOperationType.unlock) {
        final lockState = await getLockState(operation.ideabookId);
        return lockState?.isLocked ?? false;
      }

      // For toggle operations, determine based on current state
      if (operation.type == LockOperationType.toggle) {
        // Toggle operations always require passcode validation
        // regardless of current state
        return true;
      }

      return false;
    } catch (e) {
      Logger.error(
        'IdeabookLockService: Error checking passcode requirement',
        e,
      );
      // Default to requiring passcode for security
      return true;
    }
  }

  /// Validates operation against current lock state
  LockResult _validateOperationForState(
    LockOperation operation,
    LockState currentState,
  ) {
    switch (operation.type) {
      case LockOperationType.lock:
        if (currentState.isLocked) {
          return LockResult.failure(
            operation: operation,
            errorMessage: 'Ideabook is already locked',
            errorCode: LockErrorCode.alreadyLocked,
          );
        }
        break;

      case LockOperationType.unlock:
        if (!currentState.isLocked) {
          return LockResult.failure(
            operation: operation,
            errorMessage: 'Ideabook is not locked',
            errorCode: LockErrorCode.notLocked,
          );
        }
        break;

      case LockOperationType.toggle:
        // Toggle is always valid
        break;
    }

    return LockResult.success(
      operation: operation,
      message: 'Operation valid for current state',
    );
  }

  /// Validates passcode requirements for the operation
  Future<LockResult> _validatePasscodeRequirements(
    LockOperation operation,
  ) async {
    try {
      final isPasscodeSet = await PasscodeStorage.isPasscodeSet();

      // If no passcode is set but operation requires one
      if (!isPasscodeSet && await isPasscodeRequired(operation)) {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'No passcode is set. Please set a passcode first.',
          errorCode: LockErrorCode.noPasscodeSet,
        );
      }

      // If passcode is provided, validate it
      if (operation.passcode != null) {
        final isValid = await PasscodeStorage.validatePasscode(
          operation.passcode!,
        );
        if (!isValid) {
          return LockResult.failure(
            operation: operation,
            errorMessage: 'Incorrect passcode',
            errorCode: LockErrorCode.invalidPasscode,
          );
        }
      }

      return LockResult.success(
        operation: operation,
        message: 'Passcode requirements validated',
      );
    } catch (e) {
      Logger.error(
        'IdeabookLockService: Error validating passcode requirements',
        e,
      );
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Passcode validation failed: $e',
        errorCode: LockErrorCode.validationError,
      );
    }
  }

  /// Executes a lock operation
  Future<LockResult> _executeLockOperation(LockOperation operation) async {
    try {
      final success = await _repository.toggleIdeabookLock(
        operation.ideabookId,
      );

      if (success) {
        Logger.debug(
          'IdeabookLockService: Successfully locked ideabook: ${operation.ideabookId}',
        );
        return LockResult.success(
          operation: operation,
          message: 'Ideabook locked successfully',
        );
      } else {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'Failed to lock ideabook',
          errorCode: LockErrorCode.executionError,
        );
      }
    } catch (e) {
      Logger.error('IdeabookLockService: Error executing lock operation', e);
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Lock operation failed: $e',
        errorCode: LockErrorCode.executionError,
      );
    }
  }

  /// Executes an unlock operation
  Future<LockResult> _executeUnlockOperation(LockOperation operation) async {
    try {
      final success = await _repository.toggleIdeabookLock(
        operation.ideabookId,
      );

      if (success) {
        Logger.debug(
          'IdeabookLockService: Successfully unlocked ideabook: ${operation.ideabookId}',
        );
        return LockResult.success(
          operation: operation,
          message: 'Ideabook unlocked successfully',
        );
      } else {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'Failed to unlock ideabook',
          errorCode: LockErrorCode.executionError,
        );
      }
    } catch (e) {
      Logger.error('IdeabookLockService: Error executing unlock operation', e);
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Unlock operation failed: $e',
        errorCode: LockErrorCode.executionError,
      );
    }
  }

  /// Executes a toggle operation
  Future<LockResult> _executeToggleOperation(LockOperation operation) async {
    try {
      // Get current state to determine what we're toggling to
      final currentState = await getLockState(operation.ideabookId);
      if (currentState == null) {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'Cannot determine current lock state',
          errorCode: LockErrorCode.ideabookNotFound,
        );
      }

      final success = await _repository.toggleIdeabookLock(
        operation.ideabookId,
      );

      if (success) {
        final newState = currentState.isLocked ? 'unlocked' : 'locked';
        Logger.debug(
          'IdeabookLockService: Successfully toggled ideabook: ${operation.ideabookId} to $newState',
        );
        return LockResult.success(
          operation: operation,
          message: 'Ideabook $newState successfully',
        );
      } else {
        return LockResult.failure(
          operation: operation,
          errorMessage: 'Failed to toggle ideabook lock state',
          errorCode: LockErrorCode.executionError,
        );
      }
    } catch (e) {
      Logger.error('IdeabookLockService: Error executing toggle operation', e);
      return LockResult.failure(
        operation: operation,
        errorMessage: 'Toggle operation failed: $e',
        errorCode: LockErrorCode.executionError,
      );
    }
  }

  /// Checks if an ideabook can be accessed without passcode validation
  ///
  /// Returns true if the ideabook is unlocked or no passcode is set
  Future<bool> canAccessWithoutPasscode(String ideabookId) async {
    try {
      final lockState = await getLockState(ideabookId);
      if (lockState == null) {
        return false; // Cannot access non-existent ideabook
      }

      // If not locked, can access
      if (!lockState.isLocked) {
        return true;
      }

      // If locked but no passcode is set, cannot access
      final isPasscodeSet = await PasscodeStorage.isPasscodeSet();
      return !isPasscodeSet;
    } catch (e) {
      Logger.error('IdeabookLockService: Error checking access permissions', e);
      return false; // Default to restricted access for security
    }
  }

  /// Creates a lock operation for the given parameters
  ///
  /// This is a convenience method for creating properly configured operations
  static LockOperation createOperation({
    required String ideabookId,
    required LockOperationType type,
    String? passcode,
    Map<String, dynamic>? metadata,
  }) {
    return LockOperation(
      ideabookId: ideabookId,
      type: type,
      passcode: passcode,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }
}

/// Provider for the ideabook lock service
final ideabookLockServiceProvider = Provider<IdeabookLockService>((ref) {
  final repository = ref.watch(ideabookRepositoryProvider);
  return IdeabookLockService(repository: repository);
});
