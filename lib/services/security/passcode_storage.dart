import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/firebase/firestore_listener_pool.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/ui/providers/passcode_provider.dart';
import 'package:noeji/utils/crypto_utils.dart';
import 'package:noeji/utils/logger.dart';

/// Enhanced service for storing and retrieving passcodes with improved error handling
///
/// This service provides secure passcode management with the following features:
/// - Secure hashing using industry-standard algorithms
/// - Comprehensive error handling and retry logic
/// - Real-time state synchronization
/// - Passcode format validation (exactly 6 digits)
/// - Proper separation of concerns
class PasscodeStorage {
  /// Firestore service instance
  static final FirestoreService _firestoreService = FirestoreService(
    auth: FirebaseAuth.instance,
  );

  /// Firestore listener pool instance
  static final FirestoreListenerPool _listenerPool = FirestoreListenerPool(
    _firestoreService,
  );

  /// Reference to the ProviderContainer for updating the cached state
  static ProviderContainer? _container;

  /// Required passcode length for security
  static const int requiredPasscodeLength = 6;

  /// Initialize the container reference
  static void initializeContainer(ProviderContainer container) {
    _container = container;
    Logger.debug('PasscodeStorage: Container initialized');
  }

  /// Ensure the passcode hash listener is set up
  /// This helps prevent race conditions when checking the passcode on app startup
  static Future<void> _ensurePasscodeHashListener() async {
    Logger.debug('Ensuring passcode hash listener is set up');

    try {
      // Get the stream from the listener pool, which will create a listener if one doesn't exist
      _listenerPool.getPasscodeHashStream();

      // Wait a short time to allow the listener to initialize
      await Future.delayed(const Duration(milliseconds: 300));

      Logger.debug('Passcode hash listener is now set up');
    } catch (e) {
      Logger.error('Error ensuring passcode hash listener', e);
    }
  }

  /// Update the cached passcode state
  static void _updateCachedState(bool isSet) {
    if (_container != null) {
      try {
        // Update the cached state
        _container!.read(passcodeSetCachedProvider.notifier).updateState(isSet);
        Logger.debug('PasscodeStorage: Updated cached state to isSet=$isSet');
      } catch (e) {
        Logger.error('PasscodeStorage: Error updating cached state', e);
      }
    }
  }

  /// Validates passcode format
  ///
  /// Returns null if valid, or error message if invalid
  static String? validatePasscodeStrength(String passcode) {
    if (passcode.isEmpty) {
      return 'Passcode cannot be empty';
    }

    if (passcode.length != requiredPasscodeLength) {
      return 'Passcode must be exactly $requiredPasscodeLength digits';
    }

    // Check if passcode contains only digits
    if (!RegExp(r'^\d+$').hasMatch(passcode)) {
      return 'Passcode must contain only numbers';
    }

    return null; // Valid passcode
  }

  /// Checks if a passcode is valid for use
  ///
  /// This validates format requirements (6 digits, numbers only)
  static bool isValidPasscode(String passcode) {
    return validatePasscodeStrength(passcode) == null;
  }

  /// Save the passcode to Firestore with validation
  /// The passcode is validated for format and then hashed before saving
  ///
  /// Returns true if successful, false otherwise
  /// Throws ArgumentError if passcode is invalid
  static Future<bool> savePasscode(String passcode) async {
    try {
      Logger.debug('PasscodeStorage: Validating and saving passcode');

      // Validate passcode strength first
      final validationError = validatePasscodeStrength(passcode);
      if (validationError != null) {
        Logger.error(
          'PasscodeStorage: Passcode validation failed: $validationError',
        );
        throw ArgumentError(validationError);
      }

      // Hash the passcode before saving
      final hashedPasscode = CryptoUtils.hashPasscode(passcode);
      Logger.debug('PasscodeStorage: Passcode hashed successfully');

      // Save the hashed passcode to Firestore
      final result = await _firestoreService.savePasscodeHash(hashedPasscode);

      if (result) {
        Logger.debug(
          'PasscodeStorage: Passcode hash saved successfully to Firestore',
        );
        // Update the cached state to indicate a passcode is set
        _updateCachedState(true);
      } else {
        Logger.error(
          'PasscodeStorage: Failed to save passcode hash to Firestore',
        );
      }

      return result;
    } catch (e) {
      if (e is ArgumentError) {
        // Re-throw validation errors
        rethrow;
      }
      Logger.error('PasscodeStorage: Error saving passcode', e);
      return false;
    }
  }

  /// Check if a passcode has been set in Firestore
  static Future<bool> isPasscodeSet() async {
    try {
      Logger.debug('PasscodeStorage: isPasscodeSet called');

      // Ensure the passcode hash listener is set up first
      await _ensurePasscodeHashListener();
      Logger.debug(
        'PasscodeStorage: Passcode hash listener ensured for isPasscodeSet check',
      );

      // Now check if the passcode is set
      Logger.debug('PasscodeStorage: Calling _firestoreService.isPasscodeSet');
      final result = await _firestoreService.isPasscodeSet();
      Logger.debug('PasscodeStorage: isPasscodeSet check result: $result');

      // Update the cached state
      _updateCachedState(result);
      Logger.debug('PasscodeStorage: Cached state updated');

      return result;
    } catch (e) {
      Logger.error('PasscodeStorage: Error checking if passcode is set', e);
      return false;
    }
  }

  /// Clear the saved passcode from Firestore
  static Future<bool> clearPasscode() async {
    try {
      // Save an empty string to effectively clear the passcode
      final result = await _firestoreService.savePasscodeHash('');

      Logger.debug('Passcode cleared from Firestore: $result');

      if (result) {
        // Update the cached state to indicate no passcode is set
        _updateCachedState(false);
      }

      return result;
    } catch (e) {
      Logger.error('Error clearing passcode', e);
      return false;
    }
  }

  /// Validate the given passcode against the stored hash in Firestore
  static Future<bool> validatePasscode(String passcode) async {
    try {
      Logger.debug(
        'PasscodeStorage: validatePasscode called with passcode length: ${passcode.length}',
      );

      // Ensure the passcode hash listener is set up first
      await _ensurePasscodeHashListener();
      Logger.debug('PasscodeStorage: Passcode hash listener ensured');

      // Now validate the passcode
      Logger.debug(
        'PasscodeStorage: Calling _firestoreService.validatePasscode',
      );
      final result = await _firestoreService.validatePasscode(passcode);
      Logger.debug('PasscodeStorage: validatePasscode result: $result');
      return result;
    } catch (e) {
      Logger.error('PasscodeStorage: Error validating passcode', e);
      return false;
    }
  }
}
