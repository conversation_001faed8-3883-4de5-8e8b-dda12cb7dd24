import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:noeji/services/subscription/user_tier_service.dart';
import 'package:noeji/utils/logger.dart';

/// Enum for different types of limits that can trigger paywall
enum LimitType { ideabooks, ideas, notes, chat }

/// Result of a paywall trigger action
class PaywallTriggerResult {
  final bool shouldShowPaywall;
  final String? errorMessage;
  final LimitType? limitType;

  const PaywallTriggerResult({
    required this.shouldShowPaywall,
    this.errorMessage,
    this.limitType,
  });

  /// Create a result that shows paywall
  factory PaywallTriggerResult.showPaywall(LimitType limitType) {
    return PaywallTriggerResult(shouldShowPaywall: true, limitType: limitType);
  }

  /// Create a result that shows error dialog
  factory PaywallTriggerResult.showError(String errorMessage) {
    return PaywallTriggerResult(
      shouldShowPaywall: false,
      errorMessage: errorMessage,
    );
  }
}

/// Service for determining when to show paywall vs error dialogs
class PaywallTriggerService {
  final UserTierService _userTierService;

  PaywallTriggerService({UserTierService? userTierService})
    : _userTierService = userTierService ?? UserTierService.instance;

  /// Determine if paywall should be shown for a limit reached scenario
  /// Returns PaywallTriggerResult indicating what action to take
  Future<PaywallTriggerResult> shouldShowPaywallForLimit({
    required LimitType limitType,
    required String errorMessage,
  }) async {
    try {
      // Check if user is pro
      final isProUser = await _userTierService.isProUser();

      Logger.debug(
        'PaywallTriggerService: Checking limit type: $limitType, isProUser: $isProUser',
      );

      // Pro users never see paywall, always see error dialogs
      if (isProUser) {
        Logger.debug('PaywallTriggerService: Pro user - showing error dialog');
        return PaywallTriggerResult.showError(errorMessage);
      }

      // Free users see paywall for specific limit types
      switch (limitType) {
        case LimitType.ideabooks:
        case LimitType.ideas:
        case LimitType.notes:
        case LimitType.chat:
          Logger.debug(
            'PaywallTriggerService: Free user hit paywall-eligible limit - showing paywall',
          );
          return PaywallTriggerResult.showPaywall(limitType);
      }
    } catch (e) {
      Logger.error(
        'PaywallTriggerService: Error determining paywall trigger',
        e,
      );
      // In case of error, default to showing error dialog to be safe
      return PaywallTriggerResult.showError(errorMessage);
    }
  }

  /// Show the RevenueCat paywall
  /// Returns true if user made a purchase, false if cancelled/error
  Future<bool> showPaywall() async {
    try {
      Logger.debug('PaywallTriggerService: Showing RevenueCat paywall');
      final result = await RevenueCatUI.presentPaywall();

      switch (result) {
        case PaywallResult.purchased:
          Logger.debug('PaywallTriggerService: User purchased subscription');
          return true;
        case PaywallResult.restored:
          Logger.debug('PaywallTriggerService: User restored subscription');
          return true;
        case PaywallResult.cancelled:
          Logger.debug('PaywallTriggerService: User cancelled paywall');
          return false;
        case PaywallResult.error:
          Logger.error('PaywallTriggerService: Error in paywall');
          return false;
        case PaywallResult.notPresented:
          Logger.error('PaywallTriggerService: Paywall was not presented');
          return false;
      }
    } catch (e) {
      Logger.error('PaywallTriggerService: Exception showing paywall', e);
      return false;
    }
  }
}
