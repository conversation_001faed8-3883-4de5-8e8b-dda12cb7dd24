import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/paywall/paywall_trigger_service.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';

/// Provider for the paywall trigger service
final paywallTriggerServiceProvider = Provider<PaywallTriggerService>((ref) {
  final userTierService = ref.watch(userTierServiceProvider);
  return PaywallTriggerService(userTierService: userTierService);
});
