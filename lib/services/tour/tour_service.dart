import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:noeji/services/preferences/onboarding_storage.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/showcase_tour.dart';
import 'package:noeji/utils/logger.dart';

/// Service for managing tour tooltips
class TourService {
  /// Default delay before showing a tooltip
  static const Duration defaultDelay = Duration(milliseconds: 500);

  /// Show a tour tooltip with the given key
  /// Returns true if the tooltip was shown, false otherwise
  static Future<bool> showTourTooltip({
    required BuildContext context,
    required GlobalKey key,
    required Future<bool> Function() shouldShowCheck,
    required Future<bool> Function() markAsShown,
    Duration delay = Duration.zero,
    String logPrefix = '',
  }) async {
    try {
      // Check if we should show the tour
      final shouldShow = await shouldShowCheck();
      Logger.debug('$logPrefix Should show tour: $shouldShow');

      if (shouldShow && context.mounted) {
        Logger.debug('$logPrefix Starting tour with key: $key');

        // Add delay if specified
        if (delay > Duration.zero) {
          Logger.debug(
            '$logPrefix Adding delay of ${delay.inMilliseconds}ms before showing tour',
          );
          await Future.delayed(delay);

          // Check if context is still mounted after the delay
          if (!context.mounted) {
            Logger.debug(
              '$logPrefix Context no longer mounted after delay, aborting tour',
            );
            return false;
          }
        }

        try {
          final showcaseContext = ShowCaseWidget.of(context);
          Logger.debug(
            '$logPrefix ShowCaseWidget context obtained successfully',
          );

          // Start the showcase
          showcaseContext.startShowCase([key]);
          Logger.debug('$logPrefix Tour started successfully');

          // Mark the tour as shown
          Logger.debug('$logPrefix Saving tour as shown');
          await markAsShown();

          return true;
        } catch (e) {
          Logger.error('$logPrefix Error starting tour', e);
          return false;
        }
      } else {
        Logger.debug(
          '$logPrefix Not showing tour. shouldShow: $shouldShow, mounted: ${context.mounted}',
        );
        return false;
      }
    } catch (e) {
      Logger.error('$logPrefix Error checking if tour should be shown', e);
      return false;
    }
  }

  /// Create a showcase widget with consistent styling
  static Widget createShowcaseWidget({
    required BuildContext context,
    required Widget child,
    required GlobalKey key,
    required String description,
    String? title,
    Color? overlayColor,
    Color? tooltipBackgroundColor,
    Color? textColor,
    EdgeInsets? tooltipPadding,
    TextStyle? descTextStyle,
    bool showArrow = true,
    Duration movingAnimationDuration = const Duration(milliseconds: 700),
  }) {
    final theme = NoejiTheme.colorsOf(context);

    return Showcase(
      key: key,
      title: title,
      description: description,
      overlayColor: overlayColor ?? Colors.black.withAlpha(179),
      tooltipBackgroundColor: tooltipBackgroundColor ?? theme.tooltipBackground,
      textColor: textColor ?? theme.textPrimary,
      tooltipPadding: tooltipPadding ?? const EdgeInsets.all(16.0),
      descTextStyle:
          descTextStyle ??
          GoogleFonts.afacad(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: theme.textPrimary,
          ),
      showArrow: showArrow,
      movingAnimationDuration: movingAnimationDuration,
      child: child,
    );
  }

  /// Show the ideabooks list tour
  static Future<bool> showIdeabooksListTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.newIdeabookButton,
      shouldShowCheck: () => OnboardingStorage.shouldShowIdeabooksListTour(),
      markAsShown: () => OnboardingStorage.saveIdeabooksListTourShown(),
      logPrefix: '[IdeabooksList Tour]',
    );
  }

  /// Show the first ideabook tour
  static Future<bool> showFirstIdeabookTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.firstIdeabookMicButton,
      shouldShowCheck: () => OnboardingStorage.shouldShowFirstIdeabookTour(),
      markAsShown: () => OnboardingStorage.saveFirstIdeabookTourShown(),
      logPrefix: '[FirstIdeabook Tour]',
    );
  }

  /// Show the ideas reordering tour
  static Future<bool> showIdeasReorderingTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.ideasReorderingTour,
      shouldShowCheck: () => OnboardingStorage.shouldShowIdeasReorderingTour(),
      markAsShown: () => OnboardingStorage.saveIdeasReorderingTourShown(),
      logPrefix: '[IdeasReordering Tour]',
      delay: defaultDelay,
    );
  }

  /// Show the save as note tour
  static Future<bool> showSaveAsNoteTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.saveAsNoteButton,
      shouldShowCheck: () => OnboardingStorage.shouldShowSaveAsNoteTour(),
      markAsShown: () => OnboardingStorage.saveSaveAsNoteTourShown(),
      logPrefix: '[SaveAsNote Tour]',
      delay: defaultDelay,
    );
  }

  /// Show the note detail tour
  static Future<bool> showNoteDetailTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.noteDetailRefreshButton,
      shouldShowCheck: () => OnboardingStorage.shouldShowNoteDetailTour(),
      markAsShown: () => OnboardingStorage.saveNoteDetailTourShown(),
      logPrefix: '[NoteDetail Tour]',
      delay: defaultDelay,
    );
  }

  /// Show the ideabook color system tour
  static Future<bool> showIdeabookColorSystemTour(BuildContext context) {
    return showTourTooltip(
      context: context,
      key: ShowcaseKeys.ideabookColorSystemTour,
      shouldShowCheck:
          () => OnboardingStorage.shouldShowIdeabookColorSystemTour(),
      markAsShown: () => OnboardingStorage.saveIdeabookColorSystemTourShown(),
      logPrefix: '[ColorSystem Tour]',
    );
  }

  /// Reset all tour shown statuses
  /// This is useful for testing
  static Future<bool> resetAllTours() {
    return OnboardingStorage.resetAllTours();
  }
}
