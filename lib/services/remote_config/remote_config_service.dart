import 'dart:convert';
import 'dart:async';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:noeji/utils/logger.dart';

/// Service for managing Firebase Remote Config
/// Handles fetching and real-time updates of LLM prompts and configurations
class RemoteConfigService {
  static RemoteConfigService? _instance;
  static RemoteConfigService get instance =>
      _instance ??= RemoteConfigService._();

  RemoteConfigService._();

  FirebaseRemoteConfig? _remoteConfig;
  StreamSubscription<RemoteConfigUpdate>? _configUpdateSubscription;
  final StreamController<void> _configUpdatedController =
      StreamController<void>.broadcast();

  /// Stream that emits when remote config is updated
  Stream<void> get onConfigUpdated => _configUpdatedController.stream;

  /// Initialize the Remote Config service
  Future<void> initialize() async {
    try {
      Logger.debug('Initializing Firebase Remote Config service...');

      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set config settings
      await _remoteConfig!.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval:
              kDebugMode
                  ? const Duration(
                    minutes: 1,
                  ) // 1 minute for debug mode for fast testing
                  : const Duration(hours: 1), // 1 hour for production
        ),
      );

      // Set default values for fallback
      await _setDefaultValues();

      // Fetch and activate the latest config
      await _fetchAndActivate();

      // Set up real-time config updates
      await _setupRealTimeUpdates();

      Logger.debug('Firebase Remote Config service initialized successfully');
    } catch (e) {
      Logger.error('Failed to initialize Firebase Remote Config service', e);
      rethrow;
    }
  }

  /// Set default values for all parameters
  Future<void> _setDefaultValues() async {
    final defaults = <String, dynamic>{
      // Audio transcription prompts (Pro tier)
      'pro_audio_transcription_new_ideabook_prompt':
          _getDefaultNewIdeabookPrompt(),
      'pro_audio_transcription_new_idea_prompt': _getDefaultNewIdeaPrompt(),
      'pro_audio_transcription_chat_input_prompt': _getDefaultChatInputPrompt(),

      // Chat and other prompts (Pro tier)
      'pro_chat_conversation_prompt': _getDefaultChatPrompt(),
      'pro_suggested_prompts_generation_prompt':
          _getDefaultSuggestedPromptsPrompt(),
      'pro_note_regeneration_prompt': _getDefaultNoteRegenerationPrompt(),

      // New chat system and user instructions
      'chat_system_instruction': _getDefaultChatSystemInstruction(),
      'chat_user_instruction': _getDefaultChatUserInstruction(),
      'chat_response_style': _getDefaultChatResponseStyle(),
      'default_chat_response_style': _getDefaultChatResponseStyleDefaults(),

      // Model names (Pro tier)
      'pro_audio_transcription_model_name': 'gemini-2.0-flash-lite',
      'pro_chat_model_name': 'gemini-2.0-flash',
      'pro_suggested_prompts_model_name': 'gemini-2.0-flash',
      'pro_note_regeneration_model_name': 'gemini-2.0-flash',

      // Generation configs (Pro tier)
      'pro_audio_transcription_generation_config':
          '{"response_mime_type": "application/json", "temperature": 0.1}',
      'pro_chat_generation_config': '{"temperature": 0.9}',
      'pro_suggested_prompts_generation_config': '{"temperature": 0.9}',
      'pro_note_regeneration_generation_config': '{"temperature": 0.9}',

      // Free tier limits
      'free_max_ideabooks': 3,
      'free_max_ideas_per_ideabook': 10,
      'free_max_notes_per_ideabook': 10,
      'free_chat_rate_limit_minute': 5,
      'free_chat_limit_daily': 10,
      'free_chat_limit_monthly': 100,
      'free_audio_recording_length_seconds': 60,
      'free_ideabook_name_max_words': 100,
      'free_idea_max_words': 1000,
      'free_chat_input_max_words': 200,

      // Pro tier limits
      'pro_max_ideabooks': 1000,
      'pro_max_ideas_per_ideabook': 100,
      'pro_max_notes_per_ideabook': 100,
      'pro_chat_rate_limit_minute': 20,
      'pro_chat_limit_daily': 1000,
      'pro_chat_limit_monthly': 10000,
      'pro_ideabook_name_max_words': 100,
      'pro_idea_max_words': 1000,
      'pro_chat_input_max_words': 200,
      'pro_audio_recording_length_seconds': 300,

      // Generative suggested prompts feature flags
      'free_enable_generative_suggested_prompts': false,
      'pro_enable_generative_suggested_prompts': false,

      // App behavior defaults
      'default_send_voice_chat_on_finish': false,
      'default_enable_generative_suggested_prompts': false,
      'default_auto_lock_ideabooks': false,
      'default_enable_long_press_to_record_new_idea': false,

      // Copy chat message as markdown defaults
      'default_enable_chat_copy_markdown': false,

      // Color palette settings
      'ideabook_color_palettes': json.encode({
        'default':
            'red:E76F51,orange:F4A261,yellow:E9C46A,green:8AB17D,blue:2A9D8F,purple:264653',
      }),
      'default_ideabook_color_palette_name': 'default',

      // Paywall settings
      'paywall_reminder_recurring_time_window_hours': 24,
    };

    await _remoteConfig!.setDefaults(defaults);
    Logger.debug('Set default values for Remote Config parameters');
  }

  /// Fetch and activate the latest config
  Future<void> _fetchAndActivate() async {
    try {
      Logger.debug('Fetching and activating Remote Config...');
      final activated = await _remoteConfig!.fetchAndActivate();
      Logger.debug('Remote Config fetch and activate result: $activated');
    } catch (e) {
      Logger.error('Failed to fetch and activate Remote Config', e);
      // Don't rethrow - we can continue with default values
    }
  }

  /// Set up real-time config updates
  Future<void> _setupRealTimeUpdates() async {
    try {
      Logger.debug('Setting up real-time Remote Config updates...');

      final stream = _remoteConfig!.onConfigUpdated;
      _configUpdateSubscription = stream.listen(
        (RemoteConfigUpdate event) async {
          Logger.debug(
            'Remote Config updated. Updated keys: ${event.updatedKeys}',
          );

          // Activate the new config
          await _remoteConfig!.activate();
          Logger.debug('Activated new Remote Config');

          // Notify listeners
          _configUpdatedController.add(null);
        },
        onError: (error) {
          Logger.error('Error in Remote Config real-time updates', error);
        },
      );

      Logger.debug('Real-time Remote Config updates set up successfully');
    } catch (e) {
      Logger.error('Failed to set up real-time Remote Config updates', e);
      // Don't rethrow - we can continue without real-time updates
    }
  }

  /// Get a string parameter value
  String getString(String key, {String? userTier}) {
    if (_remoteConfig == null) {
      Logger.debug(
        'Remote Config not initialized, returning default value for key: $key',
      );
      return _getDefaultValueForKey(key);
    }

    try {
      // For now, we'll use the same parameters for both tiers
      // In the future, you can implement tier-specific logic here
      final value = _remoteConfig!.getString(key);
      Logger.debug(
        'Retrieved Remote Config string for key "$key": ${value.substring(0, value.length.clamp(0, 100))}${value.length > 100 ? "..." : ""}',
      );
      return value;
    } catch (e) {
      Logger.error('Failed to get Remote Config string for key: $key', e);
      return _getDefaultValueForKey(key);
    }
  }

  /// Get a JSON parameter value as a Map
  Map<String, dynamic> getJson(String key, {String? userTier}) {
    if (_remoteConfig == null) {
      Logger.debug(
        'Remote Config not initialized, returning default JSON for key: $key',
      );
      return _getDefaultJsonForKey(key);
    }

    try {
      final jsonString = _remoteConfig!.getString(key);
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      Logger.debug('Retrieved Remote Config JSON for key "$key": $jsonMap');
      return jsonMap;
    } catch (e) {
      Logger.error('Failed to get Remote Config JSON for key: $key', e);
      return _getDefaultJsonForKey(key);
    }
  }

  /// Get an integer parameter value
  int getInt(String key, {String? userTier}) {
    if (_remoteConfig == null) {
      Logger.debug(
        'Remote Config not initialized, returning default int value for key: $key',
      );
      return _getDefaultIntForKey(key);
    }

    try {
      // For tier-specific parameters, try to get the tier-specific key first
      String actualKey = key;
      if (userTier != null &&
          !key.startsWith('free_') &&
          !key.startsWith('pro_')) {
        actualKey = '${userTier}_$key';
      }

      final value = _remoteConfig!.getInt(actualKey);
      Logger.debug('Retrieved Remote Config int for key "$actualKey": $value');
      return value;
    } catch (e) {
      Logger.error('Failed to get Remote Config int for key: $key', e);
      return _getDefaultIntForKey(key);
    }
  }

  /// Get a boolean parameter value
  bool getBool(String key, {String? userTier}) {
    if (_remoteConfig == null) {
      Logger.debug(
        'Remote Config not initialized, returning default bool value for key: $key',
      );
      return _getDefaultBoolForKey(key);
    }

    try {
      // For tier-specific parameters, try to get the tier-specific key first
      String actualKey = key;
      if (userTier != null &&
          !key.startsWith('free_') &&
          !key.startsWith('pro_')) {
        actualKey = '${userTier}_$key';
      }

      final value = _remoteConfig!.getBool(actualKey);
      Logger.debug('Retrieved Remote Config bool for key "$actualKey": $value');
      return value;
    } catch (e) {
      Logger.error('Failed to get Remote Config bool for key: $key', e);
      return _getDefaultBoolForKey(key);
    }
  }

  /// Get default value for a key (fallback when Remote Config fails)
  String _getDefaultValueForKey(String key) {
    switch (key) {
      case 'pro_audio_transcription_new_ideabook_prompt':
        return _getDefaultNewIdeabookPrompt();
      case 'pro_audio_transcription_new_idea_prompt':
        return _getDefaultNewIdeaPrompt();
      case 'pro_audio_transcription_chat_input_prompt':
        return _getDefaultChatInputPrompt();
      case 'pro_chat_conversation_prompt':
        return _getDefaultChatPrompt();
      case 'pro_suggested_prompts_generation_prompt':
        return _getDefaultSuggestedPromptsPrompt();
      case 'pro_note_regeneration_prompt':
        return _getDefaultNoteRegenerationPrompt();
      case 'chat_system_instruction':
        return _getDefaultChatSystemInstruction();
      case 'chat_user_instruction':
        return _getDefaultChatUserInstruction();
      case 'chat_response_style':
        return _getDefaultChatResponseStyle();
      case 'default_chat_response_style':
        return _getDefaultChatResponseStyleDefaults();
      case 'pro_audio_transcription_model_name':
        return 'gemini-2.0-flash-lite';
      case 'pro_chat_model_name':
        return 'gemini-2.0-flash';
      case 'pro_suggested_prompts_model_name':
        return 'gemini-2.0-flash';
      case 'pro_note_regeneration_model_name':
        return 'gemini-2.0-flash';
      case 'pro_audio_transcription_generation_config':
        return '{"response_mime_type": "application/json", "temperature": 0.1}';
      case 'pro_chat_generation_config':
        return '{"temperature": 0.9}';
      case 'pro_suggested_prompts_generation_config':
        return '{"temperature": 0.9}';
      case 'pro_note_regeneration_generation_config':
        return '{"temperature": 0.9}';
      case 'ideabook_color_palettes':
        return json.encode({
          'default':
              'red:E76F51,orange:F4A261,yellow:E9C46A,green:8AB17D,blue:2A9D8F,purple:264653',
        });
      case 'default_ideabook_color_palette_name':
        return 'default';
      default:
        Logger.debug('Unknown Remote Config key: $key');
        return '';
    }
  }

  /// Get default JSON value for a key
  Map<String, dynamic> _getDefaultJsonForKey(String key) {
    try {
      final jsonString = _getDefaultValueForKey(key);
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      Logger.error('Failed to parse default JSON for key: $key', e);
      return {};
    }
  }

  /// Get default integer value for a key (fallback when Remote Config fails)
  int _getDefaultIntForKey(String key) {
    switch (key) {
      // Free tier limits
      case 'free_max_ideabooks':
        return 3;
      case 'free_max_ideas_per_ideabook':
        return 10;
      case 'free_max_notes_per_ideabook':
        return 10;
      case 'free_chat_rate_limit_minute':
        return 5;
      case 'free_chat_limit_daily':
        return 10;
      case 'free_chat_limit_monthly':
        return 100;
      case 'free_audio_recording_length_seconds':
        return 60;
      case 'free_ideabook_name_max_words':
        return 100;
      case 'free_idea_max_words':
        return 1000;
      case 'free_chat_input_max_words':
        return 200;

      // Pro tier limits
      case 'pro_max_ideabooks':
        return 1000;
      case 'pro_max_ideas_per_ideabook':
        return 100;
      case 'pro_max_notes_per_ideabook':
        return 100;
      case 'pro_chat_rate_limit_minute':
        return 20;
      case 'pro_chat_limit_daily':
        return 1000;
      case 'pro_chat_limit_monthly':
        return 10000;
      case 'pro_ideabook_name_max_words':
        return 100;
      case 'pro_idea_max_words':
        return 1000;
      case 'pro_chat_input_max_words':
        return 200;
      case 'pro_audio_recording_length_seconds':
        return 300;
      case 'paywall_reminder_recurring_time_window_hours':
        return 24;

      default:
        Logger.debug('Unknown Remote Config int key: $key');
        return 0;
    }
  }

  /// Get default boolean value for a key (fallback when Remote Config fails)
  bool _getDefaultBoolForKey(String key) {
    switch (key) {
      // Generative suggested prompts feature flags
      case 'free_enable_generative_suggested_prompts':
        return false;
      case 'pro_enable_generative_suggested_prompts':
        return false;

      // App behavior defaults
      case 'default_send_voice_chat_on_finish':
        return false;
      case 'default_enable_generative_suggested_prompts':
        return false;
      case 'default_enable_chat_copy_markdown':
        return false;

      default:
        Logger.debug('Unknown Remote Config bool key: $key');
        return false;
    }
  }

  /// Dispose the service
  void dispose() {
    _configUpdateSubscription?.cancel();
    _configUpdatedController.close();
    Logger.debug('Remote Config service disposed');
  }

  // Default prompt methods (fallbacks)
  String _getDefaultNewIdeabookPrompt() {
    return '''Audio is the user's spoken prompt to create an ideabook.

Your task is to:
1. transcribe the audio and summarize the transcription into a short name that best describes the ideabook.
2. Select a color that best represents the ideabook:
- `red`: work, professional, etc.
- `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
- `blue`: personal growth, health, time management, etc.
- `yellow`: inspiration, diary, daily log, journal, etc.
- `orange`: travel, planning, birthday, parties, wedding, etc.
- `purple`: everything else but try to much other colors if possible.

Output in the major language in user's prompt.

If you are not able to clearly capture what the user is saying, use `Unnamed` for the `short_name` field. CRITICAL: do not make things up.

Output in JSON format:
{
  "short_name": "short name of the ideabook. <= 10 words. no repeat 'ideabook'. Use `Unnamed` if you're not confident about the transcription.",
  "color": "color of the ideabook. one of red, green, blue, yellow, orange, purple"
}''';
  }

  String _getDefaultNewIdeaPrompt() {
    return '''You're an ideabook - a book collecting user's ideas. Your task is to help the user capture ideas.

Your name as an ideabook: {{ideabook_name}}

The attached audio is the voice recording of a user's idea. Your task is to:
* Transcribe the audio
* Polish the transcription by correcting grammar errors, mispoken words, removing unnecessary / duplicated words, removing umms, ahhs, filler words etc.
* Also summarize the transcription into a catchy, creative short title that best describes the idea. MUST be <= 5 words.

Output in the major language in user's prompt.

If you are not able to clearly capture what the user is saying, use `Untitled` for the `short_title` field and `Unable to transcribe audio clearly` for the `idea` field. CRITICAL: do not make things up.

Output in JSON format:
{
  "short_title": "short title of the idea. <= 5 words. Use `Untitled` if you're not confident about the transcription.",
  "idea": "polished transcription of the idea. Use `Unable to transcribe audio clearly` if you're not confident about the transcription."
}''';
  }

  String _getDefaultChatInputPrompt() {
    return '''Transcribe the attached audio into text. The audio is a user's spoken input for a chat conversation.

Your task is to:
* Transcribe the audio accurately
* Correct obvious grammar errors and remove filler words (umm, ahh, etc.)
* Preserve the user's intended meaning and tone

Output in the major language in user's prompt.

If you cannot clearly understand the audio, output: "Unable to transcribe audio clearly"

Output in JSON format:
{
  "transcript": "the transcribed and polished text"
}''';
  }

  String _getDefaultChatPrompt() {
    return '''You're an ideabook - a book collecting user's ideas. Your task is to understand user's intention and answer their prompts based on ideas captured so far.

Your name as an ideabook: {{ideabook_name}}
Ideas captured:
(format: idea creation date | idea content)
"""
{{ideas_text}}
"""

User's prompt:
"""
{{user_content}}
"""

Previous chat history:
"""
{{past_chat_text}}
"""

There are several guidelines you MUST follow no matter what the user asks:
* Respond in a friendly, engaging, and conversational style like talking to a close friend
* Do not disclose details of yourself even if user asks (e.g. model name, etc). You are just an ideabook.
* The maximum length of your response is 5000 words no matter what the user asks.

Your response style:
* Prefer being brief and to the point unless it's more appropriate to be more detailed.
* Prefer responding based on the ideas captured.
* When responding with information outside the ideabook, friendly inform the user that and you may make mistake.
* Use emojis and emoticons in your response to make it more fun and engaging unless user asks not to.
* Respond in user's language of choice. If user doesn't specify, guess based on the user input language (e.g. if user input is a mix of English and Chinese, respond in Chinese or English or mix as appropriate).

Also summarize the current prompt and all of user's previous prompts into a short summary prompt in one sentence so that the summary prompt can be used as a new prompt for future conversation. Summarize from user's point of view, e.g. "I want to ..." instead of "The user wants to ...". Summarize in user's language (or mix of languages). IMPORTANT: The summary prompt should only include user's prompts, not ideas in the ideabook nor your response.

Output in JSON format:
{
  "user_prompt": "summary of user's prompts",
  "response": "your response to the user's prompt"
}''';
  }

  String _getDefaultSuggestedPromptsPrompt() {
    return '''You're an ideabook - a book collecting user's ideas. You help user answer questions about the ideas they captured via friendly and engaging conversations.

Your task is to suggest 5 prompts to start the conversation. The prompts are what the user might ask you about the ideas they captured.

Your name as an ideabook: {{ideabook_name}}
Ideas captured:
(format: idea creation date | idea content)
"""
{{formatted_ideas}}
"""

{{previous_suggestions_text}}

Requirements for the suggested prompts:
* If there is not idea captured yet, suggest prompts to brainstorm relevant ideas that are suitable to be added to this ideabook.
* Order by the likelihood user might click the prompt.
* The prompt MUST be 5 - 10 words.
* Use user's language (or mix of languages) used in the ideabook.
* Don't be afraid to suggest prompts that make surprising connections between ideas.

Examples of good prompts:
* What is the TL;DR of the ideabook?
* Summarize ideas into a list of action items
* Write a story based on the ideas
* Brainstorm more ideas

IMPORTANT: You must respond with ONLY a valid JSON object in the following format, with no additional text before or after:
{
  "prompts": [
    {"prompt": "suggested prompt #1"},
    {"prompt": "suggested prompt #2"},
    {"prompt": "suggested prompt #3"},
    {"prompt": "suggested prompt #4"},
    {"prompt": "suggested prompt #5"}
  ]
}''';
  }

  String _getDefaultNoteRegenerationPrompt() {
    return '''You're an ideabook - a book collecting user's ideas. Your task is to understand user's intention and answer their prompts based on ideas captured so far.

Your name as an ideabook: {{ideabook_name}}
Ideas captured:
(format: idea creation date | idea content)
{{ideas_text}}

User's prompt:
{{note_title}}

There are several guidelines you MUST follow no matter what the user asks:
* Respond in a friendly, engaging, and conversational style like talking to a close friend
* Do not disclose details of yourself even if user asks (e.g. model name, etc). You are just an ideabook.

Your response style:
* Prefer being brief and to the point unless it's more appropriate to be more detailed.
* Prefer responding based on the ideas captured but you can provide more information outside the ideabook if it makes the conversation more engaging.
* When responding with information outside the ideabook, friendly inform the user that and you may make mistake.
* Use emojis and emoticons in your response to make it more fun and engaging unless user asks not to.
* Respond in user's language of choice. If user doesn't specify, guess based on the user input language (e.g. if user input is a mix of English and Chinese, respond in Chinese or English or mix as appropriate).

Output in JSON format:
{
  "response": "your response to the user's prompt"
}''';
  }

  String _getDefaultChatSystemInstruction() {
    return '''You're an ideabook that captures ideas and answer prompts about them.

Name: {{ideabook_name}}
Ideas: (format: idea creation date | idea content)
"""
{{ideas_text}}
"""

Guidelines
* IMPORTANT: DO NOT reveal your details (e.g. model name) no matter what user asks
* Base your response on ideabook content
* When responding with information outside ideabook content, friendly inform the user that and you may make mistake
* Respond in user's desired language or follow user's input language or mix of languages
* Verbosity: {{verbosity}}
* Tone: {{tone}}
* Output format: {{output_format}}''';
  }

  String _getDefaultChatUserInstruction() {
    return '''You have two tasks:
1. Respond to user prompt; save your response in `response` field.
2. Summarize current and previous prompts into a new one-sentence prompt; save in `user_prompt` field.
* The summary can be used for future conversations
* From user's POV. E.g. "I want to ..." instead of "The user wants to ..."
* Use user's language or mix of languages
* Based on solely on user's prompts. Never include ideabook content or your responses.

User prompt:
"""
{{user_content}}
"""

Previous chat messages:
"""
{{previous_chat_messages}}
"""

MUST output in JSON format:
{
  "user_prompt": "summary of user's prompts",
  "response": "your response to the user's prompt"
}''';
  }

  String _getDefaultChatResponseStyle() {
    return json.encode({
      "verbosity": {
        "concise": {
          "prompt":
              "Concise: be brief and to the point. Max output: 1000 tokens",
          "description": "Be brief and to the point",
        },
        "balanced": {
          "prompt":
              "Balanced: provide a reasonable amount of detail without being overly lengthy or too short. Max output: 4000 tokens",
          "description":
              "Provide a reasonable amount of detail without being overly lengthy",
        },
        "verbose": {
          "prompt":
              "Verbose: provide detailed explanations, examples, and thorough information. Max output: 8000 tokens",
          "description":
              "Provide detailed explanations, examples, and thorough information",
        },
      },
      "tone": {
        "casual": {
          "prompt":
              "Casual: use a friendly, approachable, and conversational style. You can use contractions and more relaxed language. Use fun engaging emojis and emoticons unless user asks not to",
          "description": "Friendly, approachable and conversational",
        },
        "standard": {
          "prompt": "Standard: maintain a neutral, clear, and objective tone",
          "description": "Neutral, clear and objective",
        },
        "professional": {
          "prompt":
              "Professional: use formal language, complete sentences, and maintain a respectful, business-like demeanor",
          "description": "Formal, respectful and business-like",
        },
      },
      "output_format": {
        "free_form": {
          "prompt":
              "Free form: provide your response in well-structured paragraphs",
          "description": "Respond with well-structured paragraphs",
        },
        "structured": {
          "prompt":
              "Structured: present information using bullet points (for unordered items), numbered lists (for ordered steps or items), or a simple markdown table if the data is suitable for tabular presentation. Choose the most appropriate structured format for the content",
          "description":
              "Respond with bullet points, numbered lists and tables",
        },
      },
    });
  }

  String _getDefaultChatResponseStyleDefaults() {
    return json.encode({
      "verbosity": "balanced",
      "tone": "casual",
      "output_format": "free_form",
    });
  }
}
