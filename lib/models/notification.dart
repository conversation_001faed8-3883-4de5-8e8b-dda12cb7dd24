/// Base class for all notifications in the app
abstract class BaseNotification {
  /// The notification message to display
  final String message;

  /// When the notification was created
  final DateTime createdAt;

  /// Duration after which the notification should be automatically dismissed
  final Duration autoDismissDuration;

  /// Creates a new BaseNotification
  const BaseNotification({
    required this.message,
    required this.createdAt,
    this.autoDismissDuration = const Duration(seconds: 1, milliseconds: 500),
  });
}

/// Notification for ideabooks
class IdeabookNotification extends BaseNotification {
  /// The ID of the ideabook showing the notification
  final String ideabookId;

  /// Creates a new IdeabookNotification
  const IdeabookNotification({
    required this.ideabookId,
    required super.message,
    required super.createdAt,
    super.autoDismissDuration = const Duration(seconds: 1, milliseconds: 500),
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is IdeabookNotification &&
        other.ideabookId == ideabookId &&
        other.message == message &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => Object.hash(ideabookId, message, createdAt);
}

/// Notification for the bottom panel
class BottomPanelNotification extends BaseNotification {
  /// Creates a new BottomPanelNotification
  const BottomPanelNotification({
    required super.message,
    required super.createdAt,
    super.autoDismissDuration = const Duration(seconds: 1, milliseconds: 500),
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BottomPanelNotification &&
        other.message == message &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => Object.hash(message, createdAt);
}
