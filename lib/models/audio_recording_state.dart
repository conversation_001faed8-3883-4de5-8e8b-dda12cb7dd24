/// Enum representing the state of an audio recording
enum RecordingState {
  /// Not recording
  idle,

  /// Recording in progress
  recording,

  /// Recording paused (not implemented yet)
  paused,

  /// Recording completed
  completed,

  /// Recording failed
  failed,
}

/// Model representing the state of an audio recording
class AudioRecordingState {
  /// Current state of the recording
  final RecordingState state;

  /// Path to the recording file (if any)
  final String? filePath;

  /// Duration of the recording
  final Duration duration;

  /// Current amplitude (0.0 to 1.0)
  final double amplitude;

  /// Error message (if any)
  final String? errorMessage;

  /// Whether the recording is in countdown mode before auto-stopping
  final bool isInCountdown;

  /// Seconds remaining in the countdown (if isInCountdown is true)
  final int? countdownSeconds;

  /// Whether the recording was automatically stopped due to reaching the time limit
  final bool autoStopped;

  /// Creates a new AudioRecordingState
  const AudioRecordingState({
    required this.state,
    this.filePath,
    this.duration = Duration.zero,
    this.amplitude = 0.0,
    this.errorMessage,
    this.isInCountdown = false,
    this.countdownSeconds,
    this.autoStopped = false,
  });

  /// Initial state (not recording)
  factory AudioRecordingState.initial() {
    return const AudioRecordingState(
      state: RecordingState.idle,
    );
  }

  /// Recording state
  factory AudioRecordingState.recording({
    required String filePath,
    required Duration duration,
    required double amplitude,
  }) {
    return AudioRecordingState(
      state: RecordingState.recording,
      filePath: filePath,
      duration: duration,
      amplitude: amplitude,
    );
  }

  /// Completed state
  factory AudioRecordingState.completed({
    required String filePath,
    required Duration duration,
  }) {
    return AudioRecordingState(
      state: RecordingState.completed,
      filePath: filePath,
      duration: duration,
    );
  }

  /// Failed state
  factory AudioRecordingState.failed({
    required String errorMessage,
  }) {
    return AudioRecordingState(
      state: RecordingState.failed,
      errorMessage: errorMessage,
    );
  }

  /// Create a copy with updated values
  AudioRecordingState copyWith({
    RecordingState? state,
    String? filePath,
    Duration? duration,
    double? amplitude,
    String? errorMessage,
    bool? isInCountdown,
    int? countdownSeconds,
    bool? autoStopped,
  }) {
    return AudioRecordingState(
      state: state ?? this.state,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      amplitude: amplitude ?? this.amplitude,
      errorMessage: errorMessage ?? this.errorMessage,
      isInCountdown: isInCountdown ?? this.isInCountdown,
      countdownSeconds: countdownSeconds ?? this.countdownSeconds,
      autoStopped: autoStopped ?? this.autoStopped,
    );
  }

  /// Update the amplitude
  AudioRecordingState withAmplitude(double amplitude) {
    return copyWith(amplitude: amplitude);
  }

  /// Update the duration
  AudioRecordingState withDuration(Duration duration) {
    return copyWith(duration: duration);
  }

  /// Update the countdown state
  AudioRecordingState withCountdown({required bool isInCountdown, int? countdownSeconds}) {
    return copyWith(
      isInCountdown: isInCountdown,
      countdownSeconds: countdownSeconds,
    );
  }
}
