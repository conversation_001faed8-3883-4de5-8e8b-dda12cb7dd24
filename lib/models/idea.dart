import 'package:noeji/models/base_model.dart';

/// Represents an Idea, which is a user input captured by voice or text
class Idea extends BaseModel {
  /// Content of the idea (field 'c' in Firestore)
  final String content;

  /// Sort order value (field 's' in Firestore)
  /// This is only present if the idea has been manually reordered
  final double? sortOrder;

  /// Creates a new Idea instance
  const Idea({
    required super.id,
    required this.content,
    this.sortOrder,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Get the effective sort value for this idea
  /// If sortOrder is not null, use it, otherwise use createdAt timestamp
  double getEffectiveSortValue() {
    return sortOrder ?? createdAt.millisecondsSinceEpoch.toDouble();
  }

  @override
  Idea copyWith({
    String? id,
    String? content,
    double? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Idea(
      id: id ?? this.id,
      content: content ?? this.content,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Idea{id: $id, sortOrder: $sortOrder}';
  }
}
