import 'package:noeji/models/base_model.dart';

/// Represents a Note, which is an LLM answer saved by the user
class Note extends BaseModel {
  /// Title of the note (field 't' in Firestore)
  final String title;

  /// Content of the note (field 'c' in Firestore)
  final String content;

  /// Sort order value (field 's' in Firestore)
  /// This is only present if the note has been manually reordered
  final double? sortOrder;

  /// Creates a new Note instance
  const Note({
    required super.id,
    required this.title,
    required this.content,
    this.sortOrder,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Get the effective sort value for this note
  /// If sortOrder is not null, use it, otherwise use createdAt timestamp
  double getEffectiveSortValue() {
    return sortOrder ?? createdAt.millisecondsSinceEpoch.toDouble();
  }

  /// Creates a copy of this Note with the given fields replaced with new values
  @override
  Note copyWith({
    String? id,
    String? title,
    String? content,
    double? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Note{id: $id, title: $title, sortOrder: $sortOrder}';
  }
}
