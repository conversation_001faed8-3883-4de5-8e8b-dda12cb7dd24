import 'package:noeji/models/ideabook.dart';

/// Represents the current lock state of an ideabook
///
/// This class encapsulates all information about an ideabook's lock status
/// and provides methods for state transitions and validation.
class LockState {
  /// The ID of the ideabook this state belongs to
  final String ideabookId;

  /// Whether the ideabook is currently locked
  final bool isLocked;

  /// When this state was last updated
  final DateTime lastUpdated;

  /// Additional metadata about the lock state
  final Map<String, dynamic> metadata;

  /// Creates a new lock state
  const LockState({
    required this.ideabookId,
    required this.isLocked,
    required this.lastUpdated,
    this.metadata = const {},
  });

  /// Creates a lock state from an ideabook model
  factory LockState.fromIdeabook(Ideabook ideabook) {
    return LockState(
      ideabookId: ideabook.id,
      isLocked: ideabook.isLocked,
      lastUpdated: ideabook.updatedAt,
      metadata: {
        'ideabookName': ideabook.name,
        'ideabookColor': ideabook.color.name,
        'createdAt': ideabook.createdAt.toIso8601String(),
      },
    );
  }

  /// Creates an unlocked state
  factory LockState.unlocked({
    required String ideabookId,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return LockState(
      ideabookId: ideabookId,
      isLocked: false,
      lastUpdated: lastUpdated ?? DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a locked state
  factory LockState.locked({
    required String ideabookId,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return LockState(
      ideabookId: ideabookId,
      isLocked: true,
      lastUpdated: lastUpdated ?? DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a copy of this state with updated values
  LockState copyWith({
    String? ideabookId,
    bool? isLocked,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return LockState(
      ideabookId: ideabookId ?? this.ideabookId,
      isLocked: isLocked ?? this.isLocked,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Returns the opposite lock state
  LockState toggle() {
    return copyWith(isLocked: !isLocked, lastUpdated: DateTime.now());
  }

  /// Checks if this state allows access without passcode
  bool get allowsAccessWithoutPasscode => !isLocked;

  /// Gets a human-readable description of the current state
  String get description {
    return isLocked ? 'Locked' : 'Unlocked';
  }

  /// Gets the state as a simple string
  String get stateString => isLocked ? 'locked' : 'unlocked';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LockState &&
        other.ideabookId == ideabookId &&
        other.isLocked == isLocked &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(ideabookId, isLocked, lastUpdated);
  }

  @override
  String toString() {
    return 'LockState{ideabookId: $ideabookId, isLocked: $isLocked, lastUpdated: $lastUpdated}';
  }

  /// Converts this state to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'ideabookId': ideabookId,
      'isLocked': isLocked,
      'lastUpdated': lastUpdated.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates a lock state from a JSON map
  factory LockState.fromJson(Map<String, dynamic> json) {
    return LockState(
      ideabookId: json['ideabookId'] as String,
      isLocked: json['isLocked'] as bool,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }
}
