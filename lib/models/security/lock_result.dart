import 'package:noeji/models/security/lock_operation.dart';

/// Error codes for lock operations
enum LockErrorCode {
  /// The ideabook was not found
  ideabookNotFound,

  /// The ideabook is already locked
  alreadyLocked,

  /// The ideabook is not locked
  notLocked,

  /// No passcode has been set
  noPasscodeSet,

  /// The provided passcode is invalid
  invalidPasscode,

  /// Validation failed
  validationError,

  /// Operation execution failed
  executionError,

  /// Permission denied
  permissionDenied,

  /// Network or connectivity error
  networkError,

  /// Unknown error
  unknown;

  /// Gets a human-readable description of the error
  String get description {
    switch (this) {
      case LockErrorCode.ideabookNotFound:
        return 'Ideabook not found';
      case LockErrorCode.alreadyLocked:
        return 'Ideabook is already locked';
      case LockErrorCode.notLocked:
        return 'Ideabook is not locked';
      case LockErrorCode.noPasscodeSet:
        return 'No passcode has been set';
      case LockErrorCode.invalidPasscode:
        return 'Invalid passcode';
      case LockErrorCode.validationError:
        return 'Validation failed';
      case LockErrorCode.executionError:
        return 'Operation execution failed';
      case LockErrorCode.permissionDenied:
        return 'Permission denied';
      case LockErrorCode.networkError:
        return 'Network error';
      case LockErrorCode.unknown:
        return 'Unknown error';
    }
  }

  /// Gets the error code as a string
  String get code {
    return name.toUpperCase();
  }

  /// Creates a LockErrorCode from a string
  static LockErrorCode fromString(String value) {
    for (final code in LockErrorCode.values) {
      if (code.name.toLowerCase() == value.toLowerCase()) {
        return code;
      }
    }
    return LockErrorCode.unknown;
  }
}

/// Represents the result of a lock operation
///
/// This class provides a standardized way to handle the outcome
/// of lock operations, including success and failure cases.
class LockResult {
  /// Whether the operation was successful
  final bool isSuccess;

  /// The operation that was performed
  final LockOperation operation;

  /// Success message (if successful)
  final String? message;

  /// Error message (if failed)
  final String? errorMessage;

  /// Error code (if failed)
  final LockErrorCode? errorCode;

  /// When this result was created
  final DateTime timestamp;

  /// Additional metadata about the result
  final Map<String, dynamic> metadata;

  /// Creates a new lock result
  const LockResult({
    required this.isSuccess,
    required this.operation,
    this.message,
    this.errorMessage,
    this.errorCode,
    required this.timestamp,
    this.metadata = const {},
  });

  /// Creates a successful result
  factory LockResult.success({
    required LockOperation operation,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return LockResult(
      isSuccess: true,
      operation: operation,
      message: message ?? 'Operation completed successfully',
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a failure result
  factory LockResult.failure({
    required LockOperation operation,
    required String errorMessage,
    required LockErrorCode errorCode,
    Map<String, dynamic>? metadata,
  }) {
    return LockResult(
      isSuccess: false,
      operation: operation,
      errorMessage: errorMessage,
      errorCode: errorCode,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a copy of this result with updated values
  LockResult copyWith({
    bool? isSuccess,
    LockOperation? operation,
    String? message,
    String? errorMessage,
    LockErrorCode? errorCode,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return LockResult(
      isSuccess: isSuccess ?? this.isSuccess,
      operation: operation ?? this.operation,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Gets whether this is a failure result
  bool get isFailure => !isSuccess;

  /// Gets the display message (success message or error message)
  String get displayMessage {
    if (isSuccess) {
      return message ?? 'Operation completed successfully';
    } else {
      return errorMessage ?? 'Operation failed';
    }
  }

  /// Gets a detailed description including operation info
  String get detailedDescription {
    final operationDesc = operation.description;
    if (isSuccess) {
      return '$operationDesc: $displayMessage';
    } else {
      final errorCodeDesc = errorCode?.description ?? 'Unknown error';
      return '$operationDesc failed: $errorCodeDesc - $displayMessage';
    }
  }

  /// Checks if this result indicates a specific error
  bool hasError(LockErrorCode code) {
    return isFailure && errorCode == code;
  }

  /// Checks if this result indicates a passcode-related error
  bool get isPasscodeError {
    return hasError(LockErrorCode.noPasscodeSet) ||
        hasError(LockErrorCode.invalidPasscode);
  }

  /// Checks if this result indicates a state-related error
  bool get isStateError {
    return hasError(LockErrorCode.alreadyLocked) ||
        hasError(LockErrorCode.notLocked);
  }

  /// Checks if this result indicates a validation error
  bool get isValidationError {
    return hasError(LockErrorCode.validationError);
  }

  /// Checks if this result indicates an execution error
  bool get isExecutionError {
    return hasError(LockErrorCode.executionError);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LockResult &&
        other.isSuccess == isSuccess &&
        other.operation == operation &&
        other.message == message &&
        other.errorMessage == errorMessage &&
        other.errorCode == errorCode;
  }

  @override
  int get hashCode {
    return Object.hash(isSuccess, operation, message, errorMessage, errorCode);
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'LockResult.success{operation: ${operation.type.operationName}, message: $message}';
    } else {
      return 'LockResult.failure{operation: ${operation.type.operationName}, error: ${errorCode?.code}, message: $errorMessage}';
    }
  }

  /// Converts this result to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'operation': operation.toJson(),
      'message': message,
      'errorMessage': errorMessage,
      'errorCode': errorCode?.code,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates a lock result from a JSON map
  factory LockResult.fromJson(Map<String, dynamic> json) {
    return LockResult(
      isSuccess: json['isSuccess'] as bool,
      operation: LockOperation.fromJson(
        json['operation'] as Map<String, dynamic>,
      ),
      message: json['message'] as String?,
      errorMessage: json['errorMessage'] as String?,
      errorCode:
          json['errorCode'] != null
              ? LockErrorCode.fromString(json['errorCode'] as String)
              : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }
}
