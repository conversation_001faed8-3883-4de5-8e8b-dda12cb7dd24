/// Represents the type of lock operation to perform
enum LockOperationType {
  /// Lock an ideabook
  lock,
  
  /// Unlock an ideabook
  unlock,
  
  /// Toggle the lock state (lock if unlocked, unlock if locked)
  toggle;

  /// Gets a human-readable description of the operation
  String get description {
    switch (this) {
      case LockOperationType.lock:
        return 'Lock ideabook';
      case LockOperationType.unlock:
        return 'Unlock ideabook';
      case LockOperationType.toggle:
        return 'Toggle lock state';
    }
  }

  /// Gets the operation name as a string
  String get operationName {
    switch (this) {
      case LockOperationType.lock:
        return 'lock';
      case LockOperationType.unlock:
        return 'unlock';
      case LockOperationType.toggle:
        return 'toggle';
    }
  }

  /// Creates a LockOperationType from a string
  static LockOperationType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'lock':
        return LockOperationType.lock;
      case 'unlock':
        return LockOperationType.unlock;
      case 'toggle':
        return LockOperationType.toggle;
      default:
        throw ArgumentError('Invalid lock operation type: $value');
    }
  }
}

/// Represents a lock operation to be performed on an ideabook
/// 
/// This class encapsulates all the information needed to perform
/// a lock operation, including validation data and metadata.
class LockOperation {
  /// The ID of the ideabook to operate on
  final String ideabookId;

  /// The type of operation to perform
  final LockOperationType type;

  /// The passcode to use for validation (if required)
  final String? passcode;

  /// When this operation was created
  final DateTime timestamp;

  /// Additional metadata for the operation
  final Map<String, dynamic> metadata;

  /// Creates a new lock operation
  const LockOperation({
    required this.ideabookId,
    required this.type,
    this.passcode,
    required this.timestamp,
    this.metadata = const {},
  });

  /// Creates a lock operation
  factory LockOperation.lock({
    required String ideabookId,
    String? passcode,
    Map<String, dynamic>? metadata,
  }) {
    return LockOperation(
      ideabookId: ideabookId,
      type: LockOperationType.lock,
      passcode: passcode,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates an unlock operation
  factory LockOperation.unlock({
    required String ideabookId,
    String? passcode,
    Map<String, dynamic>? metadata,
  }) {
    return LockOperation(
      ideabookId: ideabookId,
      type: LockOperationType.unlock,
      passcode: passcode,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a toggle operation
  factory LockOperation.toggle({
    required String ideabookId,
    String? passcode,
    Map<String, dynamic>? metadata,
  }) {
    return LockOperation(
      ideabookId: ideabookId,
      type: LockOperationType.toggle,
      passcode: passcode,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );
  }

  /// Creates a copy of this operation with updated values
  LockOperation copyWith({
    String? ideabookId,
    LockOperationType? type,
    String? passcode,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return LockOperation(
      ideabookId: ideabookId ?? this.ideabookId,
      type: type ?? this.type,
      passcode: passcode ?? this.passcode,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Creates a copy with the passcode set
  LockOperation withPasscode(String passcode) {
    return copyWith(passcode: passcode);
  }

  /// Creates a copy with additional metadata
  LockOperation withMetadata(Map<String, dynamic> additionalMetadata) {
    final newMetadata = Map<String, dynamic>.from(metadata);
    newMetadata.addAll(additionalMetadata);
    return copyWith(metadata: newMetadata);
  }

  /// Checks if this operation requires passcode validation
  bool get requiresPasscode {
    // All operations typically require passcode validation
    // This can be overridden based on specific business logic
    return true;
  }

  /// Checks if a passcode is provided
  bool get hasPasscode => passcode != null && passcode!.isNotEmpty;

  /// Gets a unique identifier for this operation
  String get operationId {
    return '${type.operationName}_${ideabookId}_${timestamp.millisecondsSinceEpoch}';
  }

  /// Gets a human-readable description of this operation
  String get description {
    return '${type.description} for ideabook $ideabookId';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LockOperation &&
        other.ideabookId == ideabookId &&
        other.type == type &&
        other.passcode == passcode &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return Object.hash(ideabookId, type, passcode, timestamp);
  }

  @override
  String toString() {
    return 'LockOperation{ideabookId: $ideabookId, type: $type, hasPasscode: $hasPasscode, timestamp: $timestamp}';
  }

  /// Converts this operation to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'ideabookId': ideabookId,
      'type': type.operationName,
      'hasPasscode': hasPasscode,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Creates a lock operation from a JSON map
  factory LockOperation.fromJson(Map<String, dynamic> json) {
    return LockOperation(
      ideabookId: json['ideabookId'] as String,
      type: LockOperationType.fromString(json['type'] as String),
      // Note: We don't restore the actual passcode for security reasons
      passcode: null,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }
}
