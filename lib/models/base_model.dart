/// Base class for all models in the app
abstract class BaseModel {
  /// Unique identifier for the model
  final String id;

  /// When the model was created
  final DateTime createdAt;

  /// When the model was last updated
  final DateTime updatedAt;

  /// Creates a new BaseModel
  const BaseModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this model with the given fields replaced with new values
  BaseModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
  });

  @override
  String toString();
}
