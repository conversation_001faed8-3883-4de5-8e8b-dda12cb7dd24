import 'package:noeji/models/base_model.dart';
import 'package:noeji/utils/logger.dart';

/// Base repository interface for all repositories
abstract class BaseRepository<T extends BaseModel> {
  /// Get all entities
  Future<List<T>> getAll();

  /// Get an entity by ID
  Future<T?> getById(String id);

  /// Create a new entity
  Future<T> create(Map<String, dynamic> data);

  /// Update an existing entity
  Future<bool> update(T entity);

  /// Delete an entity
  Future<bool> delete(String id);

  /// Log repository operations with consistent format
  void logOperation(
    String operation, {
    String? id,
    String? details,
    Object? error,
  }) {
    final className = runtimeType.toString();

    if (error != null) {
      Logger.error(
        '$className: $operation failed${id != null ? ' for ID $id' : ''}${details != null ? ' - $details' : ''}',
        error,
      );
    } else {
      Logger.debug(
        '$className: $operation${id != null ? ' for ID $id' : ''}${details != null ? ' - $details' : ''}',
      );
    }
  }
}
