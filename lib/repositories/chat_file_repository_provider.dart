import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/repositories/chat_file_repository.dart';
import 'package:noeji/services/storage/storage_providers.dart';

/// Provider for the ChatFileRepository
final chatFileRepositoryProvider = Provider<ChatFileRepository>((ref) {
  final chatFileStorage = ref.watch(chatFileStorageProvider);
  return ChatFileRepository(chatFileStorage);
});
