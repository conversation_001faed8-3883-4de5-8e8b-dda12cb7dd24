import 'package:noeji/models/models.dart' as models;
import 'package:noeji/repositories/generic_repository.dart';
import 'package:noeji/services/firebase/firestore_service.dart';
import 'package:noeji/services/limits/repository_limits_helper.dart';
import 'package:noeji/utils/id_utils.dart';

/// Repository for managing Note data
class NoteRepository extends GenericRepository<models.Note> {
  /// Firestore service for cloud storage
  final FirestoreService _firestoreService;

  /// Repository limits helper for checking user limits
  final RepositoryLimitsHelper _limitsHelper;

  /// Creates a new NoteRepository
  NoteRepository({
    required FirestoreService firestoreService,
    required RepositoryLimitsHelper limitsHelper,
  }) : _firestoreService = firestoreService,
       _limitsHelper = limitsHelper,
       super();

  /// Generate a unique ID for a new entity
  String _generateId() {
    return IdUtils.generateId();
  }

  @override
  Future<List<models.Note>> getAll() async {
    logOperation('getAll');
    throw UnimplementedError('Use getNotesByIdeabookId instead');
  }

  /// Get all notes for an ideabook
  Future<List<models.Note>> getNotesByIdeabookId(String ideabookId) async {
    logOperation(
      'getNotesByIdeabookId',
      id: ideabookId,
      details: 'Using Firestore with listener',
    );
    try {
      // Use the listener to get all notes
      return await _firestoreService.listenToNotes(ideabookId).first;
    } catch (e) {
      logOperation('getNotesByIdeabookId', id: ideabookId, error: e);
      rethrow;
    }
  }

  /// Get a stream of all notes for an ideabook
  Stream<List<models.Note>> getNotesByIdeabookIdStream(String ideabookId) {
    logOperation(
      'getNotesByIdeabookIdStream',
      id: ideabookId,
      details: 'Using Firestore listener',
    );
    return _firestoreService.listenToNotes(ideabookId);
  }

  @override
  Future<models.Note?> getById(String id) async {
    logOperation('getById', id: id);
    throw UnimplementedError('Use getNoteById with ideabookId instead');
  }

  /// Get a note by ID
  Future<models.Note?> getNoteById(String ideabookId, String id) async {
    logOperation(
      'getNoteById',
      id: id,
      details: 'Using Firestore with listener',
    );
    try {
      // Use the listener to get the note
      return await _firestoreService.listenToNoteById(ideabookId, id).first;
    } catch (e) {
      logOperation('getNoteById', id: id, error: e);
      rethrow;
    }
  }

  /// Get a stream of a note by ID
  Stream<models.Note?> getNoteByIdStream(String ideabookId, String id) {
    logOperation(
      'getNoteByIdStream',
      id: id,
      details: 'Using Firestore listener',
    );
    return _firestoreService.listenToNoteById(ideabookId, id);
  }

  @override
  Future<models.Note> create(Map<String, dynamic> data) async {
    logOperation('create');
    try {
      final ideabookId = data['ideabookId'] as String;
      final title = data['title'] as String;
      final content = data['content'] as String;

      // Create the note model
      // Use placeholder DateTime for createdAt since it will be set by Firestore's serverTimestamp()
      final now = DateTime.now(); // Only used for updatedAt
      final note = models.Note(
        id: _generateId(),
        title: title,
        content: content,
        createdAt: now, // This will be replaced by server timestamp ('r' field)
        updatedAt: now,
      );

      return await _firestoreService.createNote(ideabookId, note);
    } catch (e) {
      logOperation('create', error: e);
      rethrow;
    }
  }

  /// Get the count of notes in an ideabook
  Future<int> getNoteCount(String ideabookId) async {
    logOperation('getNoteCount', id: ideabookId);
    try {
      final notes = await getNotesByIdeabookId(ideabookId);
      return notes.length;
    } catch (e) {
      logOperation('getNoteCount', id: ideabookId, error: e);
      rethrow;
    }
  }

  /// Check if an ideabook has reached the maximum number of notes
  Future<bool> isIdeabookFullOfNotes(String ideabookId) async {
    logOperation('isIdeabookFullOfNotes', id: ideabookId);
    try {
      final count = await getNoteCount(ideabookId);
      final maxNotes = await _limitsHelper.getMaxNotesPerIdeabook();
      return count >= maxNotes;
    } catch (e) {
      logOperation('isIdeabookFullOfNotes', id: ideabookId, error: e);
      // In case of error, assume not full to avoid blocking the user
      return false;
    }
  }

  /// Create a new note
  Future<models.Note> createNote({
    required String ideabookId,
    required String title,
    required String content,
  }) async {
    logOperation('createNote', details: 'For ideabook: $ideabookId');

    // Check if the ideabook is full of notes
    final isFull = await isIdeabookFullOfNotes(ideabookId);
    if (isFull) {
      await _limitsHelper.throwNotesLimitException();
    }

    return create({
      'ideabookId': ideabookId,
      'title': title,
      'content': content,
    });
  }

  @override
  Future<bool> update(models.Note entity) async {
    logOperation('update', id: entity.id);
    try {
      // Since we don't have ideabookId in the entity anymore, we need to get it from the context
      // This method should be called with the ideabookId parameter
      throw UnimplementedError('Use updateNote with ideabookId instead');
    } catch (e) {
      logOperation('update', id: entity.id, error: e);
      rethrow;
    }
  }

  /// Update an existing note
  Future<bool> updateNote(String ideabookId, models.Note note) async {
    logOperation('updateNote', id: note.id);
    try {
      return await _firestoreService.updateNote(ideabookId, note);
    } catch (e) {
      logOperation('updateNote', id: note.id, error: e);
      rethrow;
    }
  }

  @override
  Future<bool> delete(String id) async {
    logOperation('delete', id: id);
    throw UnimplementedError('Use deleteNote with ideabookId instead');
  }

  /// Delete a note
  Future<bool> deleteNote(String ideabookId, String id) async {
    logOperation('deleteNote', id: id);
    try {
      return await _firestoreService.deleteNote(ideabookId, id);
    } catch (e) {
      logOperation('deleteNote', id: id, error: e);
      rethrow;
    }
  }
}
