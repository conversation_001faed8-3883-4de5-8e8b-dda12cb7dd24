# Firebase App Check Debug Mode

This document explains how to use Firebase App Check in debug mode for local development and testing.

## Overview

Firebase App Check helps protect your backend resources from abuse by providing an attestation that requests are coming from your authentic app. However, during development and testing, especially on real devices, you need to use the debug provider to allow your app to access Firebase services.

## How App Check is Configured in Our App

In our app, we use the following configuration for App Check:

```dart
await FirebaseAppCheck.instance.activate(
  // For web, use reCAPTCHA v3
  webProvider: ReCaptchaV3Provider(recaptchaSiteKey),

  // For Android, use Play Integrity in production, Debug provider in development
  androidProvider: kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,

  // For iOS, use App Attest with fallback to Device Check
  appleProvider: kDebugMode ? AppleProvider.debug : AppleProvider.appAttest,
);
```

This configuration automatically uses the debug provider when the app is running in debug mode (`kDebugMode` is true).

## Running in Debug Mode on Real Devices

When running the app on a real device for development, you need to ensure that `kDebugMode` is set to true. Here's how to do it:

### For iOS Devices

When running on an iOS device, use the following command:

```bash
flutter run --debug -d "Your iOS Device Name"
```

For example:

```bash
flutter run --debug -d "Jianan's iPhone 13"
```

### For Android Devices

When running on an Android device, use the following command:

```bash
flutter run --debug -d "Your Android Device ID"
```

For example:

```bash
flutter run --debug -d "R5CT10GLXVJ"
```

## Registering the Debug Token

The first time you run the app with the debug provider, it will generate a debug token that you need to register in the Firebase console:

1. Look for the debug token in the console logs. For Android, it will look like:
   ```
   D DebugAppCheckProvider: Enter this debug secret into the allow list in
   the Firebase Console for your project: 123a4567-b89c-12d3-e456-789012345678
   ```

   For iOS, it will look like:
   ```
   Firebase App Check Debug Token:
   123a4567-b89c-12d3-e456-789012345678
   ```

2. Go to the Firebase console > App Check section
3. Click on the three dots menu for your app and select "Manage debug tokens"
4. Register the debug token you found in the logs
5. After registering the token, your app will be able to access Firebase services during development

## Important Notes

- Debug tokens should never be used in production builds
- Do not share your debug tokens with untrusted parties
- If a debug token is compromised, revoke it immediately in the Firebase console
- The debug provider is automatically used only when `kDebugMode` is true, which is the case when running with `flutter run --debug`
