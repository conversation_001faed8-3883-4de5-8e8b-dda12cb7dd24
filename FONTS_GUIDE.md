# Noeji App Fonts Guide

This guide explains how all fonts are bundled in the Noeji Flutter app and how to use them in RevenueCat Paywall Builder.

## Bundled Fonts

All fonts used in the Noeji app are now properly bundled as assets in the `assets/fonts/` directory and configured in `pubspec.yaml`. This ensures they work both in the app and in RevenueCat Paywall Builder.

### 1. Afacad (Primary Font)
- **Usage**: General text, body text, buttons, ideabook names, search hints
- **Files**: 
  - `Afacad-Regular.ttf` (weight: 400)
  - `Afacad-Medium.ttf` (weight: 500)
  - `Afacad-SemiBold.ttf` (weight: 600)
  - `Afacad-Bold.ttf` (weight: 700)
- **Font Family Name**: `Afacad`

### 2. Pacifico (App Name & Titles)
- **Usage**: App name "Noeji", titles, large headings
- **Files**: `Pacifico-Regular.ttf`
- **Font Family Name**: `Pacifico`

### 3. <PERSON><PERSON><PERSON> (NOEJI Logo)
- **Usage**: NOEJI logo text styling
- **Files**: `AllertaStencil-Regular.ttf`
- **Font Family Name**: `AllertaStencil`

### 4. Acme (Ideabook Titles & Passcode)
- **Usage**: Ideabook titles, passcode input
- **Files**: `Acme-Regular.ttf`
- **Font Family Name**: `Acme`

## Usage in Flutter Code

### Using Bundled Fonts Directly
```dart
Text(
  'Noeji',
  style: TextStyle(
    fontFamily: 'Pacifico',
    fontSize: 24,
  ),
)

Text(
  'Body text',
  style: TextStyle(
    fontFamily: 'Afacad',
    fontSize: 16,
    fontWeight: FontWeight.w500, // Uses Afacad-Medium.ttf
  ),
)

Text(
  'NOEJI LOGO',
  style: TextStyle(
    fontFamily: 'AllertaStencil',
    fontSize: 20,
  ),
)

Text(
  'Ideabook Title',
  style: TextStyle(
    fontFamily: 'Acme',
    fontSize: 18,
  ),
)
```

### Using Google Fonts (Still Available)
```dart
Text(
  'Text with Google Fonts',
  style: GoogleFonts.afacad(fontSize: 16),
)

Text(
  'Noeji',
  style: GoogleFonts.pacifico(fontSize: 24),
)
```

## Usage in RevenueCat Paywall Builder

Since all fonts are now bundled as assets, they will be available in RevenueCat Paywall Builder. Use the following font family names:

1. **Afacad** - For body text and general content
2. **Pacifico** - For the Noeji brand name and decorative text
3. **AllertaStencil** - For logo-style text
4. **Acme** - For titles and headings

## Font Weights Available

- **Afacad**: 400 (Regular), 500 (Medium), 600 (SemiBold), 700 (Bold)
- **Pacifico**: 400 (Regular only)
- **AllertaStencil**: 400 (Regular only)
- **Acme**: 400 (Regular only)

## Verification

Run the font test to verify all fonts are properly bundled:

```bash
flutter test test/font_test.dart
```

## Notes

- All font files are downloaded from official sources (Google Fonts repository)
- Font files are properly formatted TTF files
- Both bundled fonts and Google Fonts API calls will work
- RevenueCat Paywall Builder will have access to all bundled fonts
- The `assets/fonts/` directory is included in the assets configuration
