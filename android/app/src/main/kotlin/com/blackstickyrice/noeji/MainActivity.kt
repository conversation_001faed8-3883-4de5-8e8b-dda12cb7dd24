package com.rokabyte.noeji

import android.os.Bundle
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant

class MainActivity : FlutterFragmentActivity() {
    private val CHANNEL = "com.rokabyte.noeji/back_button"
    private var backButtonHandler: (() -> <PERSON><PERSON><PERSON>)? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setBackButtonHandler" -> {
                    val isInChatOrNotesTab = call.argument<Boolean>("isInChatOrNotesTab") ?: false
                    backButtonHandler = if (isInChatOrNotesTab) {
                        {
                            // Return true to indicate we've handled the back press
                            MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
                                .invokeMethod("onBackPressed", null)
                            true
                        }
                    } else {
                        null
                    }
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onBackPressed() {
        val handled = backButtonHandler?.invoke() ?: false
        if (!handled) {
            super.onBackPressed()
        }
    }
}
