# Overview

Product name: Noeji

Vision: Noeji is a mobile / web app that helps the user quickly capture ideas with their voice and turn them into complete stories, powered by Generative AI.

# Data models

## Ideabook

An ideabook is a collection of ideas.

Ideabook has the following associated data:
* Short name: Usually summarized by AI. User can edit it manually.
* Color: color of the ideabook for flexible categorization.
* Lock: whether the ideabook is locked.
* Created time: when the ideabook is created.

Ideabook is the container of the following data models:
* Idea - user input captured by voice recording or manual input text.
* Note - LLM answer saved by user as a note. Note has a title and text body.
* Chat - user can chat with <PERSON>M about their ideabook. Ideas will be used as context input to LLM conversation. Chat history is stored on user local device for fast loading.

### Color system

The Color system is a flexible way to allow user categorize their ideabooks.

Noeji supports 6 colors in total. When user creates a new ideabook, <PERSON>eji will use AI to determine the color based on the content of the ideabook. The color is determined by the following rules:
* `red`: work, professional, etc.
* `green`: family, friends, social, leisure, entertainment, sports, hobbies, etc.
* `blue`: personal growth, health, time management, etc.
* `yellow`: inspiration, diary, daily log, journal, etc.
* `orange`: travel, planning, birthday, parties, wedding, etc.
* `purple`: everything else but try to much other colors if possible.

But user can always change the color manually and decides how they want to categorize their ideabooks by color.

### Lock

An ideabook can be locked. User can click the lock button to lock / unlock an ideabook. If the user tries to lock a ideabook for the very first time of the entire app lifespan, they are prompted to enter and confirm a passcode. A single passcode is used to lock / unlock all ideabooks. Noeji doesn't support per-ideabook passcode.

When an ideabook is locked, the following actions require a passcode:
* Access the ideabook content, to see all the ideas, notes, chat in that ideabook.
* Unlock the ideabook.
* User needs to type in the passcode to lock an ideabook except for the very first time when they lock any ideabook (at that they're prompted to enter a new passcode).

The following actions do not require a passcode:
* Ideabook short name is still visible in the Ideabooks list page.
* Add new idea in Ideabooks list page.
* Change color.

In the Ideabooks list page, there is no obvious indication of which Ideabook is locked to respect user's privacy. The only visual indication is in the context menu, based on if the lock or unlock button is shown.

The passcode is not stored in cloud. An encrypted salt (a fingerprint of the passcode) is stored in Firestore to support multi devices passcode verification for locked ideabooks. Noeji backend does not have access to the passcode nor provides any way to retrieve it. User is clearly reminded that they are responsible for keeping the passcode safe and Noeji cannot help retrieve it if lost.

## Idea

An idea is a user input captured by voice recording or manual input text. An idea always belong to one of the Ideabooks.

Idea has the following associated data:
* Content: the transcribed, LLM-refined text, or user manually edited text.
* Creation timestamp: when this idea is created initially.

User can freely create, edit, delete any idea in any ideabook they own. User can manually reorder the ideas within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Note

A note is an LLM answer saved by user as a note. A note always belong to one of the Ideabooks.

Note has the following associated data:
* Title: the user's original prompt.
* Content: the LLM response.
* Creation timestamp: when this note is created initially.

User can freely save note, edit the title, delete any note in any ideabook they own. User can manually reorder the notes within an ideabook. The order is saved in Firestore for persistence over multiple devices.

## Chat

A chat is a conversation with LLM about an ideabook. A chat always belong to one of the Ideabooks. Chat is a list of messages between user and LLM. Chat is saved locally on user's device only. Chat is not synced to cloud. Chat is not preserved when user switch device or reinstall the app.

# UI Features

## Welcome page

This is the first page user sees when they open the app for the first time. The purpose of the welcome page is to ask user sign in with their social account before they can continue to use the app. Google authentication and Apple authentication (a.k.a Sign in with Apple) are the only supported authentication methods for now.

The welcome page has the following elements:
* Logo: Noeji in Pacifico font.
* Slogan: "Ideas Spoken. Stories Born". in signika google font.
* Sign in to Continue, in Afacad font.
* Only 1 sign option: "Sign in with Google"
* Link to terms of service and privacy policy. User is clearly reminded that by signing in, they agree to the terms of service and privacy policy.

## Ideabook list page

This is the main page of the app.

A top bar:
* To the left: The logo "Noeji"
* To the right: a user profile picture as a button to open a menu.

The menu supports the following actions:
* Upgrade to Noeji PRO
* Settings
* Filter by color
* Sort by created time: asc / desc
* Group by color

A list of ideabooks. Each ideabook in the list shows several elements:
* Color: color of the ideabook for flexible categorization.
* Short name: Usually summarized by AI. User can edit it manually.
* A microphone button: click it will trigger recording of new idea that will be saved into the ideabook.
* Swipe to left will show more context buttons.
* Swipe to right will show color selection.

The expanded context buttons:
* The same microphone button.
* A pencil button to edit the ideabook short name.
* A lock / unlock button lock / unlock the ideabook, depending on if the ideabook is locked already.
* A trash bin button to delete the ideabook (triggers a confirmation dialog before deletion).

The color selection:
* A list of colors to choose from.

At the bottom of the ideabook list page is the "New Ideabook" button. Click it will trigger voice recording to create new ideabook.

## Settings page

The settings page is accessible from the menu in the top bar of the ideabooks list page.

The settings page has the following elements:
* A section for user profile:
  * User's name and email.
  * A button to sign out.
* A section for app settings:
  * A button to change the app passcode (if user already set one).
  * A button to change the app theme (light / dark).

## Ideabook detail page

The top bar shows the ideabook short name (aligned to right) and a back arrow (aligned to left) to navigate back to ideabooks list page.

User can click the ideabook short name to edit it in place.

The detail page is split to three tabs. The tab bar is fixed at the bottom.
* Ideas
* Chat
* Notes

The top bar and tab bar color is the same color as the ideabook color.

### Ideas tab

Shows the list of ideas. Swipe to shows context menu:
* A delete button to delete a idea.

The idea can be edited in place by tapping on the idea row. The edit mode support keyboard type input and voice input. User has the option to save or discard the edit.

The ideas can be reordered by drag-n-drop. When long click on an idea, it enters drag-n-drop mode.

There is a new idea button at the bottom of the page. It triggers the voice recording to create new idea.

### Chat tab

Look and feel like a common LLM chat UI. At bottom is a chat box ("Chat your ideabook"). With a mic icon to trigger voice chat. Messages are ordered from top to bottom. User message is shown aligned to the right with light gray background color. LLM response takes the full width of the screen without background.

User has the option to save a chat response as note, copy a chat response to clipboard, and clear chat history with a clear confirmation dialog before clearing. Cleared chat history is deleted perpermanently and can't be restored.

### Notes tab

A list of all saved notes. Each note shows a snippet of the note content. Swipe to shows context menu:
* A delete button to delete a note.

Click on the Note to open the Note detail page.

The note detail page:
* The note title (user prompt) in gray background color. similar buble style as the user message in the chat tab. but takes the full width. User can tap on the buble to edit the note title in place. It suports voice and text input with options to save or discard the edit.
* The note text (LLM response) in Markdown format.
* Buttons area:
  ** Aligned to left: a "refresh" button to regenerate the note text by sending the note title (user prompt) to LLM again.
  ** Aligned to left: a "copy" button to copy the note text to clipboard.
  ** Aligned to right: a "delete" button to delete the entire note.

# Generative AI integration

In this doc, we use the term "Generative AI" and "LLM" interchangeably. LLM is used to transcribe voice recording to text, refine and summarize the text, and provide chat functions.

Noeji uses Gemini API for all LLM needs. Different Gemini models are chosen for different use cases. The prompts, model names and generation configs are stored in Firebase remote config to fast iterate to improve user experience.

Gemini API Keys are securely stored in Firebase. User doesn't have direct access to the API keys from their client device.

LLM is used to power the following features:
* Transcribe voice recording to text.
* Refine and summarize the text.
* Automatically pick color for ideabook.
* Automatically suggest name for ideabook.
* Provide functions to allow user chat with their ideabooks.
* Provide suggested prompts for chat.
* Provide note refresh functions.

# Cloud integration

Noeji mainly uses Firebase for cloud integration. Firebase provides the following services:
* Firebase Authentication for user authentication.
* Firebase Firestore for cloud data store and sync.
* Firebase Cloud Functions for serverless functions.
* Firebase Remote Config for remote config.
* Firebase Analytics for analytics.

## Firebase Firestore

Noeji uses Firestore for cloud data store and sync. This allows user to access their data from multiple devices.

Data model:

```
collection(users):
  doc(userId):
    p | string: user's passcode (encrypted) to lock / unlock ideabooks
    collection(ideabooks):
      doc(ideabookId):
        n | string: ideabook name
        c | single byte: ideabook color, encoded as single byte char.
        l | bool: ideabook is locked
        collection(ideas):
          doc(ideaId):
            c | string: idea content
            t | date: idea creation timestamp
        collection(notes):
          doc(noteId):
            t | string: note title
            c | string: note content
            r | date: note creation timestamp
```

For color encoding, follow the following map:

Color   | Byte Value (Hex) | Byte Value (Decimal)
--------|------------------|--------------------
red     | 0x00             | 0
green   | 0x01             | 1
blue    | 0x02             | 2
yellow  | 0x03             | 3
orange  | 0x04             | 4
purple  | 0x05             | 5

Noeji uses Firestore streaming listener to listen to data changes and update data in real time.

# Per user limits

Noeji enforeces the following per user limits on both client side and server side.

Free user:
* Limited to 3 ideabooks.
* Limited to 10 ideas per ideabook.
* Limited to 10 notes per ideabook.
* Limited chat messages (including note refresh): 10 per day, 100 per month.
* Limited to 1 minute audio recording length.
* Limited to 100 total words per ideabook name.
* Limited to 1000 total words per idea.
* Limited to 200 total words per user input to chat message.

Pro user:
* Limited to 1000 ideabooks.
* Limited to 100 ideas per ideabook.
* Limited to 100 notes per ideabook.
* Rate limit chat messages (including note refresh): 20 per minute, 1000 per day, 10000 per month.
* Limited to 5 minutes audio recording length.
* Limited to 100 total words per ideabook name.
* Limited to 1000 total words per idea.
* Limited to 200 total words per user input to chat message.

Old account clean up
* Inactive accounts are subject to auto-removal. An Account is deemed 'Inactive' if the User has not successfully logged into the App using any of their registered authentication methods for a continuous period of twelve (12) months.
* Users whose Accounts are identified as Inactive will receive a notification via their registered email address thirty (30) days in advance before the Account is scheduled for deletion. It is the User's responsibility to keep their email address on file with Noeji current and accessible.
* Following this 30-day notification period, if the Account remains Inactive, Noeji may proceed with the auto-removal and permanent deletion of the Account and all associated data. Noeji provides no way to recover the Account or its data once the Account has been deleted. All such deletions are final.

# Onboarding

Noeji provides tours to help user understand the app. The tours are shown when user first opens the app, and when user first creates a ideabook, and when user first chats with the ideabook.

# Subscription

Noeji use RevenueCat for user subscription state management.

Noeji support two tiers:
* Free user - subject to lower usage limits and limited access to features.
* Pro user (paid) - higher usage limits and unlimited access to all features.

Free user will see occasionally nudges to upgrade to pro user. Free user will see paywall when they reach the usage limits or tries to use pro features.

Pro user pays subscription fees to maintain their pro status. Noeji supports two payment models:
* Monthly subscription at $5.99 per month paid monthly.
* Annual subscription at $59.99 per year paid annually.
Both payment models entitles the user the same level of Pro tier access.

Noeji occasionally runs promos to discount the subscription price to encourage users to upgrade to Pro. Noeji reserves the right to change the subscription price at any time. Noeji will notify existing Pro users in advance at least 30 days before changing the subscription price.

# Platforms

Noeji supports the following platforms:
* Android
* iOS
* Web (implemention in progress)

# Infrastructure and Data Management

## Third-Party Infrastructure

Noeji relies on the following third-party infrastructure and services:

### Core Infrastructure
* **Firebase** (Google Cloud Platform)
  * Firebase Authentication - User authentication with Google Sign-In
  * Firebase Firestore - Cloud database for ideabooks, ideas, and notes
  * Firebase Cloud Functions - Serverless backend for LLM API calls
  * Firebase Remote Config - Dynamic configuration management
  * Firebase Analytics - Usage analytics and metrics
  * Firebase App Check - Security and abuse prevention

### AI and Machine Learning
* **Google Gemini API** - Large Language Model services
  * Gemini 2.0 Flash and Lite model for chat and content generation

### Payment Processing
* **RevenueCat** - Subscription management and billing
  * iOS: Apple App Store
  * Android: Google Play Store

### Authentication
* **Google Sign-In** - OAuth authentication provider

### Development and Monitoring
* **Flutter/Dart** - Cross-platform app framework
* **Various Flutter packages** - UI components, audio recording, permissions, etc.

## Data Storage and Persistence

### Cloud Data (Firebase Firestore)
**Persisted across devices and app reinstalls:**
* **User Profile Data**
  * User ID (Firebase Auth UID)
  * Passcode hash (encrypted fingerprint, field name 'p')
* **Ideabooks** (collection: users/{userId}/ideabooks)
  * Name (field: 'n')
  * Color (field: 'c', encoded as single byte 0-5)
  * Lock status (field: 'l', boolean)
  * Creation timestamp
* **Ideas** (collection: users/{userId}/ideabooks/{ideabookId}/ideas)
  * Content (field: 'c')
  * Creation timestamp (field: 't')
  * Sort order (field: 's', optional float for manual reordering)
* **Notes** (collection: users/{userId}/ideabooks/{ideabookId}/notes)
  * Title (field: 't')
  * Content (field: 'c')
  * Creation timestamp (field: 'r')
  * Sort order (field: 's', optional float for manual reordering)

### Local Device Data (Not Synced)
**Lost when switching devices or reinstalling app:**
* **Chat History** - Stored in local files per ideabook
* **Rate Limiting Data** - Chat message timestamps for rate limit enforcement
* **App Preferences**
  * Onboarding tour completion status
  * Sort order preferences
  * Theme selection
  * Filter settings
* **Audio Recordings** - Temporary files during voice recording (deleted after transcription)

### Hybrid Storage
* **Passcode** - The actual passcode is never stored anywhere. Only a SHA-1 hash is stored in Firestore for multi-device verification.

## Passcode Security

### Encryption and Storage
* **No Recovery Available** - Noeji explicitly does not provide passcode recovery. Users are clearly warned they are responsible for remembering their passcode.
* **Encryption Method** - Passcode is hashed using SHA-1 before storage
* **Storage Location** - Only the hash is stored in Firestore (field 'p' in user document)
* **Multi-Device Support** - Hash allows passcode verification across devices without storing the actual passcode
* **Backend Access** - Noeji backend has no access to the actual passcode and cannot retrieve it

## User Data Collection

### Current Data Collection
* **Authentication Data**
  * Google account basic profile (name, email, profile picture)
  * Firebase Auth UID
* **User Content**
  * Ideabook names, ideas, notes content
  * Creation timestamps
  * User preferences (colors, sort orders)
* **Usage Analytics** (via Firebase Analytics)
  * App usage patterns
  * Feature usage statistics
  * Error logs and crash reports
* **Technical Data**
  * Device OS and version
  * App version
  * Performance metrics
* **Rate Limiting Data**
  * Chat message timestamps (stored locally)
  * API usage patterns

### Future Data Collection Plans
Based on codebase analysis, no additional data collection is currently planned beyond what's already implemented.

## Data Usage

### Primary Uses
* **Service Delivery** - Storing and syncing user content across devices
* **Authentication** - User identification and access control
* **AI Features** - Providing LLM-powered transcription, chat, and content generation
* **Rate Limiting** - Preventing abuse and managing API costs
* **Analytics** - Understanding app usage to improve features

### Third-Party Data Sharing
* **RevenueCat** - Receives Firebase Auth UID for subscription management
* **Google Firebase** - Processes all user data as part of the service infrastructure
* **Google Gemini API** - Receives user content for AI processing (via Firebase Cloud Functions)

No user data is shared with other third parties for advertising, marketing, or other purposes.

# Payment and Billing

## Payment Processing
* **Platform Integration** - Payments processed through platform-native systems (Apple App Store, Google Play Store)
* **RevenueCat Integration** - Subscription state management using Firebase Auth UID as app user ID
* **Billing Information** - Noeji does not directly handle payment information; all billing is managed by Apple/Google

## Subscription Plans and Pricing
* **Monthly Subscription** - $5.99/month
* **Annual Subscription** - $59.99/year
* **Price Changes** - 30-day advance notice for existing subscribers
* **Auto Renew** - Auto renew will be on by default, but users can disable auto renew any time.
* **Promotional Pricing** - Occasional discounts available

## Refund Policy
Based on Terms of Service:
* **No Refunds** - Subscription fees are non-refundable.  No refund Account termination due to ToS violations. No refund for service interruptions due to reasonable service maintainces.       
* **Cancellation** - Users can cancel anytime, but no refunds for unused portions. Upon cancellation, Pro access will continue until the end of the current paid billing period. After this, the account will revert to the Free Tier (or be subject to Free Tier limitations).
* **Platform Policies** - Subject to Apple App Store and Google Play Store refund policies

## Billing Data Sharing
Information shared with third parties for billing:
* **RevenueCat** - Firebase Auth UID only (no personal information)
* **Apple/Google** - Payment information handled directly by platform stores
* **No Direct Payment Data** - Noeji never receives or stores credit card or payment information

# Security and Abuse Prevention

## Rate Limiting and Abuse Detection
* **Chat Rate Limits**
  * Free users: 10 messages/day, 100/month
  * Pro users: 20/minute, 1000/day, 10000/month
* **Usage Limits**
  * Free: 3 ideabooks, 10 ideas/notes per ideabook
  * Pro: 1000 ideabooks, 100 ideas/notes per ideabook
* **Audio Recording Limits**
  * Free: 1 minute maximum
  * Pro: 5 minutes maximum
* **Automatic Enforcement** - Limits enforced client-side and server-side
* **Rate Limit Messages** - Friendly user messages with specific time when limits reset

## Firebase App Check
* **Production Security**
  * iOS: App Attest with Device Check fallback
  * Android: Play Integrity
  * Web: reCAPTCHA v3
* **Development** - Debug providers for testing
* **Token Auto-Refresh** - Automatic token refresh to prevent expiration

## Error Handling and Permission Management
* **Permission Errors** - Generic user-friendly messages for security issues
* **Firestore Security** - Rules-based access control
* **Authentication Required** - All data access requires valid Firebase Auth
* **Listener Cleanup** - Automatic cleanup of Firestore listeners on sign-out to prevent permission errors

# Service Operations

## Server Maintenance and Interruptions
Based on current infrastructure:
* **Firebase SLA** - Relies on Google Firebase service level agreements
* **No Compensation Policy** - No explicit compensation for paid users during outages (standard for SaaS)
* **Graceful Degradation** - App continues to work offline with local data
* **Error Handling** - Retry logic with exponential backoff for network operations

## Account Management and Termination

### User-Initiated Account Deletion
* **Data Deletion Rights** - Users can request account deletion (per privacy policy)
* **Contact Required** - Must contact support for account deletion
* **Cascade Deletion** - When ideabook deleted, all associated ideas and notes are deleted

### Automatic Account Cleanup
* **Inactive Accounts** - Accounts not used for 12 months automatically deleted
* **Terms of Service** - Accounts not used for 6 months may be deleted (per ToS)

### Service Termination
* **Immediate Termination** - Noeji reserves right to terminate accounts for Terms violations
* **Data Access** - Upon termination, user access immediately ceases
* **Data Retention** - Terminated account data handling follows privacy policy

# Future Development Plans

Based on codebase analysis, the following features are **NOT** currently planned or implemented:

## Community Features
* **No Evidence** - No community features found in codebase or documentation
* **Current Focus** - Individual user experience only

## Sharing and Collaboration
* **No Sharing Features** - No sharing functionality implemented
* **No Collaboration** - Single-user ideabooks only
* **Private by Design** - All content is private to individual users

## Advertising and Monetization
* **No Ads** - No advertising infrastructure or plans found
* **No Cookies** - Mobile app doesn't use web cookies
* **No Affiliate Links** - No affiliate marketing implementation
* **Subscription Only** - Revenue model based solely on subscriptions

## Web Platform
* **In Progress** - Web implementation mentioned but not fully developed
* **Firebase Web Config** - Web Firebase configuration exists
* **Limited Functionality** - Web platform capabilities unclear from current codebase

Note: These future plans are based on current codebase analysis. Actual development roadmap may differ and should be confirmed with product management.

# Appendix

## Algorithm: Hybrid Sort Order with Fractional Indexing for Ideas

The sorting order logic discussed in this section applies to both ideas and notes. But for the sake of simple, we will use "ideas" as the example.

User triggers drag-and-drop mode to reorder the ideas by long pressing on a idea row. When long press on a idea row, the row turns slightly bigger and brighter (depending on theme) to indicate the row is selected and ready to be draged for reordering. Long press on a idea row does not enter the idea edit mode.

**Objective:**
To provide a flexible sorting mechanism for a list of ideas within an ideabook. The system supports:
1.  **Default Sorting:** Ideas are sorted by creation time, with the newest idea appearing at the top.
2.  **Manual User Sorting:** Users can reorder ideas via drag-and-drop.
3.  **Efficiency:** Minimize Firestore write operations and data storage, primarily by only modifying the document of the idea being moved.

**Core Data Representation:**
Each idea document utilizes two potential values for determining its sort position:

1.  `created_at` (Timestamp): A standard Firestore metadata field indicating when the idea document was created (e.g., milliseconds since epoch). This is an immutable value and serves as the default sort key.
2.  `s` (Float): An optional field stored in the idea document. This field is only present if the idea has been manually reordered by the user. It stores a floating-point number representing its user-defined sort order.

**Effective Sort Value:**
To determine an idea's actual position in the sorted list, we use its "effective sort value":
*   `effective_sort_value(idea) = idea.s ?? idea.created_at`
    *(This means: if `idea.s` exists and is not null, use `idea.s`; otherwise, use `idea.created_at`.)*

**Sorting Rule:** Ideas are sorted in **descending order** based on their `effective_sort_value`. A higher value means the idea is ranked higher (appears closer to the top).

**Algorithm Details:**

1.  **Default State & New Ideas:**
    *   When an idea is newly created, it does **not** have an `s` field.
    *   Its `effective_sort_value` is its `created_at` timestamp.
    *   By default, the list is sorted by `created_at` descending, so new ideas appear at the top.

2.  **Manual Reordering (Drag-and-Drop):**
    When a user drags an idea (`moved_idea`) to a new position:
    *   The `s` field of `moved_idea` will be calculated and stored (or updated if it already exists). No other idea documents are modified.
    *   Let `idea_above` be the idea immediately above the new target position, and `idea_below` be the idea immediately below the new target position.

    *   **Case 1: Moved between two existing ideas (`idea_above` and `idea_below` both exist):**
        *   `s_new = (effective_sort_value(idea_above) + effective_sort_value(idea_below)) / 2.0`
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 2: Moved to the absolute top of the list (`idea_above` does not exist):**
        *   Let `current_top_idea` be the idea that was previously at the top of the list (before this move).
        *   `s_new = effective_sort_value(current_top_idea) + 1.0`
        *   *(If the list was empty before moving this item, or if this is the only item being made top, `s_new` could default to its own `created_at + 1.0` or a predefined high value like `Date.now()`. The key is it must be higher than any other potential value. Using `previous_top_idea` is generally cleaner if the list wasn't empty).*
        *   `moved_idea.s` is set to `s_new`.

    *   **Case 3: Moved to the absolute bottom of the list (`idea_below` does not exist):**
        *   Let `current_bottom_idea` be the idea that was previously at the bottom of the list.
        *   `s_new = effective_sort_value(current_bottom_idea) - 1.0`
        *   *(Similar to the top case, if the list was empty, `s_new` could be its `created_at - 1.0` or a predefined low value. The key is it must be lower.)*
        *   `moved_idea.s` is set to `s_new`.

**Rationale:**

*   **Minimized Firestore Writes:** Only the `s` field of the single moved idea needs to be written to Firestore. This is cost-effective and performant.
*   **Minimized Storage:** Ideas that have never been manually reordered do not consume extra storage for an `s` field.
*   **Leverages Default Timestamps:** The `created_at` field provides a natural and free default sort order.
*   **Handles Edge Cases:** The `****` and `-1.0` logic for top/bottom placement creates clear separation and ample "space" for future insertions without immediately needing to average very close numbers.
*   **Resilient to Concurrent Edits (Partially):** While not fully conflict-free without more complex logic, if two users reorder different items, their changes are isolated to those items' `s` values. Collisions occur if they move items relative to the *same* neighbors simultaneously.

**Considerations & Limitations:**

*   **Floating-Point Precision:** When repeatedly inserting items between two specific items using the averaging method, the difference between their `s` values shrinks. Double-precision floats offer ~50-52 such "halving" operations in the absolute worst-case scenario (always inserting into the same narrowing gap) before precision limits prevent further distinct ordering within that specific gap. This is highly unlikely to be hit in typical user behavior for an ideabook.
*   **Client-Side Sorting:** The sorting logic (calculating effective sort values and performing the sort) is handled on the client. This is efficient for lists up to ~1000 items. For significantly larger lists, server-side assistance or more complex querying might be needed.

---

**Example Scenario:**

Assume `created_at` timestamps are simplified integers for clarity. Higher value = newer.

**Initial State (No `s` values yet):**
1.  Idea A (created_at: 1000) -> Effective: 1000 (Top)
2.  Idea B (created_at: 900)  -> Effective: 900
3.  Idea C (created_at: 800)  -> Effective: 800
4.  Idea D (created_at: 700)  -> Effective: 700 (Bottom)

**Displayed Order:** A, B, C, D

**Action 1: User drags Idea D between Idea A and Idea B.**
*   `moved_idea` = D
*   `idea_above` = A (effective_sort_value: 1000)
*   `idea_below` = B (effective_sort_value: 900)
*   D's new `s` value = (1000 + 900) / 2.0 = **950.0**
*   Firestore: Update Idea D, set `D.s = 950.0`

**New State & Effective Values:**
1.  Idea A (created_at: 1000)         -> Effective: 1000
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea C (created_at: 800)          -> Effective: 800

**Displayed Order:** A, D, B, C

**Action 2: User drags Idea C to the top of the list.**
*   `moved_idea` = C
*   `idea_above` = null (moving to top)
*   `current_top_idea` = A (effective_sort_value: 1000)
*   C's new `s` value = 1000 + 1.0 = **1001.0**
*   Firestore: Update Idea C, set `C.s = 1001.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea A (created_at: 1000)         -> Effective: 1000
3.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
4.  Idea B (created_at: 900)          -> Effective: 900

**Displayed Order:** C, A, D, B

**Action 3: User drags Idea A to the bottom of the list.**
*   `moved_idea` = A
*   `idea_below` = null (moving to bottom)
*   `current_bottom_idea` = B (effective_sort_value: 900)
*   A's new `s` value = 900 - 1.0 = **899.0**
*   Firestore: Update Idea A, set `A.s = 899.0`

**New State & Effective Values:**
1.  Idea C (created_at: 800, s: 1001.0) -> Effective: 1001.0
2.  Idea D (created_at: 700, s: 950.0) -> Effective: 950.0
3.  Idea B (created_at: 900)          -> Effective: 900
4.  Idea A (created_at: 1000, s: 899.0) -> Effective: 899.0

**Displayed Order:** C, D, B, A

This refined description should provide a clear understanding of your sorting algorithm, its benefits, and how it works in practice.
```

TODO:
* Apple Sign in - Server-to-Server Notification Endpoint
