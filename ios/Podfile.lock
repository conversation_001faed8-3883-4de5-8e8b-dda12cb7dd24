PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - cloud_firestore (5.6.7):
    - Firebase/Firestore (= 11.10.0)
    - firebase_core
    - Flutter
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Firestore (11.10.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.10.0)
  - Firebase/RemoteConfig (11.10.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.10.0)
  - firebase_analytics (11.4.6):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.2-6):
    - Firebase/CoreOnly (~> 11.10.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.10.0)
    - Flutter
  - firebase_auth (5.5.4):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_remote_config (5.4.4):
    - Firebase/RemoteConfig (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.10.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.13.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.13.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseFirestore (11.10.0):
    - FirebaseFirestoreBinary (= 11.10.0)
  - FirebaseFirestoreAbseilBinary (1.2024072200.0)
  - FirebaseFirestoreBinary (11.10.0):
    - FirebaseCore (= 11.10.0)
    - FirebaseCoreExtension (= 11.10.0)
    - FirebaseFirestoreInternalBinary (= 11.10.0)
    - FirebaseSharedSwift (= 11.10.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.69.0)
  - FirebaseFirestoreGRPCCoreBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.69.0)
  - FirebaseFirestoreGRPCCPPBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.69.0)
  - FirebaseFirestoreInternalBinary (11.10.0):
    - FirebaseCore (= 11.10.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfig (11.10.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.13.0)
  - FirebaseSharedSwift (11.10.0)
  - Flutter (1.0.0)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - purchases_flutter (8.8.1):
    - Flutter
    - PurchasesHybridCommon (= 13.32.0)
  - purchases_ui_flutter (8.8.1):
    - Flutter
    - PurchasesHybridCommonUI (= 13.32.0)
  - PurchasesHybridCommon (13.32.0):
    - RevenueCat (= 5.24.0)
  - PurchasesHybridCommonUI (13.32.0):
    - PurchasesHybridCommon (= 13.32.0)
    - RevenueCatUI (= 5.24.0)
  - RecaptchaInterop (101.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - RevenueCat (5.24.0)
  - RevenueCatUI (5.24.0):
    - RevenueCat (= 5.24.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.10.0`)
  - Flutter (from `Flutter`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - purchases_flutter (from `.symlinks/plugins/purchases_flutter/ios`)
  - purchases_ui_flutter (from `.symlinks/plugins/purchases_ui_flutter/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PurchasesHybridCommon
    - PurchasesHybridCommonUI
    - RecaptchaInterop
    - RevenueCat
    - RevenueCatUI

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.10.0
  Flutter:
    :path: Flutter
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  purchases_flutter:
    :path: ".symlinks/plugins/purchases_flutter/ios"
  purchases_ui_flutter:
    :path: ".symlinks/plugins/purchases_ui_flutter/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.10.0

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  cloud_firestore: b7b3bfffcd2ea3205f612d5602f959a0ec33c7b0
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 23a2678c8f20c46ef305c2f05a9ead0e681163cf
  firebase_app_check: 6073868cfe434aa25e008ef2ca3ca8f457271869
  firebase_auth: 4d9603c196d22f71e80cf518b71939c4f2117088
  firebase_core: ba71b44041571da878cb624ce0d80250bcbe58ad
  firebase_remote_config: 6401714e2bea6f7905f71f780f4a49ff89728d2d
  FirebaseABTesting: dfc10eb6cc08fe3b391ac9e5aa40396d43ea6675
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheck: 9687ebd909702469bc09d2d58008697b83f4ac27
  FirebaseAppCheckInterop: 72066489c209823649a997132bcd9269bc33a4bb
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: 4fa327ec3c551a80a6929561f83af80b1dd44937
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseFirestore: e5c3d65fef6c6e07c47af2130aaadfb89dd07ea7
  FirebaseFirestoreAbseilBinary: 4cfa8823cedc1b774843e04fe578ad279b387f97
  FirebaseFirestoreBinary: 6cf15472267bbb89ce9ac5e645eb0abae29208ce
  FirebaseFirestoreGRPCBoringSSLBinary: c3dfef3ff448ae2c1c85f9baf9fac5afc4db99fa
  FirebaseFirestoreGRPCCoreBinary: 565534e160a0415d12185f7f171c52a567382fbd
  FirebaseFirestoreGRPCCPPBinary: 6c0134e8d230ee58b9d51dec2a30a48efd6d5dc7
  FirebaseFirestoreInternalBinary: 96b309279c4efdf00b83ab80e8af4d0a73d30258
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseRemoteConfig: 10695bc0ce3b103e3706a5578c43f2a9f69d5aaa
  FirebaseRemoteConfigInterop: 7915cec47731a806cda541f90898ad0fab8f9f86
  FirebaseSharedSwift: 1baacae75939499b5def867cbe34129464536a38
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  purchases_flutter: 4cf57c17c62108103a549ebe12b28a0203f0cf55
  purchases_ui_flutter: 3d8fc20c096bd123d665b972e3ff3c57a0f0da88
  PurchasesHybridCommon: 662aa1f4769ff4ef594e35adf1a5d0ad7b3d08b7
  PurchasesHybridCommonUI: ce17a7fcc23bc9e47b6ed117fb87b4fc99539e15
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  RevenueCat: 2d3639d30b8a71cbfe3d99cef8243c602793e0ec
  RevenueCatUI: 80bec9099c200c254c0ec0025b4fb83a85fbacce
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: d2677b866001bd1228717097482801e21ae52c28

COCOAPODS: 1.16.2
