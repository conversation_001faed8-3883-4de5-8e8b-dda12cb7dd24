import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/ideabook_color_provider.dart';
import 'package:noeji/ui/providers/fake_data_provider.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/ui/widgets/color_picker.dart';
import 'package:noeji/services/color_palette/color_palette_service.dart';

void main() {
  testWidgets('ColorPicker displays color options and updates ideabook color', (
    WidgetTester tester,
  ) async {
    // Create a test ideabook
    final testIdeabook = Ideabook(
      id: 'test-id',
      name: 'Test Ideabook',
      color: IdeabookColor.red,
      isLocked: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Create a container for our providers
    final fakeNotifier = FakeIdeabooksNotifier()..state = [testIdeabook];
    final container = ProviderContainer(
      overrides: [
        fakeIdeabooksProvider.overrideWith((ref) => fakeNotifier),
        // Override the updateIdeabookColorProvider to use our fake notifier
        updateIdeabookColorProvider.overrideWith(
          (ref) => (String ideabookId, IdeabookColor newColor) {
            // Update the ideabook color using the fake notifier
            fakeNotifier.updateIdeabookColor(ideabookId, newColor);
            // Exit color picking mode
            ref.read(colorPickingIdeabookIdProvider.notifier).state = null;
          },
        ),
      ],
    );

    // Create mock color palette for testing
    final mockColorPalette = ColorPalette(
      name: 'test',
      colors: {
        'red': const Color(0xFFE76F51),
        'orange': const Color(0xFFF4A261),
        'yellow': const Color(0xFFE9C46A),
        'green': const Color(0xFF8AB17D),
        'blue': const Color(0xFF2A9D8F),
        'purple': const Color(0xFF264653),
      },
    );

    // Create mock theme data with the required extensions
    final noejiColors = NoejiColors.light(mockColorPalette);
    final theme = ThemeData.light().copyWith(
      extensions: [noejiColors, NoejiTextStyles.light(noejiColors)],
    );

    // Build the widget
    await tester.pumpWidget(
      UncontrolledProviderScope(
        container: container,
        child: MaterialApp(
          theme: theme,
          home: Scaffold(
            body: ColorPicker(
              ideabookId: testIdeabook.id,
              currentColor: testIdeabook.color,
            ),
          ),
        ),
      ),
    );

    // Verify that the color picker displays color options (5 colors + current color + background)
    expect(
      find.byType(Container),
      findsNWidgets(7),
    ); // 5 color options + current color + background container

    // We know the order of colors in the ColorPicker widget:
    // [purple, blue, green, yellow, orange, red]
    // Since red is filtered out (current color), the remaining colors are:
    // [purple, blue, green, yellow, orange]

    // Tap on any color option (let's use the first one which should be purple)
    await tester.tap(find.byType(GestureDetector).at(1)); // First color option
    await tester.pump();

    // Verify that the ideabook color was updated (should be purple)
    final updatedIdeabooks = container.read(fakeIdeabooksProvider);
    expect(updatedIdeabooks.length, 1);
    expect(updatedIdeabooks[0].id, testIdeabook.id);
    expect(
      updatedIdeabooks[0].color,
      IdeabookColor.purple,
    ); // First color in the list

    // Verify that color picking mode was exited
    final colorPickingId = container.read(colorPickingIdeabookIdProvider);
    expect(colorPickingId, isNull);
  });
}
