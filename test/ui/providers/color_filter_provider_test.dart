import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/models/models.dart';
import 'package:noeji/ui/providers/color_filter_provider.dart';

void main() {
  test('colorFilterProvider should initially be null', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    expect(container.read(colorFilterProvider), null);
  });

  test('colorFilteredIdeabooksProvider should filter ideabooks by color', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Create test ideabooks with different colors
    final testIdeabooks = [
      Ideabook(
        id: '1',
        name: 'Red Ideabook',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Ideabook(
        id: '2',
        name: 'Blue Ideabook',
        color: IdeabookColor.blue,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Ideabook(
        id: '3',
        name: 'Green Ideabook',
        color: IdeabookColor.green,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // Test with no color filter
    expect(
      container.read(colorFilteredIdeabooksProvider(testIdeabooks)).length,
      testIdeabooks.length,
    );

    // Set color filter to red
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;

    // Test with red color filter
    final filteredIdeabooks = container.read(
      colorFilteredIdeabooksProvider(testIdeabooks),
    );
    expect(filteredIdeabooks.length, 1);
    expect(filteredIdeabooks.first.id, '1');
    expect(filteredIdeabooks.first.color, IdeabookColor.red);

    // Change color filter to blue
    container.read(colorFilterProvider.notifier).state = IdeabookColor.blue;

    // Test with blue color filter
    final blueFilteredIdeabooks = container.read(
      colorFilteredIdeabooksProvider(testIdeabooks),
    );
    expect(blueFilteredIdeabooks.length, 1);
    expect(blueFilteredIdeabooks.first.id, '2');
    expect(blueFilteredIdeabooks.first.color, IdeabookColor.blue);

    // Clear color filter
    container.read(colorFilterProvider.notifier).state = null;

    // Test with no color filter again
    expect(
      container.read(colorFilteredIdeabooksProvider(testIdeabooks)).length,
      testIdeabooks.length,
    );
  });

  test('ideabookColorFilterProvider should provide a filter', () {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    // Set color filter to red
    container.read(colorFilterProvider.notifier).state = IdeabookColor.red;

    // Get the filter
    final filter = container.read(ideabookColorFilterProvider);

    // Check that the filter is active
    expect(filter.isActive, true);

    // Create test ideabooks
    final testIdeabooks = [
      Ideabook(
        id: '1',
        name: 'Red Ideabook',
        color: IdeabookColor.red,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Ideabook(
        id: '2',
        name: 'Blue Ideabook',
        color: IdeabookColor.blue,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // Apply the filter
    final filteredIdeabooks = filter.apply(testIdeabooks);

    // Verify the results
    expect(filteredIdeabooks.length, 1);
    expect(filteredIdeabooks.first.id, '1');
  });
}
