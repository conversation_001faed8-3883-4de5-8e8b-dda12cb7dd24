import 'package:flutter_test/flutter_test.dart';
import 'package:noeji/services/llm/firebase_ai_provider.dart';
import 'package:noeji/services/llm/llm_service.dart';

void main() {
  group('Firebase AI Migration Tests', () {
    test('FirebaseAiProvider can be instantiated', () {
      // Test that our new provider can be created
      final provider = FirebaseAiProvider();
      expect(provider, isNotNull);
    });

    test('FirebaseAiProvider can create schemas for different use cases', () {
      final provider = FirebaseAiProvider();
      
      // Test schema creation for different transcription use cases
      final newIdeabookSchema = provider.getSchemaForTranscriptionUseCase(
        TranscriptionUseCase.newIdeabook,
      );
      expect(newIdeabookSchema, isNotNull);

      final newIdeaSchema = provider.getSchemaForTranscriptionUseCase(
        TranscriptionUseCase.newIdea,
      );
      expect(newIdeaSchema, isNotNull);

      final chatInputSchema = provider.getSchemaForTranscriptionUseCase(
        TranscriptionUseCase.chatInput,
      );
      expect(chatInputSchema, isNotNull);
    });

    test('FirebaseAiProvider can create chat response schema', () {
      final provider = FirebaseAiProvider();
      
      final chatSchema = provider.getChatResponseSchema();
      expect(chatSchema, isNotNull);
    });

    test('FirebaseAiProvider can create models', () {
      final provider = FirebaseAiProvider();

      // Test that we can call the model creation methods without errors
      expect(() {
        provider.getSchemaForTranscriptionUseCase(TranscriptionUseCase.newIdeabook);
      }, returnsNormally);

      expect(() {
        provider.getChatResponseSchema();
      }, returnsNormally);
    });
  });
}
