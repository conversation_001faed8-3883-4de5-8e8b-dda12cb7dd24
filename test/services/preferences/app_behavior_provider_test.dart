import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('App Behavior Reset Integration', () {
    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test(
      'should reset all app behavior preferences using direct SharedPreferences approach',
      () async {
        // Arrange - Set all preferences to true
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('noeji_send_voice_chat_on_finish', true);
        await prefs.setBool('noeji_copy_as_markdown', true);
        await prefs.setBool('noeji_generative_suggested_prompts', true);

        // Verify they are set
        expect(prefs.getBool('noeji_send_voice_chat_on_finish'), true);
        expect(prefs.getBool('noeji_copy_as_markdown'), true);
        expect(prefs.getBool('noeji_generative_suggested_prompts'), true);

        // Act - Reset using the same approach as AppBehaviorNotifier.resetToDefaults()
        await Future.wait([
          prefs.remove('noeji_send_voice_chat_on_finish'),
          prefs.remove('noeji_copy_as_markdown'),
          prefs.remove('noeji_generative_suggested_prompts'),
        ]);

        // Assert - All should be removed (null)
        expect(prefs.getBool('noeji_send_voice_chat_on_finish'), null);
        expect(prefs.getBool('noeji_copy_as_markdown'), null);
        expect(prefs.getBool('noeji_generative_suggested_prompts'), null);
      },
    );

    test('should handle partial reset gracefully', () async {
      // Arrange - Set only some preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('noeji_send_voice_chat_on_finish', true);
      // Don't set the other two

      // Verify initial state
      expect(prefs.getBool('noeji_send_voice_chat_on_finish'), true);
      expect(prefs.getBool('noeji_copy_as_markdown'), null);
      expect(prefs.getBool('noeji_generative_suggested_prompts'), null);

      // Act - Reset all (should handle missing keys gracefully)
      await Future.wait([
        prefs.remove('noeji_send_voice_chat_on_finish'),
        prefs.remove('noeji_copy_as_markdown'),
        prefs.remove('noeji_generative_suggested_prompts'),
      ]);

      // Assert - All should be null
      expect(prefs.getBool('noeji_send_voice_chat_on_finish'), null);
      expect(prefs.getBool('noeji_copy_as_markdown'), null);
      expect(prefs.getBool('noeji_generative_suggested_prompts'), null);
    });
  });
}
