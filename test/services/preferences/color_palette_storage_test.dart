import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/services/preferences/color_palette_storage.dart';

void main() {
  group('ColorPaletteStorage', () {
    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test(
      'saveSelectedColorPalette should save the palette name to shared preferences',
      () async {
        // Act
        final result = await ColorPaletteStorage.saveSelectedColorPalette(
          'vibrant',
        );

        // Assert
        expect(result, true);

        // Verify it was saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('noeji_selected_color_palette'), 'vibrant');
      },
    );

    test(
      'loadSelectedColorPalette should return the saved palette name',
      () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('noeji_selected_color_palette', 'ocean');

        // Act
        final result = await ColorPaletteStorage.loadSelectedColorPalette();

        // Assert
        expect(result, 'ocean');
      },
    );

    test(
      'loadSelectedColorPalette should return null when no preference is saved',
      () async {
        // Act
        final result = await ColorPaletteStorage.loadSelectedColorPalette();

        // Assert
        expect(result, null);
      },
    );

    test(
      'resetSelectedColorPalette should remove the saved preference',
      () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('noeji_selected_color_palette', 'sunset');

        // Act
        final result = await ColorPaletteStorage.resetSelectedColorPalette();

        // Assert
        expect(result, true);
        expect(prefs.getString('noeji_selected_color_palette'), null);
      },
    );
  });
}
