import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/services/preferences/app_behavior_storage.dart';

void main() {
  group('AppBehaviorStorage', () {
    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    group('Send Voice Chat On Finish', () {
      test('saveSendVoiceChatOnFinish should save the preference', () async {
        // Act
        final result = await AppBehaviorStorage.saveSendVoiceChatOnFinish(true);

        // Assert
        expect(result, true);

        // Verify the value was saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('noeji_send_voice_chat_on_finish'), true);
      });

      test(
        'loadSendVoiceChatOnFinish should return the saved preference',
        () async {
          // Arrange
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('noeji_send_voice_chat_on_finish', true);

          // Act
          final result = await AppBehaviorStorage.loadSendVoiceChatOnFinish();

          // Assert
          expect(result, true);
        },
      );

      test(
        'loadSendVoiceChatOnFinish should return default when none is saved',
        () async {
          // Act
          final result = await AppBehaviorStorage.loadSendVoiceChatOnFinish(
            defaultValue: false,
          );

          // Assert
          expect(result, false);
        },
      );

      test('resetSendVoiceChatOnFinish should remove the preference', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('noeji_send_voice_chat_on_finish', true);

        // Act
        final result = await AppBehaviorStorage.resetSendVoiceChatOnFinish();

        // Assert
        expect(result, true);
        expect(prefs.getBool('noeji_send_voice_chat_on_finish'), null);
      });
    });

    group('Copy As Markdown', () {
      test('saveCopyAsMarkdown should save the preference', () async {
        // Act
        final result = await AppBehaviorStorage.saveCopyAsMarkdown(true);

        // Assert
        expect(result, true);

        // Verify the value was saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('noeji_copy_as_markdown'), true);
      });

      test('loadCopyAsMarkdown should return the saved preference', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('noeji_copy_as_markdown', true);

        // Act
        final result = await AppBehaviorStorage.loadCopyAsMarkdown();

        // Assert
        expect(result, true);
      });

      test('resetCopyAsMarkdown should remove the preference', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('noeji_copy_as_markdown', true);

        // Act
        final result = await AppBehaviorStorage.resetCopyAsMarkdown();

        // Assert
        expect(result, true);
        expect(prefs.getBool('noeji_copy_as_markdown'), null);
      });
    });

    group('Generative Suggested Prompts', () {
      test(
        'saveGenerativeSuggestedPrompts should save the preference',
        () async {
          // Act
          final result =
              await AppBehaviorStorage.saveGenerativeSuggestedPrompts(true);

          // Assert
          expect(result, true);

          // Verify the value was saved
          final prefs = await SharedPreferences.getInstance();
          expect(prefs.getBool('noeji_generative_suggested_prompts'), true);
        },
      );

      test(
        'loadGenerativeSuggestedPrompts should return the saved preference',
        () async {
          // Arrange
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('noeji_generative_suggested_prompts', true);

          // Act
          final result =
              await AppBehaviorStorage.loadGenerativeSuggestedPrompts();

          // Assert
          expect(result, true);
        },
      );

      test(
        'resetGenerativeSuggestedPrompts should remove the preference',
        () async {
          // Arrange
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('noeji_generative_suggested_prompts', true);

          // Act
          final result =
              await AppBehaviorStorage.resetGenerativeSuggestedPrompts();

          // Assert
          expect(result, true);
          expect(prefs.getBool('noeji_generative_suggested_prompts'), null);
        },
      );
    });

    group('Reset All Preferences', () {
      test(
        'resetAllPreferences should reset all app behavior preferences',
        () async {
          // Arrange - Set all preferences to true
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('noeji_send_voice_chat_on_finish', true);
          await prefs.setBool('noeji_copy_as_markdown', true);
          await prefs.setBool('noeji_generative_suggested_prompts', true);

          // Verify they are set
          expect(prefs.getBool('noeji_send_voice_chat_on_finish'), true);
          expect(prefs.getBool('noeji_copy_as_markdown'), true);
          expect(prefs.getBool('noeji_generative_suggested_prompts'), true);

          // Act
          final result = await AppBehaviorStorage.resetAllPreferences();

          // Assert
          expect(result, true);
          expect(prefs.getBool('noeji_send_voice_chat_on_finish'), null);
          expect(prefs.getBool('noeji_copy_as_markdown'), null);
          expect(prefs.getBool('noeji_generative_suggested_prompts'), null);
        },
      );
    });
  });
}
