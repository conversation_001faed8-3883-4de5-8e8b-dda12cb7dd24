import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/services/preferences/sort_order_storage.dart';
import 'package:noeji/ui/providers/sort_provider.dart';

void main() {
  group('SortOrderStorage', () {
    setUp(() async {
      // Set up shared preferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test(
      'saveSortOrder should save the sort order to shared preferences',
      () async {
        // Act
        final result = await SortOrderStorage.saveSortOrder(
          SortOrder.descending,
        );

        // Assert
        expect(result, true);

        // Verify the value was saved
        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getString('noeji_ideabook_sort_order'), 'descending');
      },
    );

    test('loadSortOrder should return the saved sort order', () async {
      // Arrange
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('noeji_ideabook_sort_order', 'descending');

      // Act
      final result = await SortOrderStorage.loadSortOrder();

      // Assert
      expect(result, SortOrder.descending);
    });

    test(
      'loadSortOrder should return default sort order when none is saved',
      () async {
        // Act
        final result = await SortOrderStorage.loadSortOrder();

        // Assert
        expect(result, SortOrder.ascending);
      },
    );

    test('loadSortOrder should handle invalid values gracefully', () async {
      // Arrange
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('noeji_ideabook_sort_order', 'invalid_value');

      // Act
      final result = await SortOrderStorage.loadSortOrder();

      // Assert
      expect(result, SortOrder.ascending);
    });
  });
}
