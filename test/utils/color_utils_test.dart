import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:noeji/utils/color_utils.dart';

void main() {
  group('ColorUtils', () {
    group('getContrastingTextColor', () {
      test('should return black text for light backgrounds', () {
        // Test with white background
        final textColor = ColorUtils.getContrastingTextColor(Colors.white);
        expect(textColor, equals(const Color(0xFF333333)));

        // Test with light gray background
        final lightGrayTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFFEEEEEE),
        );
        expect(lightGrayTextColor, equals(const Color(0xFF333333)));

        // Test with yellow background (typically light)
        final yellowTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFFffca3a),
        );
        expect(yellowTextColor, equals(const Color(0xFF333333)));
      });

      test('should return white text for dark backgrounds', () {
        // Test with black background
        final textColor = ColorUtils.getContrastingTextColor(Colors.black);
        expect(textColor, equals(Colors.white));

        // Test with dark gray background
        final darkGrayTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFF333333),
        );
        expect(darkGrayTextColor, equals(Colors.white));

        // Test with blue background (typically dark)
        final blueTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFF1982c4),
        );
        expect(blueTextColor, equals(Colors.white));

        // Test with purple background (typically dark)
        final purpleTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFF6a4c93),
        );
        expect(purpleTextColor, equals(Colors.white));
      });

      test('should allow custom text colors', () {
        final customDarkColor = const Color(0xFF111111);
        final customLightColor = const Color(0xFFFAFAFA);

        // Test with light background and custom colors
        final lightBgTextColor = ColorUtils.getContrastingTextColor(
          Colors.white,
          darkTextColor: customDarkColor,
          lightTextColor: customLightColor,
        );
        expect(lightBgTextColor, equals(customDarkColor));

        // Test with dark background and custom colors
        final darkBgTextColor = ColorUtils.getContrastingTextColor(
          Colors.black,
          darkTextColor: customDarkColor,
          lightTextColor: customLightColor,
        );
        expect(darkBgTextColor, equals(customLightColor));
      });

      test('should handle edge cases correctly', () {
        // Test with medium gray - this tests the threshold behavior
        final mediumGrayTextColor = ColorUtils.getContrastingTextColor(
          const Color(0xFF808080),
        );
        // The result depends on Flutter's brightness estimation algorithm
        expect(
          mediumGrayTextColor,
          isIn([const Color(0xFF333333), Colors.white]),
        );
      });

      test(
        'should be consistent with Flutter ThemeData.estimateBrightnessForColor',
        () {
          final testColors = [
            Colors.white,
            Colors.black,
            const Color(0xFFff595e), // Red
            const Color(0xFFff924c), // Orange
            const Color(0xFFffca3a), // Yellow
            const Color(0xFF8ac926), // Green
            const Color(0xFF1982c4), // Blue
            const Color(0xFF6a4c93), // Purple
          ];

          for (final color in testColors) {
            final brightness = ThemeData.estimateBrightnessForColor(color);
            final textColor = ColorUtils.getContrastingTextColor(color);

            if (brightness == Brightness.dark) {
              expect(
                textColor,
                equals(Colors.white),
                reason: 'Dark background $color should have white text',
              );
            } else {
              expect(
                textColor,
                equals(const Color(0xFF333333)),
                reason: 'Light background $color should have dark text',
              );
            }
          }
        },
      );
    });
  });
}
