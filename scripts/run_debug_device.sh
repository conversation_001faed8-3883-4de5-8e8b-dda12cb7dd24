#!/bin/bash

# This script helps run the app in debug mode on a real device
# to ensure Firebase App Check debug provider is used

# Make the script executable with: chmod +x scripts/run_debug_device.sh
# Run it with: ./scripts/run_debug_device.sh

# Get list of connected devices
echo "Fetching connected devices..."
flutter devices

echo ""
echo "Select a device to run the app in debug mode:"
read -p "Enter the device ID or name: " DEVICE_ID

if [ -z "$DEVICE_ID" ]; then
  echo "No device selected. Exiting."
  exit 1
fi

echo ""
echo "Running app in debug mode on device: $DEVICE_ID"
echo "This will ensure kDebugMode is true and Firebase App Check debug provider is used."
echo ""

# Run the app in debug mode on the selected device
flutter run --debug -d "$DEVICE_ID"

# Note: The first time you run this, look for the Firebase App Check debug token in the logs
# and register it in the Firebase console (App Check > Manage debug tokens)
