import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider to track which ideabook is currently in right-to-left swipe mode
/// Stores the ID of the ideabook that is currently showing the context menu
final swipedIdeabookIdProvider = StateProvider<String?>((ref) => null);

/// Provider to track which ideabook is currently in left-to-right swipe mode
/// Stores the ID of the ideabook that is currently showing the color picker
final leftSwipedIdeabookIdProvider = StateProvider<String?>((ref) => null);
