Okay, this is a great and common migration scenario! The Firebase AI Logic SDKs are designed to simplify exactly this.

Here's a detailed plan to migrate your Flutter app from using Firebase Cloud Functions for Gemini API calls to directly using the Firebase AI Logic SDKs.

**Overall Strategy:**

2.  **Create Wrapper for `GenerativeModel`:** Abstract the `GenerativeModel` creation and configuration.
3.  **Refactor `GeminiService` (Audio Transcription):**
    *   Replace Cloud Function call with direct SDK call (`generateContent`).
    *   Implement structured output using `responseSchema` to ensure JSON format for parsing.
4.  **Refactor `GeminiChatService` (Chat):**
    *   Replace Cloud Function call with direct SDK call (`generateContent`).
    *   Format chat history correctly for the SDK.
    *   Handle system instructions via `GenerationConfig`.
    *   Implement structured output if chat responses are expected in JSON (which they seem to be, given your parsing logic).
5.  **Update Error Handling and Retries:** Adapt to SDK-specific exceptions.
6.  **Remove `CloudFunctionService`:** Once all functionalities are migrated.

---

**Detailed Migration Plan**


**Phase 1: Centralized Firebase AI Service**

It's good practice to have a central place to get your `GenerativeModel` instances, especially if you use different configurations.

Create `firebase_ai_provider.dart` (or similar):

```dart
import 'package:firebase_ai/firebase_ai.dart';

class FirebaseAiProvider {
  // For general text/chat models
  GenerativeModel _getGeminiModel({
    required String modelName,
    GenerationConfig? generationConfig,
  }) {
    return FirebaseAI.instance.googleAI().generativeModel(
          model: modelName,
          generationConfig: generationConfig,
        );
  }

  // For audio transcription with structured JSON output
  GenerativeModel getAudioTranscriptionModel({
    required String modelName,
    required Schema responseSchema, // Expects JSON
    GenerationConfig? baseGenerationConfig,
  }) {
    final config = (baseGenerationConfig ?? const GenerationConfig()).copyWith(
      responseMimeType: 'application/json',
      responseSchema: responseSchema,
      // candidateCount: 1, // Usually default, but can be explicit
      // temperature: ... // from your LlmModelConfig
      // maxOutputTokens: ...
    );
    return _getGeminiModel(modelName: modelName, generationConfig: config);
  }

  // For chat with potentially structured JSON output
  GenerativeModel getChatModel({
    required String modelName,
    Schema? responseSchema, // Optional, if chat response is JSON
    String? systemInstructionText,
    GenerationConfig? baseGenerationConfig,
  }) {
    SystemInstruction? systemInstruction;
    if (systemInstructionText != null && systemInstructionText.isNotEmpty) {
      systemInstruction = SystemInstruction(parts: [Part.text(systemInstructionText)]);
    }

    final config = (baseGenerationConfig ?? const GenerationConfig()).copyWith(
      responseMimeType: responseSchema != null ? 'application/json' : null, // Set only if schema is provided
      responseSchema: responseSchema,
      systemInstruction: systemInstruction, // Add system instruction here
      // candidateCount: 1,
      // temperature: ...
      // maxOutputTokens: ...
    );
    return _getGeminiModel(modelName: modelName, generationConfig: config);
  }
}
```
*   You'll need to pass the actual `GenerationConfig` parameters (temperature, maxTokens, etc.) from your `LlmModelConfig` to this provider.

---

**Phase 2: Refactor `GeminiService` (Audio Transcription)**

1.  **Update Constructor:**
    Inject `FirebaseAiProvider` instead of `CloudFunctionService`.
    ```dart
    // llm_service.dart
    // ...
    import 'package:firebase_ai/firebase_ai.dart'; // Add this
    import 'path_to/firebase_ai_provider.dart'; // Add this

    class GeminiService implements LlmService {
      final FirebaseAiProvider _firebaseAiProvider; // Changed
      final LlmPrompts _llmPrompts;
      final LlmModelConfig _llmModelConfig;
      final String? _userTier;

      GeminiService({
        FirebaseAiProvider? firebaseAiProvider, // Changed
        required LlmPrompts llmPrompts,
        required LlmModelConfig llmModelConfig,
        String? userTier,
      }) : _firebaseAiProvider = firebaseAiProvider ?? FirebaseAiProvider(), // Changed
           _llmPrompts = llmPrompts,
           _llmModelConfig = llmModelConfig,
           _userTier = userTier;
      // ...
    ```

2.  **Define Response Schemas:**
    For each `TranscriptionUseCase`, define the expected JSON output structure.
    ```dart
    // Inside GeminiService or a helper class
    Schema _getSchemaForUseCase(TranscriptionUseCase useCase) {
      switch (useCase) {
        case TranscriptionUseCase.newIdeabook:
          return Schema.object(
            properties: {
              'short_name': Schema.string(description: "Short name for the ideabook"),
              'color': Schema.enumString(
                enumValues: ['red', 'green', 'blue', 'yellow', 'orange', 'purple'],
                description: "Suggested color for the ideabook",
              ),
            },
            requiredProperties: ['short_name'], // 'color' is optional
            // If your response is an array with one object:
            // return Schema.array(items: Schema.object(... above properties ...));
          );
        case TranscriptionUseCase.newIdea:
          return Schema.object(
            properties: {
              'idea': Schema.string(description: "The detailed idea content"),
              'short_title': Schema.string(description: "A short title for the idea"),
            },
            requiredProperties: ['idea'], // 'short_title' is optional
          );
        case TranscriptionUseCase.chatInput: // Simple transcription
          return Schema.object(
            properties: {
              'transcript': Schema.string(description: "The transcribed audio content"),
            },
            requiredProperties: ['transcript'],
          );
      }
    }
    ```
    *Note: If your current Cloud Function returns `[{"short_name": "..."}]` (an array with one object), then the schema should be `Schema.array(items: Schema.object(...))`. Adjust based on the actual current JSON structure.*

3.  **Modify `_performTranscription`:**
    ```dart
    // llm_service.dart
    // ...
    Future<TranscriptionResponse> _performTranscription(
      String audioFilePath,
      TranscriptionUseCase useCase,
      String? ideabookName,
    ) async {
      try {
        final fileSize = await File(audioFilePath).length();
        Logger.debug('Audio file size: $fileSize bytes');
        if (fileSize > 19 * 1024 * 1024) { // ~19MB to be safe for base64 overhead
            Logger.warn('Audio file size is large ($fileSize bytes), might exceed 20MB request limit after base64 encoding.');
            // Potentially return failure or use URL method if supported and file is publicly accessible
        }

        final bytes = await File(audioFilePath).readAsBytes();
        // No need to base64Encode here, InlineDataPart takes bytes directly.
        // Firebase SDK handles base64 encoding internally.

        final promptText = _getPromptForUseCase(useCase, ideabookName);
        final modelName = _llmModelConfig.getAudioTranscriptionModel(userTier: _userTier);
        
        // Extract relevant parts from your LlmModelConfig for GenerationConfig
        final Map<String, dynamic> remoteConfigMap = _llmModelConfig.getAudioTranscriptionConfig(userTier: _userTier);
        final baseGenerationConfig = GenerationConfig(
            candidateCount: remoteConfigMap['candidateCount'] as int?,
            maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
            temperature: remoteConfigMap['temperature'] as double?,
            topP: remoteConfigMap['topP'] as double?,
            topK: remoteConfigMap['topK'] as int?,
            // stopSequences: (remoteConfigMap['stopSequences'] as List<dynamic>?)?.cast<String>(), // if you use this
        );

        final schema = _getSchemaForUseCase(useCase);

        final model = _firebaseAiProvider.getAudioTranscriptionModel(
          modelName: modelName,
          responseSchema: schema,
          baseGenerationConfig: baseGenerationConfig,
        );

        final audioPart = DataPart('audio/aac', bytes); // Assuming AAC format
        final promptPart = TextPart(promptText);

        Logger.debug('Calling Firebase AI Logic SDK for transcription with model $modelName');

        final GenerateContentResponse response = await model.generateContent([
          Content.multi([promptPart, audioPart]) // For multimodal input
        ]);

        Logger.debug('Firebase AI Logic SDK response: ${response.text}');

        if (response.text == null || response.text!.isEmpty) {
          throw Exception('No text in response from Firebase AI Logic SDK');
        }

        final String jsonString = response.text!;
        // The SDK with responseSchema should directly give you valid JSON string.
        // Your existing parsing logic:
        final dynamic parsedJson = jsonDecode(jsonString);
        Map<String, dynamic> jsonData;

        if (parsedJson is List) {
          if (parsedJson.isEmpty) throw Exception('Empty JSON array in response');
          jsonData = parsedJson[0] as Map<String, dynamic>;
        } else if (parsedJson is Map<String, dynamic>) {
          jsonData = parsedJson;
        } else {
          throw Exception('Unexpected JSON format: ${parsedJson.runtimeType}');
        }

        return _processResponseForUseCase(useCase, jsonData);

      } catch (e) {
        Logger.error('Error in SDK transcription operation', e);
        // Map SDK errors to your existing error handling or rethrow
        if (e is FirebaseException) { // Or a more specific FirebaseAIException if it exists
            throw Exception('Firebase AI SDK error: ${e.message} (Code: ${e.code})');
        }
        rethrow;
      }
    }
    ```
    *   **Important:** The `InlineDataPart` takes `Uint8List`. The SDK handles base64 encoding. The total request limit is 20MB *after* base64 encoding. Inline data increases size by ~33%.
    *   The `responseSchema` will instruct Gemini to return JSON matching that schema. This should simplify your parsing and make `_cleanJsonContent` less necessary here.
    *   Adjust the `DataPart` MIME type if your audio isn't `audio/aac`. Check the supported MIME types in the docs.
    *   Pass parameters like `temperature`, `maxOutputTokens` from your `_llmModelConfig.getAudioTranscriptionConfig` into the `baseGenerationConfig` for the model.

4.  **Keep `_getPromptForUseCase` and `_processResponseForUseCase`:** These should largely remain the same, as the prompts still define *what* to extract, and `_processResponseForUseCase` consumes the (now more reliably structured) JSON.

---

**Phase 3: Refactor `GeminiChatService`**

1.  **Update Constructor:**
    Inject `FirebaseAiProvider`.
    ```dart
    // gemini_chat_service.dart
    // ...
    import 'package:firebase_ai/firebase_ai.dart'; // Add this
    import 'path_to/firebase_ai_provider.dart'; // Add this

    class GeminiChatService {
      final FirebaseAiProvider _firebaseAiProvider; // Changed
      final LlmModelConfig _llmModelConfig;
      final String? _userTier;

      GeminiChatService({
        FirebaseAiProvider? firebaseAiProvider, // Changed
        required LlmModelConfig llmModelConfig,
        String? userTier,
      }) : _firebaseAiProvider = firebaseAiProvider ?? FirebaseAiProvider(), // Changed
           _llmModelConfig = llmModelConfig,
           _userTier = userTier;
      // ...
    ```

2.  **Define Response Schema for Chat (if applicable):**
    Your current `gemini_chat_service.dart` has complex JSON parsing logic (`_cleanJsonContent`, `_extractJsonFieldsManually`). This implies the chat response *itself* is expected to be a JSON string containing a field like `"response"`. If so, define a schema:
    ```dart
    // Inside GeminiChatService or a helper
    Schema _getChatResponseSchema() {
      return Schema.object(
        properties: {
          'response': Schema.string(description: "The chat bot's textual response."),
          'user_prompt': Schema.string(description: "Summary of user's prompts."),
        },
        requiredProperties: ['response', 'user_prompt'],
      );
    }
    ```
    

3.  **Modify `chatWithSystemInstruction` and `chat`:**
    ```dart
    // gemini_chat_service.dart

    // Helper to convert ChatMessage list to Content list
    List<Content> _formatMessagesForSdk(List<ChatMessage> messages) {
      return messages.map((message) {
        final role = message.role.toString() == 'MessageRole.user' ? 'user' : 'model';
        return Content(role, [Part.text(message.content)]);
      }).toList();
    }

    Future<ChatResponse> chatWithSystemInstruction({
      required List<ChatMessage> messages,
      required String systemInstruction,
    }) async {
      try {
        // ... (existing logging and message limiting) ...
        final limitedMessages = messages.length > 25 ? messages.sublist(messages.length - 25) : messages;
        final List<Content> sdkMessages = _formatMessagesForSdk(limitedMessages);

        final modelName = _llmModelConfig.getChatModel(userTier: _userTier);
        final Map<String, dynamic> remoteConfigMap = _llmModelConfig.getChatConfig(userTier: _userTier);
        final baseGenerationConfig = GenerationConfig(
            candidateCount: remoteConfigMap['candidateCount'] as int?,
            maxOutputTokens: remoteConfigMap['maxOutputTokens'] as int?,
            temperature: remoteConfigMap['temperature'] as double?,
            // ... other config from LlmModelConfig ...
        );

        final Schema chatSchema = _getChatResponseSchema();

        final model = _firebaseAiProvider.getChatModel(
          modelName: modelName,
          systemInstructionText: systemInstruction,
          responseSchema: chatSchema, // Pass schema if expecting JSON
          baseGenerationConfig: baseGenerationConfig,
        );

        Logger.debug('Calling Firebase AI Logic SDK for chat with system instruction. Model: $modelName');
        final GenerateContentResponse response = await model.generateContent(sdkMessages);

        if (response.text == null || response.text!.isEmpty) {
          throw Exception('No text in response from Firebase AI Logic SDK for chat');
        }
        
        String responseContent = response.text!;
        Map<String, dynamic>? originalJson;

        originalJson = jsonDecode(responseContent) as Map<String, dynamic>;
        if (originalJson.containsKey('response')) {
            responseContent = originalJson['response'] as String;
        } else {
            Logger.warn("Chat response was JSON but did not contain 'response' key. Using full JSON string.");
        }

        Logger.debug('Firebase AI Logic SDK chat response: $responseContent');
        return ChatResponse.success(content: responseContent, originalJson: originalJson);

      } catch (e) {
        Logger.error('Error in SDK chat request with system instruction', e);
        if (e is FirebaseException) {
             throw Exception('Firebase AI SDK error: ${e.message} (Code: ${e.code})');
        }
        rethrow; // Or map to ChatResponse.failure
      }
    }

    // Modify `chat` method similarly, but without `systemInstructionText`
    Future<ChatResponse> chat(List<ChatMessage> messages) async {
      // Similar logic to chatWithSystemInstruction, but pass null for systemInstructionText
      // ...
      try {
        final limitedMessages = messages.length > 25 ? messages.sublist(messages.length - 25) : messages;
        final List<Content> sdkMessages = _formatMessagesForSdk(limitedMessages);

        final modelName = _llmModelConfig.getChatModel(userTier: _userTier);
        final Map<String, dynamic> remoteConfigMap = _llmModelConfig.getChatConfig(userTier: _userTier);
        final baseGenerationConfig = GenerationConfig( /* ... from LlmModelConfig ... */ );
        final Schema? chatSchema = _getChatResponseSchema();

        final model = _firebaseAiProvider.getChatModel(
          modelName: modelName,
          responseSchema: chatSchema,
          baseGenerationConfig: baseGenerationConfig,
        );

        Logger.debug('Calling Firebase AI Logic SDK for chat. Model: $modelName');
        final GenerateContentResponse response = await model.generateContent(sdkMessages);
        
        // ... (response parsing logic similar to chatWithSystemInstruction) ...
        String responseContent = response.text!;
        Map<String, dynamic>? originalJson;

        if (chatSchema != null && response.text != null) {
            originalJson = jsonDecode(response.text!) as Map<String, dynamic>;
            if (originalJson.containsKey('response')) {
                responseContent = originalJson['response'] as String;
            } else {
                 Logger.warn("Chat response was JSON but did not contain 'response' key. Using full JSON string.");
            }
        }
        return ChatResponse.success(content: responseContent, originalJson: originalJson);

      } catch (e) {
         Logger.error('Error in SDK chat request', e);
         if (e is FirebaseException) {
             throw Exception('Firebase AI SDK error: ${e.message} (Code: ${e.code})');
         }
         rethrow;
      }
    }
    ```
    *   The `_ensureProperEncoding`, `_cleanJsonContent`, and `_extractJsonFieldsManually` methods might become less critical or unnecessary if `responseSchema` works reliably for chat. Test this thoroughly. If the model still sometimes returns malformed JSON, you might need to keep some cleaning logic.
    *   The `generateSuggestedPrompts` method would follow a similar pattern: use `_firebaseAiProvider.getChatModel` (possibly with a specific schema for the suggested prompts if they are JSON) and call `model.generateContent()`.

4.  **Review Role Formatting:**
    The SDK uses `Content('user', [Part.text(...)])` and `Content('model', [Part.text(...)])`. Ensure your `_formatMessagesForSdk` aligns with this.

---

**Phase 4: Error Handling and Retries**

1.  **Update `RetryUtil`:**
    Your `RetryUtil` is generic, which is good. The main change will be the types of exceptions it might catch or that you specifically check for. SDK calls might throw `FirebaseException` or a more specific `FirebaseAIException` (check the `firebase_ai` package docs for specific exception types).
2.  **Adapt `catch` blocks:**
    In `GeminiService` and `GeminiChatService`, change `catch (e)` blocks to handle new exception types if needed for specific error messages. For example:
    ```dart
    // Instead of:
    // throw Exception(response.errorMessage ?? 'Unknown error from Gemini API');

    // Catch specific SDK exceptions:
    // catch (e is FirebaseAIException) { // Fictional specific exception
    //   Logger.error('Firebase AI SDK Error: ${e.code} - ${e.message}');
    //   throw TranscriptionResponse.failure(errorMessage: 'AI Error: ${e.message}');
    // }
    catch (e is FirebaseException) {
      Logger.error('Firebase SDK Error: ${e.code} - ${e.message}');
      // Potentially map to your existing LlmResponse/ChatResponse failures
      throw Exception('Firebase Error: ${e.message}');
    }
    ```

---

**Phase 5: Cleanup**

1.  **Remove `CloudFunctionService`:**
    Once `GeminiService` and `GeminiChatService` no longer use `CloudFunctionService`, you can:
    *   Remove `cloud_function_service.dart` from your project.
    *   Remove the `cloud_functions` dependency from `pubspec.yaml`.
    *   Delete the `callLanguageModelApi` Firebase Cloud Function from your Firebase project.
2.  **Simplify JSON Parsing:**
    If structured output (`responseSchema`) works well, you might be able to remove or significantly simplify `_cleanJsonContent`, `_escapeJsonStringValues`, and `_extractJsonFieldsManually` in `GeminiChatService`.

---

