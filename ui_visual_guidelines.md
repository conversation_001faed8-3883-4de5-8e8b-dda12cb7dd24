# Overall Design Philosophy

Minimalism. Extremely simple.

# Color Palette

Primary color

Secondary color

# Typography

Primary font: Google font Afacad. Used for most text.

Secondary font: Google font Afacad. Used for title, short name, headings.

Noeji app name logo: Google font Pacifico.

# Iconography

Style: Use clean, modern line icons for UI elements (e.g., search, settings, back). Ensure consistency in stroke weight and style. Filled icons can be used for active states if appropriate.

# UI Elements


# Spacing & Layout

White Space: Be generous with spacing to maintain a clean, uncluttered look. Use a consistent spacing unit (e.g., multiples of 8pt/dp) for margins and padding between elements.

Alignment: Use clear alignment (left-alignment for text is generally preferred for readability) to create structure and order. Center-align text sparingly for specific emphasis (like large numbers or titles).

Hierarchy: Use size, weight (typography), color, and placement to guide the user's eye to the most important information and actions.
